import 'package:flutter/foundation.dart';

enum PaymentMethod {
  cash,
  mpesaTill,
  mpesaPaybill,
  mpesaSendMoney;

  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.mpesaTill:
        return 'M-Pesa Till';
      case PaymentMethod.mpesaPaybill:
        return 'M-Pesa Paybill';
      case PaymentMethod.mpesaSendMoney:
        return 'M-Pesa Send Money';
    }
  }

  String get dbValue {
    switch (this) {
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.mpesaTill:
        return 'mpesa_till';
      case PaymentMethod.mpesaPaybill:
        return 'mpesa_paybill';
      case PaymentMethod.mpesaSendMoney:
        return 'mpesa_send_money';
    }
  }

  bool get isMpesa {
    return this != PaymentMethod.cash;
  }

  static PaymentMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'mpesa_till':
        return PaymentMethod.mpesaTill;
      case 'mpesa_paybill':
        return PaymentMethod.mpesaPaybill;
      case 'mpesa_send_money':
        return PaymentMethod.mpesaSendMoney;
      default:
        return PaymentMethod.cash;
    }
  }
}

@immutable
class Payment {
  final String id;
  final String invoiceId;
  final String orderId;
  final double amount;
  final PaymentMethod method;
  final String? receiptNumber;
  final String? transactionCode;
  final String? channelTarget;
  final String? payerPhone;
  final String? notes;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Payment({
    required this.id,
    required this.invoiceId,
    required this.orderId,
    required this.amount,
    required this.method,
    this.receiptNumber,
    this.transactionCode,
    this.channelTarget,
    this.payerPhone,
    this.notes,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'] as String,
      invoiceId: json['invoice_id'] as String,
      orderId: json['order_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      method: PaymentMethod.fromString(json['method'] as String),
      receiptNumber: json['receipt_number'] as String?,
      transactionCode: json['transaction_code'] as String?,
      channelTarget: json['channel_target'] as String?,
      payerPhone: json['payer_phone'] as String?,
      notes: json['notes'] as String?,
      createdBy: json['created_by'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'order_id': orderId,
      'amount': amount,
      'method': method.dbValue,
      'receipt_number': receiptNumber,
      'transaction_code': transactionCode,
      'channel_target': channelTarget,
      'payer_phone': payerPhone,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get displayReference {
    if (method.isMpesa && transactionCode != null) {
      return 'M-Pesa: $transactionCode';
    } else if (receiptNumber != null) {
      return 'Receipt: $receiptNumber';
    }
    return 'No reference';
  }

  String get displayMethod {
    if (method.isMpesa && channelTarget != null) {
      return '${method.displayName} ($channelTarget)';
    }
    return method.displayName;
  }

  Payment copyWith({
    String? id,
    String? invoiceId,
    String? orderId,
    double? amount,
    PaymentMethod? method,
    String? receiptNumber,
    String? transactionCode,
    String? channelTarget,
    String? payerPhone,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      orderId: orderId ?? this.orderId,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      transactionCode: transactionCode ?? this.transactionCode,
      channelTarget: channelTarget ?? this.channelTarget,
      payerPhone: payerPhone ?? this.payerPhone,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
