-- User settings table to store company name and preferred currency
create table if not exists public.user_settings (
  user_id uuid primary key references auth.users(id) on delete cascade,
  company_name text,
  currency_code text,
  updated_at timestamp with time zone default now()
);

alter table public.user_settings enable row level security;

-- RLS: users can manage only their own settings
-- Drop existing policies first to make migration idempotent
DROP POLICY IF EXISTS "Users can view own settings" ON public.user_settings;
DROP POLICY IF EXISTS "Users can upsert own settings" ON public.user_settings;
DROP POLICY IF EXISTS "Users can update own settings" ON public.user_settings;
DROP POLICY IF EXISTS "Users can delete own settings" ON public.user_settings;

-- Optimized with (SELECT auth.uid()) for better performance
create policy "Users can view own settings"
on public.user_settings for select
using ((SELECT auth.uid()) = user_id);

create policy "Users can upsert own settings"
on public.user_settings for insert
to authenticated
with check ((SELECT auth.uid()) = user_id);

create policy "Users can update own settings"
on public.user_settings for update
using ((SELECT auth.uid()) = user_id)
with check ((SELECT auth.uid()) = user_id);

create policy "Users can delete own settings"
on public.user_settings for delete
using ((SELECT auth.uid()) = user_id);