import 'package:flutter/foundation.dart';

@immutable
class StaffProfile {
  final String id; // auth.users id
  final int staffId; // 4-digit staff ID
  final String fullName;
  final String? email;
  final String? phone;
  final String? password;
  final bool emailEnabled;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const StaffProfile({
    required this.id,
    required this.staffId,
    required this.fullName,
    this.email,
    this.phone,
    this.password,
    required this.emailEnabled,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StaffProfile.fromJson(Map<String, dynamic> json) {
    return StaffProfile(
      id: json['id'] as String,
      staffId: json['staff_id'] as int,
      fullName: json['full_name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      password: json['password'] as String?,
      emailEnabled: json['email_enabled'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'staff_id': staffId,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'password': password,
      'email_enabled': emailEnabled,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  StaffProfile copyWith({
    int? staffId,
    String? fullName,
    String? email,
    String? phone,
    String? password,
    bool? emailEnabled,
    bool? isActive,
  }) {
    return StaffProfile(
      id: id,
      staffId: staffId ?? this.staffId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      password: password ?? this.password,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
