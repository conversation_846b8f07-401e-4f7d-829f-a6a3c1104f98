import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/country_model.dart';

class CountryService {
  final SupabaseClient _supabase;

  CountryService({required SupabaseClient supabase}) : _supabase = supabase;

  Future<List<Country>> getAllCountries() async {
    try {
      final response = await _supabase
          .from('countries')
          .select()
          .eq('is_active', true)
          .order('name', ascending: true);

      final countries = (response as List)
          .map((country) => Country.fromJson(country))
          .toList();
      
      // Ensure alphabetical sorting client-side as backup
      countries.sort((a, b) => a.name.compareTo(b.name));
      
      return countries;
    } catch (e) {
      throw 'Error fetching countries: $e';
    }
  }

  Future<List<Country>> searchCountries(String query) async {
    try {
      final response = await _supabase.rpc(
        'search_countries',
        params: {'search_term': query},
      );

      final countries = (response as List)
          .map((country) => Country.fromJson(country))
          .toList();
      
      // Ensure alphabetical sorting
      countries.sort((a, b) => a.name.compareTo(b.name));
      
      return countries;
    } catch (e) {
      throw 'Error searching countries: $e';
    }
  }

  Future<Country?> getCountryByCode(String code) async {
    try {
      final response = await _supabase
          .from('countries')
          .select()
          .eq('code', code.toUpperCase())
          .single();

      return Country.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<Country?> getCountryByName(String name) async {
    try {
      final response = await _supabase
          .from('countries')
          .select()
          .ilike('name', name)
          .single();

      return Country.fromJson(response);
    } catch (e) {
      return null;
    }
  }
}
