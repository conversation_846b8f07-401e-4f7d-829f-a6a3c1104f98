import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/service_model.dart';
import '../../services/service_service.dart';

class ServiceDetailPage extends StatefulWidget {
  final Service? service;
  final bool readOnly;
  const ServiceDetailPage({super.key, this.service, this.readOnly = false});

  @override
  State<ServiceDetailPage> createState() => _ServiceDetailPageState();
}

class _ServiceDetailPageState extends State<ServiceDetailPage> {
  final _formKey = GlobalKey<FormState>();
  final _svc = ServiceService(supabase: Supabase.instance.client);

  late String _name;
  String? _description;
  String _pricingType = 'per_kg';
  double _basePrice = 0;
  double? _pricePerKg;
  double? _pricePerItem;
  int _estimatedHours = 24;
  String? _icon;

  @override
  void initState() {
    super.initState();
    final s = widget.service;
    if (s != null) {
      _name = s.name;
      _description = s.description;
      _pricingType = s.pricingType;
      _basePrice = s.basePrice;
      _pricePerKg = s.pricePerKg;
      _pricePerItem = s.pricePerItem;
      _estimatedHours = s.estimatedHours;
      _icon = s.icon;
    } else {
      _name = '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.service != null && !widget.readOnly;
    final isCreate = widget.service == null && !widget.readOnly;
    final isRead = widget.readOnly;

    return Scaffold(
      appBar: AppBar(
        title: Text(isCreate ? 'Add Service' : isEdit ? 'Edit Service' : 'Service Detail'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: AbsorbPointer(
          absorbing: isRead,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  initialValue: _name,
                  decoration: const InputDecoration(
                    labelText: 'Name *',
                    hintText: 'Enter service name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (v) => (v == null || v.trim().isEmpty) ? 'Service name is required' : null,
                  onChanged: (v) => _name = v.trim(),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  initialValue: _description,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Enter service description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (v) => (v == null || v.trim().isEmpty) ? 'Service description is required' : null,
                  onChanged: (v) => _description = v.trim().isEmpty ? null : v.trim(),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    ChoiceChip(
                      label: const Text('Per KG'),
                      selected: _pricingType == 'per_kg',
                      onSelected: (_) => setState(() => _pricingType = 'per_kg'),
                    ),
                    ChoiceChip(
                      label: const Text('Per Item'),
                      selected: _pricingType == 'per_item',
                      onSelected: (_) => setState(() => _pricingType = 'per_item'),
                    ),
                    ChoiceChip(
                      label: const Text('Fixed'),
                      selected: _pricingType == 'fixed',
                      onSelected: (_) => setState(() => _pricingType = 'fixed'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  key: const ValueKey('base_price'),
                  initialValue: _basePrice == 0 ? '' : _basePrice.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Base Price *',
                    hintText: 'Enter base price',
                    prefixText: '\$ ',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  validator: (v) {
                    if (v == null || v.trim().isEmpty) return 'Base price is required';
                    final price = double.tryParse(v);
                    if (price == null) return 'Please enter a valid price';
                    if (price < 0) return 'Price cannot be negative';
                    return null;
                  },
                  onChanged: (v) => _basePrice = double.tryParse(v) ?? 0,
                ),
                if (_pricingType == 'per_kg') ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    key: const ValueKey('price_per_kg'),
                    initialValue: _pricePerKg?.toString() ?? '',
                    decoration: const InputDecoration(
                      labelText: 'Price per KG *',
                      hintText: 'Enter price per kilogram',
                      prefixText: '\$ ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (v) {
                      if (v == null || v.trim().isEmpty) return 'Price per KG is required';
                      final price = double.tryParse(v);
                      if (price == null) return 'Please enter a valid price';
                      if (price < 0) return 'Price cannot be negative';
                      return null;
                    },
                    onChanged: (v) => _pricePerKg = double.tryParse(v),
                  ),
                ],
                if (_pricingType == 'per_item') ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    key: const ValueKey('price_per_item'),
                    initialValue: _pricePerItem?.toString() ?? '',
                    decoration: const InputDecoration(
                      labelText: 'Price per Item *',
                      hintText: 'Enter price per item',
                      prefixText: '\$ ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (v) {
                      if (v == null || v.trim().isEmpty) return 'Price per item is required';
                      final price = double.tryParse(v);
                      if (price == null) return 'Please enter a valid price';
                      if (price < 0) return 'Price cannot be negative';
                      return null;
                    },
                    onChanged: (v) => _pricePerItem = double.tryParse(v),
                  ),
                ],
                const SizedBox(height: 16),
                TextFormField(
                  key: const ValueKey('estimated_hours'),
                  initialValue: _estimatedHours.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Estimated Hours',
                    hintText: 'Enter estimated completion time',
                    suffixText: 'hours',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (v) {
                    if (v == null || v.trim().isEmpty) return null; // Optional field
                    final hours = int.tryParse(v);
                    if (hours == null) return 'Please enter a valid number';
                    if (hours <= 0) return 'Hours must be greater than 0';
                    return null;
                  },
                  onChanged: (v) => _estimatedHours = int.tryParse(v) ?? 24,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  key: const ValueKey('icon'),
                  initialValue: _icon ?? '',
                  decoration: const InputDecoration(
                    labelText: 'Icon (Material icon name)',
                    hintText: 'e.g., local_laundry_service, dry_cleaning',
                    border: OutlineInputBorder(),
                    helperText: 'Optional: Material icon name for the service',
                  ),
                  onChanged: (v) => _icon = v.trim().isEmpty ? null : v.trim(),
                ),
                // Active toggle removed from edit UI
                const SizedBox(height: 16),
                if (!isRead)
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          label: const Text('Cancel'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _save,
                          icon: const Icon(Icons.save),
                          label: const Text('Save'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            elevation: 0,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors above'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    try {
      if (widget.service == null) {
        final created = await _svc.createRaw(
          name: _name,
          description: _description,
          basePrice: _basePrice,
          pricePerKg: _pricePerKg,
          pricePerItem: _pricePerItem,
          pricingType: _pricingType,
          estimatedHours: _estimatedHours,
          isActive: true,
          icon: _icon,
        );
        if (!mounted) return;
        Navigator.pop(context, {'ok': true, 'action': 'created', 'service': created});
      } else {
        final updated = await _svc.update(widget.service!.id, {
          'name': _name,
          'description': _description,
          'base_price': _basePrice,
          'price_per_kg': _pricePerKg,
          'price_per_item': _pricePerItem,
          'pricing_type': _pricingType,
          'estimated_hours': _estimatedHours,
          // is_active not editable here
          'icon': _icon,
        });
        if (!mounted) return;
        Navigator.pop(context, {'ok': true, 'action': 'updated', 'service': updated});
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Save failed: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
