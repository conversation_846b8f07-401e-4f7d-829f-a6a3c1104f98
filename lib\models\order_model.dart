import 'package:flutter/foundation.dart';
import 'store_model.dart';
import 'service_model.dart';
import 'garment_model.dart';
import 'user_address_model.dart';

enum OrderStatus {
  pending,
  accepted,
  pickedUp,
  inProcess,
  readyForPickup,
  outForDelivery,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.accepted:
        return 'Accepted';
      case OrderStatus.pickedUp:
        return 'Order Collected';  // When customer drops off laundry
      case OrderStatus.inProcess:
        return 'In Process';
      case OrderStatus.readyForPickup:
        return 'Ready for Pickup';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.completed:
        return 'Completed';  // Final status - customer has received laundry
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  static OrderStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'accepted':
        return OrderStatus.accepted;
      case 'picked_up':
        return OrderStatus.pickedUp;
      case 'in_process':
        return OrderStatus.inProcess;
      case 'ready_for_pickup':
        return OrderStatus.readyForPickup;
      case 'out_for_delivery':
        return OrderStatus.outForDelivery;
      case 'completed':
        return OrderStatus.completed;
      case 'cancelled':
        return OrderStatus.cancelled;
      // Legacy support for old 'delivered' status
      case 'delivered':
        return OrderStatus.completed;  // Map old delivered to completed
      default:
        return OrderStatus.pending;
    }
  }

  String toDbString() {
    switch (this) {
      case OrderStatus.pending:
        return 'pending';
      case OrderStatus.accepted:
        return 'accepted';
      case OrderStatus.pickedUp:
        return 'picked_up';
      case OrderStatus.inProcess:
        return 'in_process';
      case OrderStatus.readyForPickup:
        return 'ready_for_pickup';
      case OrderStatus.outForDelivery:
        return 'out_for_delivery';
      case OrderStatus.completed:
        return 'completed';
      case OrderStatus.cancelled:
        return 'cancelled';
    }
  }
}

@immutable
class Order {
  final String id;
  final String orderNumber;
  final String userId;
  final String? storeId;
  final String serviceId;
  final OrderStatus status;
  
  // Related objects (populated when joining)
  final Store? store;
  final Service? service;
  final List<OrderItem>? items;
  
  // Address information
  final String? pickupAddressId;
  final String? deliveryAddressId;
  final UserAddress? pickupAddress;
  final UserAddress? deliveryAddress;
  
  // Time slots
  final DateTime? pickupDate;
  final String? pickupTimeSlot;
  final DateTime? deliveryDate;
  final String? deliveryTimeSlot;
  
  // Pricing
  final double totalAmount;
  final double taxAmount;
  final double discountAmount;
  
  // Payment options
  final bool payOnDelivery;
  
  // Special instructions
  final String? specialInstructions;
  
  // Assignment and cancellation
  final String? assignedAgentId;
  final String? cancelledBy;
  final String? cancelReason;
  final DateTime? cancelledAt;
  
  // Timestamps
  final DateTime createdAt;
  final DateTime updatedAt;

  const Order({
    required this.id,
    required this.orderNumber,
    required this.userId,
    this.storeId,
    required this.serviceId,
    required this.status,
    this.store,
    this.service,
    this.items,
    this.pickupAddressId,
    this.deliveryAddressId,
    this.pickupAddress,
    this.deliveryAddress,
    this.pickupDate,
    this.pickupTimeSlot,
    this.deliveryDate,
    this.deliveryTimeSlot,
    required this.totalAmount,
    required this.taxAmount,
    required this.discountAmount,
    this.payOnDelivery = false,
    this.specialInstructions,
    this.assignedAgentId,
    this.cancelledBy,
    this.cancelReason,
    this.cancelledAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String,
      userId: json['user_id'] as String,
      storeId: json['store_id'] as String?,
      serviceId: json['service_id'] as String,
      status: OrderStatus.fromString(json['status'] as String),
      store: json['store'] != null ? Store.fromJson(json['store'] as Map<String, dynamic>) : null,
      service: json['service'] != null ? Service.fromJson(json['service'] as Map<String, dynamic>) : null,
      items: json['items'] != null 
          ? (json['items'] as List).map((item) => OrderItem.fromJson(item as Map<String, dynamic>)).toList()
          : null,
      pickupAddressId: json['pickup_address_id'] as String?,
      deliveryAddressId: json['delivery_address_id'] as String?,
      pickupAddress: json['pickup_address'] != null 
          ? UserAddress.fromJson(json['pickup_address'] as Map<String, dynamic>) 
          : null,
      deliveryAddress: json['delivery_address'] != null 
          ? UserAddress.fromJson(json['delivery_address'] as Map<String, dynamic>) 
          : null,
      pickupDate: json['pickup_date'] != null ? DateTime.parse(json['pickup_date'] as String) : null,
      pickupTimeSlot: json['pickup_time_slot'] as String?,
      deliveryDate: json['delivery_date'] != null ? DateTime.parse(json['delivery_date'] as String) : null,
      deliveryTimeSlot: json['delivery_time_slot'] as String?,
      totalAmount: (json['total_amount'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      payOnDelivery: json['pay_on_delivery'] as bool? ?? false,
      specialInstructions: json['special_instructions'] as String?,
      assignedAgentId: json['assigned_agent_id'] as String?,
      cancelledBy: json['cancelled_by'] as String?,
      cancelReason: json['cancel_reason'] as String?,
      cancelledAt: json['cancelled_at'] != null ? DateTime.parse(json['cancelled_at'] as String) : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'user_id': userId,
      'store_id': storeId,
      'service_id': serviceId,
      'status': status.toDbString(),
      'pickup_address_id': pickupAddressId,
      'delivery_address_id': deliveryAddressId,
      'pickup_date': pickupDate?.toIso8601String().split('T')[0],
      'pickup_time_slot': pickupTimeSlot,
      'delivery_date': deliveryDate?.toIso8601String().split('T')[0],
      'delivery_time_slot': deliveryTimeSlot,
      'total_amount': totalAmount,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'pay_on_delivery': payOnDelivery,
      'special_instructions': specialInstructions,
      'assigned_agent_id': assignedAgentId,
      'cancelled_by': cancelledBy,
      'cancel_reason': cancelReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get canCancel {
    return status == OrderStatus.pending || status == OrderStatus.accepted;
  }

  bool get isActive {
    return status != OrderStatus.completed && status != OrderStatus.cancelled;
  }

  String get statusIcon {
    switch (status) {
      case OrderStatus.pending:
        return '⏳';
      case OrderStatus.accepted:
        return '✅';
      case OrderStatus.pickedUp:
        return '📥';  // Order collected from customer
      case OrderStatus.inProcess:
        return '🔄';
      case OrderStatus.readyForPickup:
        return '✨';  // Ready for customer pickup/delivery
      case OrderStatus.outForDelivery:
        return '🚚';  // Out for delivery
      case OrderStatus.completed:
        return '📦';  // Complete - customer has received laundry
      case OrderStatus.cancelled:
        return '❌';
    }
  }

  int get totalItems {
    return items?.fold<int>(0, (sum, item) => sum + item.quantity) ?? 0;
  }

  double get subtotal {
    return items?.fold<double>(0.0, (sum, item) => sum + item.totalPrice) ?? totalAmount - taxAmount + discountAmount;
  }

  Order copyWith({
    String? id,
    String? orderNumber,
    String? userId,
    String? storeId,
    String? serviceId,
    OrderStatus? status,
    Store? store,
    Service? service,
    List<OrderItem>? items,
    String? pickupAddressId,
    String? deliveryAddressId,
    UserAddress? pickupAddress,
    UserAddress? deliveryAddress,
    DateTime? pickupDate,
    String? pickupTimeSlot,
    DateTime? deliveryDate,
    String? deliveryTimeSlot,
    double? totalAmount,
    double? taxAmount,
    double? discountAmount,
    String? specialInstructions,
    String? assignedAgentId,
    String? cancelledBy,
    String? cancelReason,
    DateTime? cancelledAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      userId: userId ?? this.userId,
      storeId: storeId ?? this.storeId,
      serviceId: serviceId ?? this.serviceId,
      status: status ?? this.status,
      store: store ?? this.store,
      service: service ?? this.service,
      items: items ?? this.items,
      pickupAddressId: pickupAddressId ?? this.pickupAddressId,
      deliveryAddressId: deliveryAddressId ?? this.deliveryAddressId,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      pickupDate: pickupDate ?? this.pickupDate,
      pickupTimeSlot: pickupTimeSlot ?? this.pickupTimeSlot,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      deliveryTimeSlot: deliveryTimeSlot ?? this.deliveryTimeSlot,
      totalAmount: totalAmount ?? this.totalAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      assignedAgentId: assignedAgentId ?? this.assignedAgentId,
      cancelledBy: cancelledBy ?? this.cancelledBy,
      cancelReason: cancelReason ?? this.cancelReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => '$orderNumber - ${status.displayName}';
}


