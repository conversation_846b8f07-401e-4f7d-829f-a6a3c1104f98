import 'package:flutter/material.dart';
import 'package:email_validator/email_validator.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import '../../widgets/country_picker_dialog.dart';
import '../../models/country_model.dart';

class CustomerRegistrationPage extends StatefulWidget {
  const CustomerRegistrationPage({super.key});

  @override
  State<CustomerRegistrationPage> createState() => _CustomerRegistrationPageState();
}

class _CustomerRegistrationPageState extends State<CustomerRegistrationPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameCtrl = TextEditingController();
  final _emailCtrl = TextEditingController();
  final _phoneCtrl = TextEditingController();
  final _countryCtrl = TextEditingController();
  final _notesCtrl = TextEditingController();

  // address (optional)
  final _addrTitleCtrl = TextEditingController(text: 'Home');
  final _addrLine1Ctrl = TextEditingController();
  final _addrLine2Ctrl = TextEditingController();
  final _addrCityCtrl = TextEditingController();
  final _addrStateCtrl = TextEditingController();
  final _addrPostalCtrl = TextEditingController();
  final _addrCountryCtrl = TextEditingController();

  final CustomerService _service = CustomerService(supabase: Supabase.instance.client);

  bool _emailEnabled = true; // toggle: email optional
  bool _addAddress = false;  // toggle: add first address
  bool _isChecking = false;
  bool? _emailAvailable;
  bool _isSaving = false;

  Country? _selectedCountry;
  Country? _selectedAddrCountry;

  @override
  void dispose() {
    _nameCtrl.dispose();
    _emailCtrl.dispose();
    _phoneCtrl.dispose();
    _countryCtrl.dispose();
    _notesCtrl.dispose();
    _addrTitleCtrl.dispose();
    _addrLine1Ctrl.dispose();
    _addrLine2Ctrl.dispose();
    _addrCityCtrl.dispose();
    _addrStateCtrl.dispose();
    _addrPostalCtrl.dispose();
    _addrCountryCtrl.dispose();
    super.dispose();
  }

  Future<void> _checkEmail() async {
    final email = _emailCtrl.text.trim();
    if (email.isEmpty || !EmailValidator.validate(email)) {
      setState(() => _emailAvailable = null);
      return;
    }
    setState(() => _isChecking = true);
    final available = await _service.isEmailAvailable(email);
    if (!mounted) return;
    setState(() {
      _emailAvailable = available;
      _isChecking = false;
    });
  }

  Future<void> _pickCountry() async {
    await showCountryPicker(
      context: context,
      selectedCountry: _selectedCountry,
      onSelect: (Country c) {
        setState(() {
          _selectedCountry = c;
          _countryCtrl.text = c.name;
        });
      },
    );
  }

  Future<void> _pickAddrCountry() async {
    await showCountryPicker(
      context: context,
      selectedCountry: _selectedAddrCountry,
      onSelect: (Country c) {
        setState(() {
          _selectedAddrCountry = c;
          _addrCountryCtrl.text = c.name;
        });
      },
    );
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;
    if (_emailEnabled && _emailCtrl.text.trim().isNotEmpty && _emailAvailable == false) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Email already in use')));
      return;
    }
    setState(() => _isSaving = true);
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      final customer = Customer(
        fullName: _nameCtrl.text.trim(),
        email: _emailEnabled ? (_emailCtrl.text.trim().isEmpty ? null : _emailCtrl.text.trim()) : null,
        phone: _phoneCtrl.text.trim().isEmpty ? null : _phoneCtrl.text.trim(),
        country: _countryCtrl.text.trim().isEmpty ? null : _countryCtrl.text.trim(),
        notes: _notesCtrl.text.trim().isEmpty ? null : _notesCtrl.text.trim(),
        createdBy: userId,
      );

      final addresses = <CustomerAddress>[];
      if (_addAddress) {
        if (_addrLine1Ctrl.text.trim().isEmpty || _addrCityCtrl.text.trim().isEmpty || _addrCountryCtrl.text.trim().isEmpty) {
          setState(() => _isSaving = false);
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Fill address line 1, city and country')));
          return;
        }
        addresses.add(CustomerAddress(
          title: _addrTitleCtrl.text.trim().isEmpty ? 'Home' : _addrTitleCtrl.text.trim(),
          addressLine1: _addrLine1Ctrl.text.trim(),
          addressLine2: _addrLine2Ctrl.text.trim().isEmpty ? null : _addrLine2Ctrl.text.trim(),
          city: _addrCityCtrl.text.trim(),
          state: _addrStateCtrl.text.trim().isEmpty ? null : _addrStateCtrl.text.trim(),
          postalCode: _addrPostalCtrl.text.trim().isEmpty ? null : _addrPostalCtrl.text.trim(),
          country: _addrCountryCtrl.text.trim(),
          isDefault: true,
          isActive: true,
        ));
      }

      final created = await _service.createCustomer(customer, addresses: addresses);
      if (!mounted) return;
      setState(() => _isSaving = false);
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Customer created')));
      Navigator.of(context).pop(created);
    } catch (e) {
      if (!mounted) return;
      setState(() => _isSaving = false);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register Customer')),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Basic Info', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _nameCtrl,
                  textCapitalization: TextCapitalization.words,
                  decoration: const InputDecoration(
                    labelText: 'Full name *',
                    prefixIcon: Icon(Icons.person_outline),
                  ),
                  validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('Provide email'),
                  value: _emailEnabled,
                  onChanged: (v) {
                    setState(() {
                      _emailEnabled = v;
                      if (!v) {
                        _emailCtrl.clear();
                        _emailAvailable = null;
                      }
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                if (_emailEnabled) ...[
                  TextFormField(
                    controller: _emailCtrl,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      prefixIcon: const Icon(Icons.alternate_email),
                      helperText: 'Optional. We\'ll check if it\'s already used.',
                      suffixIcon: _isChecking
                          ? const Padding(
                              padding: EdgeInsets.all(12),
                              child: SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
                            )
                          : (_emailAvailable == null
                              ? IconButton(icon: const Icon(Icons.search), onPressed: _checkEmail)
                              : (_emailAvailable == true
                                  ? const Icon(Icons.check_circle, color: Colors.green)
                                  : const Icon(Icons.error, color: Colors.red))),
                    ),
                    onChanged: (_) => setState(() => _emailAvailable = null),
                    validator: (v) {
                      if (v == null || v.trim().isEmpty) return null;
                      if (!EmailValidator.validate(v.trim())) return 'Invalid email';
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      ElevatedButton.icon(onPressed: _checkEmail, icon: const Icon(Icons.search), label: const Text('Check availability')),
                      const SizedBox(width: 12),
                      if (_emailAvailable == true) const Text('Available', style: TextStyle(color: Colors.green)),
                      if (_emailAvailable == false) const Text('Already in use', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ],
                const SizedBox(height: 12),
                TextFormField(
                  controller: _phoneCtrl,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    labelText: 'Phone',
                    prefixIcon: Icon(Icons.phone_outlined),
                  ),
                ),
                const SizedBox(height: 12),
                GestureDetector(
                  onTap: _pickCountry,
                  child: AbsorbPointer(
                    child: TextFormField(
                      controller: _countryCtrl,
                      decoration: const InputDecoration(
                        labelText: 'Country',
                        prefixIcon: Icon(Icons.flag_outlined),
                        suffixIcon: Icon(Icons.keyboard_arrow_down),
                        hintText: 'Select country',
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextFormField(
                  controller: _notesCtrl,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    prefixIcon: Icon(Icons.notes_outlined),
                  ),
                  minLines: 1,
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('Add default address now'),
                  value: _addAddress,
                  onChanged: (v) => setState(() => _addAddress = v),
                  contentPadding: EdgeInsets.zero,
                ),
                if (_addAddress) ...[
                  const Divider(height: 24),
                  const Text('Address', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _addrTitleCtrl,
                    decoration: const InputDecoration(labelText: 'Title (Home/Office)', prefixIcon: Icon(Icons.home_outlined)),
                  ),
                  const SizedBox(height: 12),
                  // Start with Country row (Country then State)
                  Row(children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: _pickAddrCountry,
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: _addrCountryCtrl,
                            decoration: const InputDecoration(
                              labelText: 'Country *',
                              prefixIcon: Icon(Icons.flag),
                              suffixIcon: Icon(Icons.keyboard_arrow_down),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _addrStateCtrl,
                        decoration: const InputDecoration(labelText: 'State', prefixIcon: Icon(Icons.map_outlined)),
                      ),
                    ),
                  ]),
                  const SizedBox(height: 12),
                  // Then City and Postal code
                  Row(children: [
                    Expanded(
                      child: TextFormField(
                        controller: _addrCityCtrl,
                        decoration: const InputDecoration(labelText: 'City *', prefixIcon: Icon(Icons.location_city)),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _addrPostalCtrl,
                        decoration: const InputDecoration(labelText: 'Postal code', prefixIcon: Icon(Icons.local_post_office_outlined)),
                      ),
                    ),
                  ]),
                  const SizedBox(height: 12),
                  // Then address lines
                  TextFormField(
                    controller: _addrLine1Ctrl,
                    decoration: const InputDecoration(labelText: 'Address line 1 *', prefixIcon: Icon(Icons.location_on_outlined)),
                  ),
                  const SizedBox(height: 12),
                  TextFormField(
                    controller: _addrLine2Ctrl,
                    decoration: const InputDecoration(labelText: 'Address line 2', prefixIcon: Icon(Icons.location_on)),
                  ),
                ],
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isSaving ? null : _save,
                    icon: const Icon(Icons.person_add),
                    label: Text(_isSaving ? 'Saving...' : 'Create Customer'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}



