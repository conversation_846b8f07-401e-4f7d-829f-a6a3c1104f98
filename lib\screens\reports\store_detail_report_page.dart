import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/currency_service.dart' as currency_service;

class StoreDetailReportPage extends StatefulWidget {
  final String storeId;
  final String storeName;
  final DateTime initialStart;
  final DateTime initialEnd;

  const StoreDetailReportPage({
    super.key,
    required this.storeId,
    required this.storeName,
    required this.initialStart,
    required this.initialEnd,
  });

  @override
  State<StoreDetailReportPage> createState() => _StoreDetailReportPageState();
}

class _StoreDetailReportPageState extends State<StoreDetailReportPage> {
  final _supabase = Supabase.instance.client;
  final currency_service.AppCurrencyService _currency = currency_service.AppCurrencyService();

  bool _isLoading = true;
  String _currencySymbol = '';
  late DateTime _start;
  late DateTime _end;

  // Metrics
  double _revenue = 0.0;
  int _ordersCount = 0;
  double _avgOrder = 0.0;
  int _paidOrders = 0;
  int _unpaidInvoices = 0;

  // Daily sales (date -> total)
  List<MapEntry<DateTime, double>> _daily = [];

  @override
  void initState() {
    super.initState();
    _start = widget.initialStart;
    _end = widget.initialEnd;
    _init();
  }

  Future<void> _init() async {
    setState(() => _isLoading = true);
    _currencySymbol = await _currency.getCurrencySymbol();
    await _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // 1) Orders for store in range
      var ordersQuery = _supabase
          .from('orders')
          .select('id,total_amount,created_at')
          .eq('store_id', widget.storeId)
          .gte('created_at', _start.toIso8601String())
          .lte('created_at', _end.toIso8601String());
      final ordersRows = await ordersQuery as List<dynamic>;
      final orderIds = ordersRows.map((e) => e['id'] as String).toList();
      final double ordersTotalAmount = ordersRows.fold<double>(0.0, (s, e) => s + ((e['total_amount'] as num).toDouble()));
      final int ordersCount = ordersRows.length;

      // 2) Payments for those orders in range
      double revenue = 0.0;
      final Map<String, double> byDay = {};
      if (orderIds.isNotEmpty) {
        // fetch payments in chunks if needed
        const int chunk = 100;
        for (int i = 0; i < orderIds.length; i += chunk) {
          final sub = orderIds.sublist(i, i + chunk > orderIds.length ? orderIds.length : i + chunk);
          var payQuery = _supabase
              .from('payments')
              .select('amount, created_at, order_id')
              .inFilter('order_id', sub)
              .gte('created_at', _start.toIso8601String())
              .lte('created_at', _end.toIso8601String());
          final pays = await payQuery as List<dynamic>;
          for (final p in pays) {
            final amt = (p['amount'] as num).toDouble();
            revenue += amt;
            final dt = DateTime.parse(p['created_at'] as String);
            final key = DateTime(dt.year, dt.month, dt.day).toIso8601String();
            byDay.update(key, (v) => v + amt, ifAbsent: () => amt);
          }
        }
      }

      // 3) Invoices status for those orders
      int paid = 0;
      int unpaid = 0;
      if (orderIds.isNotEmpty) {
        const int chunk = 100;
        for (int i = 0; i < orderIds.length; i += chunk) {
          final sub = orderIds.sublist(i, i + chunk > orderIds.length ? orderIds.length : i + chunk);
          final invs = await _supabase
              .from('invoices')
              .select('order_id, payment_status')
              .inFilter('order_id', sub);
          for (final inv in invs as List<dynamic>) {
            final status = inv['payment_status'] as String?;
            if (status == 'paid') {
              paid++;
            } else if (status == 'pending' || status == 'partial') {
              unpaid++;
            }
          }
        }
      }

      // Average order value: prefer orders total if available, else revenue/orders
      final double avg = ordersCount == 0
          ? 0.0
          : (ordersTotalAmount > 0 ? ordersTotalAmount / ordersCount : (revenue / ordersCount));

      // Prepare daily list
      final entries = byDay.entries
          .map((e) => MapEntry(DateTime.parse(e.key), e.value))
          .toList()
        ..sort((a, b) => a.key.compareTo(b.key));

      if (!mounted) return;
      setState(() {
        _revenue = revenue;
        _ordersCount = ordersCount;
        _avgOrder = avg;
        _paidOrders = paid;
        _unpaidInvoices = unpaid;
        _daily = entries;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load store details: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String two(int v) => v.toString().padLeft(2, '0');
    String fmt(DateTime d) => '${two(d.day)}/${two(d.month)}/${d.year}';

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.storeName),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadData, icon: const Icon(Icons.refresh))],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _start,
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() => _start = DateTime(picked.year, picked.month, picked.day));
                        await _loadData();
                      }
                    },
                    icon: const Icon(Icons.date_range),
                    label: Text('Start: ${fmt(_start)}'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _end,
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() => _end = DateTime(picked.year, picked.month, picked.day, 23, 59, 59, 999));
                        await _loadData();
                      }
                    },
                    icon: const Icon(Icons.event_available),
                    label: Text('End: ${fmt(_end)}'),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildKpiGrid(),
                        const SizedBox(height: 12),
                        _buildDailyList(),
                      ],
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget _buildKpiGrid() {
    String money(double x) => _currencySymbol.isEmpty ? x.toStringAsFixed(2) : '$_currencySymbol${x.toStringAsFixed(2)}';
    final items = [
      _Kpi('Revenue', money(_revenue), Icons.payments, Colors.teal),
      _Kpi('Orders', _ordersCount.toString(), Icons.receipt_long, Colors.indigo),
      _Kpi('Avg Order', money(_avgOrder), Icons.trending_up, Colors.orange),
      _Kpi('Paid Orders', _paidOrders.toString(), Icons.verified, Colors.green),
      _Kpi('Unpaid Invoices', _unpaidInvoices.toString(), Icons.warning_amber_rounded, Colors.redAccent),
    ];
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.4,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      children: [
        for (final k in items)
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: k.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(k.icon, color: k.color, size: 18),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(k.title, maxLines: 1, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.w700)),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Text(k.value, style: TextStyle(color: k.color, fontWeight: FontWeight.w800, fontSize: 16)),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDailyList() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Sales by Day', style: TextStyle(fontWeight: FontWeight.w800)),
            const SizedBox(height: 8),
            for (final e in _daily)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: Row(
                  children: [
                    Expanded(child: Text('${e.key.year}-${e.key.month.toString().padLeft(2, '0')}-${e.key.day.toString().padLeft(2, '0')}')),
                    Text(_currencySymbol.isEmpty ? e.value.toStringAsFixed(2) : '$_currencySymbol${e.value.toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.w700)),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _Kpi {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  _Kpi(this.title, this.value, this.icon, this.color);
}


