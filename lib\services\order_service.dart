import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/order_model.dart';
import '../models/service_model.dart';
import '../models/garment_model.dart';
import '../models/user_address_model.dart';
import 'currency_service.dart' as currency_service;

class OrderService {
  final SupabaseClient _supabase;
  final currency_service.AppCurrencyService _currencyService;

  OrderService({required SupabaseClient supabase})
    : _supabase = supabase,
      _currencyService = currency_service.AppCurrencyService();

  // ============================================
  // CURRENCY FORMATTING METHODS
  // ============================================

  /// Format amount with user's selected currency
  Future<String> formatAmount(double amount) async {
    return await _currencyService.formatAmount(amount);
  }

  /// Get the user's selected currency symbol
  Future<String> getCurrencySymbol() async {
    return await _currencyService.getCurrencySymbol();
  }

  /// Get the user's selected currency code
  Future<String> getCurrencyCode() async {
    return await _currencyService.getCurrencyCode();
  }

  /// Format order total amount with currency
  Future<String> formatOrderTotal(Order order) async {
    return await formatAmount(order.totalAmount);
  }

  /// Format service price with currency
  Future<String> formatServicePrice(Service service) async {
    return await formatAmount(service.basePrice);
  }

  /// Format multiple amounts for order breakdown
  Future<Map<String, String>> formatOrderBreakdown(Order order) async {
    return {
      'subtotal': await formatAmount(order.subtotal),
      'tax': await formatAmount(order.taxAmount),
      'discount': await formatAmount(order.discountAmount),
      'total': await formatAmount(order.totalAmount),
    };
  }

  // ============================================
  // SERVICES OPERATIONS
  // ============================================

  Future<List<Service>> getAllServices() async {
    try {
      final response = await _supabase
          .from('services')
          .select()
          .eq('is_active', true)
          .order('name');

      return (response as List)
          .map((service) => Service.fromJson(service))
          .toList();
    } catch (e) {
      throw 'Error fetching services: $e';
    }
  }

  Future<Service?> getServiceById(String serviceId) async {
    try {
      final response = await _supabase
          .from('services')
          .select()
          .eq('id', serviceId)
          .single();

      return Service.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<Service> createService({
    required String name,
    String? description,
    double basePrice = 0.0,
    double? pricePerKg,
    double? pricePerItem,
    required String pricingType, // 'per_kg', 'per_item', 'fixed'
    int estimatedHours = 24,
    bool isActive = true,
    String? icon,
  }) async {
    try {
      final insert = {
        'name': name,
        'description': description,
        'base_price': basePrice,
        'price_per_kg': pricePerKg,
        'price_per_item': pricePerItem,
        'pricing_type': pricingType,
        'estimated_hours': estimatedHours,
        'is_active': isActive,
        'icon': icon,
      };

      final response = await _supabase
          .from('services')
          .insert(insert)
          .select()
          .single();

      return Service.fromJson(response);
    } catch (e) {
      throw 'Error creating service: $e';
    }
  }

  // ============================================
  // GARMENTS OPERATIONS
  // ============================================

  Future<List<Garment>> getAllGarments() async {
    try {
      final response = await _supabase
          .from('garments')
          .select()
          .eq('is_active', true)
          .order('category, name');

      return (response as List)
          .map((garment) => Garment.fromJson(garment))
          .toList();
    } catch (e) {
      throw 'Error fetching garments: $e';
    }
  }

  Future<Map<String, List<Garment>>> getGarmentsByCategory() async {
    try {
      final garments = await getAllGarments();
      final Map<String, List<Garment>> categorizedGarments = {};

      for (final garment in garments) {
        if (!categorizedGarments.containsKey(garment.category)) {
          categorizedGarments[garment.category] = [];
        }
        categorizedGarments[garment.category]!.add(garment);
      }

      return categorizedGarments;
    } catch (e) {
      throw 'Error categorizing garments: $e';
    }
  }

  // ============================================
  // USER ADDRESSES OPERATIONS
  // ============================================

  Future<List<UserAddress>> getUserAddresses(String userId) async {
    try {
      final response = await _supabase
          .from('user_addresses')
          .select()
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);

      return (response as List)
          .map((address) => UserAddress.fromJson(address))
          .toList();
    } catch (e) {
      throw 'Error fetching user addresses: $e';
    }
  }

  Future<UserAddress> createUserAddress(UserAddress address) async {
    try {
      final response = await _supabase
          .from('user_addresses')
          .insert(address.toJson())
          .select()
          .single();

      return UserAddress.fromJson(response);
    } catch (e) {
      throw 'Error creating address: $e';
    }
  }

  Future<UserAddress> updateUserAddress(UserAddress address) async {
    try {
      final response = await _supabase
          .from('user_addresses')
          .update(address.toJson())
          .eq('id', address.id)
          .select()
          .single();

      return UserAddress.fromJson(response);
    } catch (e) {
      throw 'Error updating address: $e';
    }
  }

  Future<void> deleteUserAddress(String addressId) async {
    try {
      await _supabase.from('user_addresses').delete().eq('id', addressId);
    } catch (e) {
      throw 'Error deleting address: $e';
    }
  }

  Future<void> setDefaultAddress(String userId, String addressId) async {
    try {
      // First, remove default from all addresses
      await _supabase
          .from('user_addresses')
          .update({'is_default': false})
          .eq('user_id', userId);

      // Then set the selected address as default
      await _supabase
          .from('user_addresses')
          .update({'is_default': true})
          .eq('id', addressId);
    } catch (e) {
      throw 'Error setting default address: $e';
    }
  }

  // ============================================
  // ORDERS OPERATIONS
  // ============================================

  Future<Order> createOrder({
    required String userId,
    required String storeId,
    required String serviceId,
    required List<OrderItem> items,
    String? pickupAddressId,
    String? deliveryAddressId,
    DateTime? pickupDate,
    String? pickupTimeSlot,
    DateTime? deliveryDate,
    String? deliveryTimeSlot,
    String? specialInstructions,
    double discountAmount = 0.0,
  }) async {
    try {
      // Calculate totals
      final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
      final taxAmount = subtotal * 0.1; // 10% tax
      const deliveryFee = 0.0; // Can be calculated based on distance or service
      final totalAmount = subtotal + taxAmount + deliveryFee - discountAmount;

      // Create order only (no invoice yet - invoice will be created when payment is received)
      final response = await _supabase.rpc(
        'create_order_only',
        params: {
          'p_user_id': userId,
          'p_store_id': storeId,
          'p_service_id': serviceId,
          'p_pickup_address_id': pickupAddressId,
          'p_delivery_address_id': deliveryAddressId,
          'p_pickup_date': pickupDate?.toIso8601String().split('T')[0],
          'p_pickup_time_slot': pickupTimeSlot,
          'p_delivery_date': deliveryDate?.toIso8601String().split('T')[0],
          'p_delivery_time_slot': deliveryTimeSlot,
          'p_subtotal': subtotal,
          'p_tax_amount': taxAmount,
          'p_delivery_fee': deliveryFee,
          'p_discount_amount': discountAmount,
          'p_total_amount': totalAmount,
          'p_special_instructions': specialInstructions,
        },
      );

      final orderId = response.first['order_id'] as String;
      final orderNumber = response.first['order_number'] as String;

      // Create order items
      final itemsData = items
          .map(
            (item) => {
              'order_id': orderId,
              'garment_id': item.garmentId,
              'quantity': item.quantity,
              'unit_price': item.unitPrice,
              'total_price': item.totalPrice,
              'notes': item.notes,
            },
          )
          .toList();

      await _supabase.from('order_items').insert(itemsData);

      // Log status change
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': null,
        'new_status': 'pending',
        'changed_by': userId,
        'notes': 'Order $orderNumber created - no invoice yet',
      });

      return await getOrderById(orderId);
    } catch (e) {
      // Enhanced error handling for better debugging
      if (e.toString().contains('PostgrestException')) {
        if (e.toString().contains('ambiguous')) {
          throw 'Database error: Column reference issue. Please contact support.';
        } else if (e.toString().contains('order_number')) {
          throw 'Error generating order number. Please try again.';
        } else if (e.toString().contains('foreign key')) {
          throw 'Invalid data reference. Please refresh and try again.';
        } else if (e.toString().contains('duplicate key')) {
          throw 'Duplicate order detected. Please refresh and try again.';
        } else {
          throw 'Database error occurred. Details: ${e.toString()}';
        }
      } else {
        throw 'Error creating order: $e';
      }
    }
  }

  /// Create invoice for an existing order when payment is received
  Future<Map<String, String>> createInvoiceForOrder(
    String orderId, {
    String? paymentMethod,
  }) async {
    try {
      final response = await _supabase.rpc(
        'create_invoice_for_order',
        params: {'p_order_id': orderId, 'p_payment_method': paymentMethod},
      );

      return {
        'invoice_id': response.first['invoice_id'] as String,
        'invoice_number': response.first['invoice_number'] as String,
      };
    } catch (e) {
      if (e.toString().contains('Invoice already exists')) {
        throw 'Invoice already exists for this order';
      } else if (e.toString().contains('not found')) {
        throw 'Order not found';
      } else {
        throw 'Error creating invoice: $e';
      }
    }
  }

  /// Alternative method: Create order with immediate invoice (backward compatibility)
  Future<Order> createOrderWithInvoice({
    required String userId,
    required String storeId,
    required String serviceId,
    required List<OrderItem> items,
    String? pickupAddressId,
    String? deliveryAddressId,
    DateTime? pickupDate,
    String? pickupTimeSlot,
    DateTime? deliveryDate,
    String? deliveryTimeSlot,
    String? specialInstructions,
    double discountAmount = 0.0,
  }) async {
    try {
      // Calculate totals
      final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
      final taxAmount = subtotal * 0.1; // 10% tax
      const deliveryFee = 0.0;
      final totalAmount = subtotal + taxAmount + deliveryFee - discountAmount;

      // Use the combined function for immediate invoice creation
      final response = await _supabase.rpc(
        'create_order_with_invoice',
        params: {
          'p_user_id': userId,
          'p_store_id': storeId,
          'p_service_id': serviceId,
          'p_pickup_address_id': pickupAddressId,
          'p_delivery_address_id': deliveryAddressId,
          'p_pickup_date': pickupDate?.toIso8601String().split('T')[0],
          'p_pickup_time_slot': pickupTimeSlot,
          'p_delivery_date': deliveryDate?.toIso8601String().split('T')[0],
          'p_delivery_time_slot': deliveryTimeSlot,
          'p_subtotal': subtotal,
          'p_tax_amount': taxAmount,
          'p_delivery_fee': deliveryFee,
          'p_discount_amount': discountAmount,
          'p_total_amount': totalAmount,
          'p_special_instructions': specialInstructions,
        },
      );

      final orderId = response.first['order_id'] as String;

      // Create order items
      final itemsData = items
          .map(
            (item) => {
              'order_id': orderId,
              'garment_id': item.garmentId,
              'quantity': item.quantity,
              'unit_price': item.unitPrice,
              'total_price': item.totalPrice,
              'notes': item.notes,
            },
          )
          .toList();

      await _supabase.from('order_items').insert(itemsData);

      // Log status change
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': null,
        'new_status': 'pending',
        'changed_by': userId,
        'notes':
            'Order created with immediate invoice ${response.first['invoice_number']}',
      });

      return await getOrderById(orderId);
    } catch (e) {
      throw 'Error creating order with invoice: $e';
    }
  }

  /// Process payment for an order: Create invoice (if needed) and optionally update order status
  Future<Map<String, dynamic>> processPaymentForOrder({
    required String orderId,
    String? paymentMethod,
    double? paymentAmount,
    String? updateOrderStatus,
    String? paymentNotes,
  }) async {
    try {
      final response = await _supabase.rpc(
        'process_payment_for_order',
        params: {
          'p_order_id': orderId,
          'p_payment_method': paymentMethod,
          'p_payment_amount': paymentAmount,
          'p_update_order_status': updateOrderStatus,
          'p_payment_notes': paymentNotes,
        },
      );

      return {
        'invoice_id': response.first['invoice_id'] as String,
        'invoice_number': response.first['invoice_number'] as String,
        'payment_id': response.first['payment_id'] as String?,
        'order_updated': response.first['order_updated'] as bool,
      };
    } catch (e) {
      if (e.toString().contains('not found')) {
        throw 'Order not found';
      } else {
        throw 'Error processing payment: $e';
      }
    }
  }

  Future<Order> getOrderById(String orderId) async {
    try {
      final response = await _supabase
          .from('orders')
          .select('''
            *,
            store:stores(*),
            service:services(*),
            pickup_address:user_addresses!pickup_address_id(*),
            delivery_address:user_addresses!delivery_address_id(*),
            items:order_items(*, garment:garments(*))
          ''')
          .eq('id', orderId)
          .single();

      return Order.fromJson(response);
    } catch (e) {
      throw 'Error fetching order: $e';
    }
  }

  Future<List<Order>> getUserOrders(String userId) async {
    try {
      final response = await _supabase.rpc(
        'get_user_orders',
        params: {'user_uuid': userId},
      );

      return (response as List).map((orderData) {
        // Transform the data from stored procedure format to Order model format
        final transformedData = {
          'id': orderData['id'],
          'order_number': orderData['order_number'],
          'user_id': userId,
          'store_id': null, // Not provided by stored procedure
          'service_id': '', // Empty string instead of null to avoid type error
          'status': orderData['status'],
          'pickup_address_id': null,
          'delivery_address_id': null,
          'pickup_date': orderData['pickup_date'],
          'pickup_time_slot': null,
          'delivery_date': orderData['delivery_date'],
          'delivery_time_slot': null,
          'subtotal': orderData['total_amount'],
          'tax_amount': 0.0,
          'delivery_fee': 0.0,
          'discount_amount': 0.0,
          'total_amount': orderData['total_amount'],
          'special_instructions': null,
          'assigned_agent_id': null,
          'cancelled_by': null,
          'cancel_reason': null,
          'cancelled_at': null,
          'created_at': orderData['created_at'],
          'updated_at': orderData['created_at'],
          // Transform service_name to service object if available
          'service': orderData['service_name'] != null
              ? {
                  'id': '',
                  'name': orderData['service_name'],
                  'description': null,
                  'base_price': 0.0,
                  'price_per_kg': null,
                  'price_per_item': null,
                  'pricing_type': 'per_item',
                  'estimated_hours': 24,
                  'is_active': true,
                  'icon': null,
                  'created_at': orderData['created_at'],
                  'updated_at': orderData['created_at'],
                }
              : null,
          // Transform store_name to store object if available
          'store': orderData['store_name'] != null
              ? {
                  'id': '',
                  'name': orderData['store_name'],
                  'address': '',
                  'phone': null,
                  'email': null,
                  'manager_name': null,
                  'store_number': null,
                  'opening_time': null,
                  'closing_time': null,
                  'is_active': true,
                  'country': null,
                  'created_at': orderData['created_at'],
                  'updated_at': orderData['created_at'],
                }
              : null,
        };

        return Order.fromJson(transformedData);
      }).toList();
    } catch (e) {
      throw 'Error fetching user orders: $e';
    }
  }

  Future<List<Order>> getAllOrders({String? storeId}) async {
    try {
      final response = await _supabase.rpc(
        'get_all_orders',
        params: storeId != null ? {'store_filter_id': storeId} : {},
      );

      return (response as List).map((orderData) {
        // Transform the data from stored procedure format to Order model format
        final transformedData = {
          'id': orderData['id'],
          'order_number': orderData['order_number'],
          'user_id': orderData['user_id'],
          'store_id': null, // Not provided by stored procedure
          'service_id': '', // Empty string instead of null to avoid type error
          'status': orderData['status'],
          'pickup_address_id': null,
          'delivery_address_id': null,
          'pickup_date': orderData['pickup_date'],
          'pickup_time_slot': null,
          'delivery_date': orderData['delivery_date'],
          'delivery_time_slot': null,
          'subtotal': orderData['total_amount'],
          'tax_amount': 0.0,
          'delivery_fee': 0.0,
          'discount_amount': 0.0,
          'total_amount': orderData['total_amount'],
          'special_instructions': null,
          'assigned_agent_id': null,
          'cancelled_by': null,
          'cancel_reason': null,
          'cancelled_at': null,
          'created_at': orderData['created_at'],
          'updated_at': orderData['created_at'],
          // Transform service_name to service object if available
          'service': orderData['service_name'] != null
              ? {
                  'id': '',
                  'name': orderData['service_name'],
                  'description': null,
                  'base_price': 0.0,
                  'price_per_kg': null,
                  'price_per_item': null,
                  'pricing_type': 'per_item',
                  'estimated_hours': 24,
                  'is_active': true,
                  'icon': null,
                  'created_at': orderData['created_at'],
                  'updated_at': orderData['created_at'],
                }
              : null,
          // Transform store_name to store object if available
          'store': orderData['store_name'] != null
              ? {
                  'id': '',
                  'name': orderData['store_name'],
                  'address': '',
                  'phone': null,
                  'email': null,
                  'manager_name': null,
                  'store_number': null,
                  'opening_time': null,
                  'closing_time': null,
                  'is_active': true,
                  'country': null,
                  'created_at': orderData['created_at'],
                  'updated_at': orderData['created_at'],
                }
              : null,
        };

        return Order.fromJson(transformedData);
      }).toList();
    } catch (e) {
      throw 'Error fetching all orders: $e';
    }
  }

  Future<List<Order>> getOrdersByStatus(OrderStatus status) async {
    try {
      final response = await _supabase.rpc(
        'get_orders_by_status',
        params: {'order_status': status.toDbString()},
      );

      return (response as List).map((order) => Order.fromJson(order)).toList();
    } catch (e) {
      throw 'Error fetching orders by status: $e';
    }
  }

  Future<Order> updateOrderStatus({
    required String orderId,
    required OrderStatus newStatus,
    required String changedBy,
    String? notes,
  }) async {
    try {
      // Get current order to log old status
      final currentOrder = await getOrderById(orderId);

      // Update order status
      await _supabase
          .from('orders')
          .update({'status': newStatus.toDbString()})
          .eq('id', orderId);

      // Log status change
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': currentOrder.status.toDbString(),
        'new_status': newStatus.toDbString(),
        'changed_by': changedBy,
        'notes': notes,
      });

      return await getOrderById(orderId);
    } catch (e) {
      throw 'Error updating order status: $e';
    }
  }

  Future<Order> cancelOrder({
    required String orderId,
    required String cancelledBy,
    required String cancelReason,
  }) async {
    try {
      final now = DateTime.now();

      await _supabase
          .from('orders')
          .update({
            'status': 'cancelled',
            'cancelled_by': cancelledBy,
            'cancel_reason': cancelReason,
            'cancelled_at': now.toIso8601String(),
          })
          .eq('id', orderId);

      // Log status change
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': null, // Will be filled by trigger
        'new_status': 'cancelled',
        'changed_by': cancelledBy,
        'notes': 'Order cancelled: $cancelReason',
      });

      return await getOrderById(orderId);
    } catch (e) {
      throw 'Error cancelling order: $e';
    }
  }

  Future<Order> assignAgent({
    required String orderId,
    required String agentId,
    required String assignedBy,
  }) async {
    try {
      await _supabase
          .from('orders')
          .update({'assigned_agent_id': agentId})
          .eq('id', orderId);

      // Log assignment
      await _supabase.from('order_status_log').insert({
        'order_id': orderId,
        'old_status': null,
        'new_status': null,
        'changed_by': assignedBy,
        'notes': 'Agent assigned: $agentId',
      });

      return await getOrderById(orderId);
    } catch (e) {
      throw 'Error assigning agent: $e';
    }
  }

  // ============================================
  // TIME SLOTS
  // ============================================

  List<String> getAvailableTimeSlots() {
    return ['09:00-12:00', '12:00-15:00', '15:00-18:00', '18:00-21:00'];
  }

  bool isTimeSlotAvailable(DateTime date, String timeSlot) {
    // In a real app, you would check against existing bookings
    // For now, we'll assume all slots are available
    final now = DateTime.now();
    final slotDateTime = DateTime(date.year, date.month, date.day);

    // Don't allow booking in the past
    return slotDateTime.isAfter(now.subtract(const Duration(days: 1)));
  }

  // ============================================
  // REPEAT ORDER
  // ============================================

  Future<Order> repeatOrder({
    required String originalOrderId,
    required String userId,
    DateTime? newPickupDate,
    String? newPickupTimeSlot,
    DateTime? newDeliveryDate,
    String? newDeliveryTimeSlot,
  }) async {
    try {
      final originalOrder = await getOrderById(originalOrderId);

      if (originalOrder.items == null || originalOrder.items!.isEmpty) {
        throw 'Original order has no items';
      }

      return await createOrder(
        userId: userId,
        storeId: originalOrder.storeId!,
        serviceId: originalOrder.serviceId,
        items: originalOrder.items!
            .map(
              (item) => OrderItem(
                orderId: '', // Will be set when creating
                garmentId: item.garmentId,
                garment: item.garment,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                notes: item.notes,
              ),
            )
            .toList(),
        pickupAddressId: originalOrder.pickupAddressId,
        deliveryAddressId: originalOrder.deliveryAddressId,
        pickupDate: newPickupDate ?? originalOrder.pickupDate,
        pickupTimeSlot: newPickupTimeSlot ?? originalOrder.pickupTimeSlot,
        deliveryDate: newDeliveryDate ?? originalOrder.deliveryDate,
        deliveryTimeSlot: newDeliveryTimeSlot ?? originalOrder.deliveryTimeSlot,
        specialInstructions: originalOrder.specialInstructions,

        discountAmount: 0.0, // Reset discount for new order
      );
    } catch (e) {
      throw 'Error repeating order: $e';
    }
  }

  // ============================================
  // DELETE ORDER
  // ============================================

  Future<void> deleteOrder({
    required String orderId,
    required String deletedBy,
    required String deleteReason,
  }) async {
    try {
      // Get the order to check if it can be deleted
      final order = await getOrderById(orderId);

      // Only allow deletion of pending or cancelled orders
      if (!order.canDelete) {
        throw 'Order cannot be deleted. Only pending or cancelled orders can be deleted.';
      }

      // Start a transaction to delete order and related data
      // Delete order items first (foreign key constraint)
      await _supabase.from('order_items').delete().eq('order_id', orderId);

      // Delete order status log entries
      await _supabase.from('order_status_log').delete().eq('order_id', orderId);

      // Delete any related invoices and payments
      try {
        // Get invoice if exists
        final invoiceResponse = await _supabase
            .from('invoices')
            .select('id')
            .eq('order_id', orderId)
            .maybeSingle();

        if (invoiceResponse != null) {
          final invoiceId = invoiceResponse['id'] as String;

          // Delete payments first
          await _supabase.from('payments').delete().eq('invoice_id', invoiceId);

          // Delete invoice
          await _supabase.from('invoices').delete().eq('id', invoiceId);
        }
      } catch (e) {
        // Continue even if invoice deletion fails
        debugPrint('Warning: Could not delete invoice for order $orderId: $e');
      }

      // Finally delete the order
      await _supabase.from('orders').delete().eq('id', orderId);
    } catch (e) {
      throw 'Error deleting order: $e';
    }
  }
}
