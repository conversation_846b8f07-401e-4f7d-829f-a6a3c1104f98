import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/country_model.dart';
import '../services/country_service.dart';

class LocationService {
  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Check and request location permissions
  static Future<LocationPermission> checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return LocationPermission.denied;
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      return LocationPermission.deniedForever;
    }
    
    return permission;
  }

  /// Get current position
  static Future<Position?> getCurrentPosition() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled.');
        return null;
      }

      // Check permissions
      LocationPermission permission = await checkLocationPermission();
      if (permission == LocationPermission.denied || 
          permission == LocationPermission.deniedForever) {
        debugPrint('Location permissions are denied.');
        return null;
      }

      // Get current position
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  /// Get country from coordinates
  static Future<String?> getCountryFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        return placemarks.first.country;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting country from coordinates: $e');
      return null;
    }
  }

  /// Auto-detect country from current location
  static Future<Country?> autoDetectCountry() async {
    try {
      Position? position = await getCurrentPosition();
      if (position == null) return null;

      String? countryName = await getCountryFromCoordinates(
        position.latitude, 
        position.longitude
      );
      
      if (countryName == null) return null;

      final countryService = CountryService(supabase: Supabase.instance.client);
      return await countryService.getCountryByName(countryName);
    } catch (e) {
      debugPrint('Error auto-detecting country: $e');
      return null;
    }
  }

  /// Get location permission status message
  static String getPermissionStatusMessage(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        return 'Location access denied. Please enable location permissions.';
      case LocationPermission.deniedForever:
        return 'Location access permanently denied. Please enable in system settings.';
      case LocationPermission.whileInUse:
      case LocationPermission.always:
        return 'Location access granted.';
      default:
        return 'Unknown location permission status.';
    }
  }
}
