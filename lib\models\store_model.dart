class Store {
  final String id;
  final String? storeNumber;
  final String name;
  final String address;
  final String? phone;
  final String? email;
  final String? description;
  final String? country;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Store({
    required this.id,
    this.storeNumber,
    required this.name,
    required this.address,
    this.phone,
    this.email,
    this.description,
    this.country,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  // Create a copy of this store with some fields changed
  Store copyWith({
    String? id,
    String? storeNumber,
    String? name,
    String? address,
    String? phone,
    String? email,
    String? description,
    String? country,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Store(
      id: id ?? this.id,
      storeNumber: storeNumber ?? this.storeNumber,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      description: description ?? this.description,
      country: country ?? this.country,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Convert Store to Map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'store_number': storeNumber,
      'name': name,
      'address': address,
      'phone': phone,
      'email': email,
      'description': description,
      'country': country,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create Store from Map
  factory Store.fromMap(Map<String, dynamic> map) {
    return Store(
      id: map['id'] ?? '',
      storeNumber: map['store_number'],
      name: map['name'] ?? '',
      address: map['address'] ?? '',
      phone: map['phone'],
      email: map['email'],
      description: map['description'],
      country: map['country'],
      isActive: map['is_active'] ?? map['isActive'] ?? true,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at']) 
          : map['createdAt'] != null 
              ? DateTime.parse(map['createdAt'])
              : DateTime.now(),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : map['updatedAt'] != null 
              ? DateTime.parse(map['updatedAt'])
              : DateTime.now(),
    );
  }

  // Create Store from JSON (alias for fromMap)
  factory Store.fromJson(Map<String, dynamic> json) => Store.fromMap(json);

  @override
  String toString() {
    return 'Store{id: $id, storeNumber: $storeNumber, name: $name, address: $address, phone: $phone, email: $email, description: $description, country: $country, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Store && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
