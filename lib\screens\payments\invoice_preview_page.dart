import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import 'package:printing/printing.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../models/invoice_model.dart';
import '../../models/payment_model.dart';
import '../../services/invoice_service.dart';
import '../../services/order_service.dart';
import '../../models/order_model.dart';

class InvoicePreviewPage extends StatefulWidget {
  final String orderId;
  final String? invoiceId;

  const InvoicePreviewPage({super.key, required this.orderId, this.invoiceId});

  @override
  State<InvoicePreviewPage> createState() => _InvoicePreviewPageState();
}

class _InvoicePreviewPageState extends State<InvoicePreviewPage> {
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);

  Invoice? _invoice;
  Order? _order;
  List<Payment> _payments = [];
  bool _isLoading = true;
  String _currency = '';

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() => _isLoading = true);
    try {
      _order = await _orderService.getOrderById(widget.orderId);
      _currency = await _orderService.getCurrencySymbol();
      final invoice = widget.invoiceId != null
          ? await _invoiceService.getInvoiceWithPayments(widget.invoiceId!)
          : await _invoiceService.getInvoiceByOrderId(widget.orderId);
      _invoice = invoice;
      _payments = await _invoiceService.getInvoicePayments(invoice.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading invoice: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<pw.Document> _buildPdf() async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        build: (context) => [
          // Invoice Header
          pw.Header(level: 0, child: pw.Text('Invoice ${_invoice!.invoiceNumber}', style: pw.TextStyle(fontSize: 22, fontWeight: pw.FontWeight.bold))),
          pw.SizedBox(height: 8),
          pw.Text('Order: ${_order?.orderNumber ?? ''}'),
          pw.Text('Status: ${_invoice!.paymentStatus.displayName}'),
          pw.Text('Date: ${_invoice!.invoiceDate.toString().substring(0, 10)}'),
          pw.SizedBox(height: 16),
          
          // Service Details
          pw.Text('Service Details', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 8),
          pw.Text('Service: ${_order?.service?.name ?? 'N/A'}'),
          if (_order?.service?.description != null && _order!.service!.description!.isNotEmpty)
            pw.Text('Description: ${_order!.service!.description}'),
          pw.SizedBox(height: 12),
          
          // Order Items
          pw.Text('Items', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 8),
          if (_order?.items == null || _order!.items!.isEmpty)
            pw.Text('No items listed')
          else
            pw.TableHelper.fromTextArray(
              headers: ['Item', 'Quantity', 'Unit Price', 'Total'],
              data: _order!.items!.map((item) => [
                item.garment?.name ?? 'Unknown Item',
                item.quantity.toString(),
                '$_currency${item.unitPrice.toStringAsFixed(2)}',
                '$_currency${item.totalPrice.toStringAsFixed(2)}',
              ]).toList(),
            ),
          
          pw.SizedBox(height: 16),
          pw.Divider(),
          
          // Pricing Summary
          pw.Text('Pricing Summary', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Subtotal:'),
              pw.Text('$_currency${_invoice!.subtotal.toStringAsFixed(2)}'),
            ],
          ),
          if (_invoice!.taxAmount > 0)
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Tax:'),
                pw.Text('$_currency${_invoice!.taxAmount.toStringAsFixed(2)}'),
              ],
            ),
          if (_invoice!.deliveryFee > 0)
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Delivery Fee:'),
                pw.Text('$_currency${_invoice!.deliveryFee.toStringAsFixed(2)}'),
              ],
            ),
          if (_invoice!.discountAmount > 0)
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Discount:'),
                pw.Text('-$_currency${_invoice!.discountAmount.toStringAsFixed(2)}'),
              ],
            ),
          pw.Divider(),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Amount:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              pw.Text('$_currency${_invoice!.totalAmount.toStringAsFixed(2)}', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ],
          ),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Total Paid:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              pw.Text('$_currency${(_invoice!.totalPaid ?? 0).toStringAsFixed(2)}', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ],
          ),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('Outstanding:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              pw.Text('$_currency${_invoice!.effectiveOutstandingAmount.toStringAsFixed(2)}', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ],
          ),
          
          pw.SizedBox(height: 16),
          pw.Divider(),
          
          // Payment History
          pw.Text('Payment History', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 8),
          if (_payments.isEmpty)
            pw.Text('No payments recorded')
          else
            pw.TableHelper.fromTextArray(
              headers: ['Date', 'Method', 'Reference', 'Amount'],
              data: _payments.map((p) => [
                p.createdAt.toLocal().toString().substring(0, 16),
                p.method.displayName,
                p.displayReference,
                '$_currency${p.amount.toStringAsFixed(2)}',
              ]).toList(),
            ),
          
          // Special Instructions (if any)
          if (_order?.specialInstructions != null && _order!.specialInstructions!.isNotEmpty) ...[
            pw.SizedBox(height: 16),
            pw.Text('Special Instructions', style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 8),
            pw.Text(_order!.specialInstructions!),
          ],
        ],
      ),
    );

    return pdf;
  }

  Future<void> _printPdf() async {
    final pdf = await _buildPdf();
    await Printing.layoutPdf(onLayout: (format) => pdf.save());
  }

  Future<void> _savePdf() async {
    try {
      final pdf = await _buildPdf();
      final bytes = await pdf.save();
      
      final fileName = 'invoice_${_invoice!.invoiceSequenceNumber}.pdf';
      String? filePath;
      String? displayPath;
      
      if (Platform.isAndroid) {
        // For Android, try multiple approaches to save to Downloads
        try {
          // Method 1: Try to get Downloads directory directly
          final downloadsDir = await getDownloadsDirectory();
          if (downloadsDir != null) {
            final file = File('${downloadsDir.path}/$fileName');
            await file.writeAsBytes(bytes, flush: true);
            filePath = file.path;
            displayPath = 'Downloads folder';
          }
        } catch (e) {
          // Method 1 failed, try alternative approaches
        }
        
        if (filePath == null) {
          try {
            // Method 2: Use external storage Downloads folder
            final dir = await getExternalStorageDirectory();
            if (dir != null) {
              // Navigate to the public Downloads folder
              final publicDownloads = Directory('/storage/emulated/0/Download');
              if (await publicDownloads.exists()) {
                final file = File('${publicDownloads.path}/$fileName');
                await file.writeAsBytes(bytes, flush: true);
                filePath = file.path;
                displayPath = 'Downloads folder';
              } else {
                // Create Downloads subfolder in app's external directory
                final downloadsPath = '${dir.path}/Downloads';
                final downloadsDir = Directory(downloadsPath);
                if (!await downloadsDir.exists()) {
                  await downloadsDir.create(recursive: true);
                }
                final file = File('$downloadsPath/$fileName');
                await file.writeAsBytes(bytes, flush: true);
                filePath = file.path;
                displayPath = 'App Downloads folder';
              }
            }
          } catch (e) {
            // Method 2 failed, try final fallback
          }
        }
        
        if (filePath == null) {
          // Method 3: Final fallback to app directory
          final dir = await getExternalStorageDirectory();
          if (dir != null) {
            final file = File('${dir.path}/$fileName');
            await file.writeAsBytes(bytes, flush: true);
            filePath = file.path;
            displayPath = 'App folder';
          }
        }
      } else {
        // iOS/other platforms
        final dir = await getApplicationDocumentsDirectory();
        final file = File('${dir.path}/$fileName');
        await file.writeAsBytes(bytes, flush: true);
        filePath = file.path;
        displayPath = 'Documents folder';
      }

      if (filePath == null) {
        throw 'Unable to save file to storage';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invoice saved to ${displayPath ?? 'device storage'}'), 
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Open',
              textColor: Colors.white,
              onPressed: () async {
                // Try to open the file with default PDF viewer
                try {
                  final uri = Uri.file(filePath!);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri, mode: LaunchMode.externalApplication);
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please check your Downloads folder'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                }
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving PDF: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Invoice Preview'),
        actions: [
          IconButton(onPressed: _isLoading ? null : _printPdf, icon: const Icon(Icons.print)),
          IconButton(onPressed: _isLoading ? null : _savePdf, icon: const Icon(Icons.download)),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : PdfPreview(
              build: (format) async => (await _buildPdf()).save(),
              canChangeOrientation: false,
              canChangePageFormat: false,
            ),
    );
  }
}
