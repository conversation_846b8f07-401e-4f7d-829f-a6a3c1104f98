import 'package:flutter/foundation.dart';

@immutable
class Garment {
  final String id;
  final String name;
  final String category; // 'clothing', 'household', 'special'
  final String? description;
  final String? icon;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Garment({
    required this.id,
    required this.name,
    required this.category,
    this.description,
    this.icon,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Garment.fromJson(Map<String, dynamic> json) {
    return Garment(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'icon': icon,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get categoryDisplayName {
    switch (category) {
      case 'clothing':
        return 'Clothing';
      case 'household':
        return 'Household Items';
      case 'special':
        return 'Special Care';
      default:
        return 'Other';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Garment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => '$name ($category)';
}

@immutable
class OrderItem {
  final String? id;
  final String orderId;
  final String garmentId;
  final Garment? garment; // Populated when joining
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const OrderItem({
    this.id,
    required this.orderId,
    required this.garmentId,
    this.garment,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] as String?,
      orderId: json['order_id'] as String,
      garmentId: json['garment_id'] as String,
      garment: json['garment'] != null ? Garment.fromJson(json['garment'] as Map<String, dynamic>) : null,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'order_id': orderId,
      'garment_id': garmentId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'notes': notes,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  OrderItem copyWith({
    String? id,
    String? orderId,
    String? garmentId,
    Garment? garment,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      garmentId: garmentId ?? this.garmentId,
      garment: garment ?? this.garment,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem && 
           other.orderId == orderId && 
           other.garmentId == garmentId;
  }

  @override
  int get hashCode => Object.hash(orderId, garmentId);

  @override
  String toString() => '${garment?.name ?? garmentId} x$quantity = \$${totalPrice.toStringAsFixed(2)}';
}
