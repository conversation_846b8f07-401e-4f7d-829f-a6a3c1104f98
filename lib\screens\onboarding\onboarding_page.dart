import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class OnboardingFlow extends StatefulWidget {
  final VoidCallback onFinished;
  const OnboardingFlow({super.key, required this.onFinished});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _page = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _next() {
    if (_page < 2) {
      _pageController.nextPage(duration: const Duration(milliseconds: 400), curve: Curves.easeOut);
    } else {
      widget.onFinished();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Stack(
          children: [
            if (_page < 2)
              Positioned(
                top: 8,
                right: 16,
                child: TextButton(
                  onPressed: widget.onFinished,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text(
                    'Skip',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            PageView(
              controller: _pageController,
              onPageChanged: (i) => setState(() => _page = i),
              children: const [
                _SplashPage(
                  title: 'Welcome to Laundry Pro',
                  subtitle: 'Your complete laundry management solution',
                  lottieAsset: 'Assets/wash.json',
                  features: [
                    OnboardingFeature(
                      icon: Icons.flash_on,
                      title: 'Smart Order Processing',
                      description: 'Place orders faster with intelligent item selection and automated pricing',
                    ),
                    OnboardingFeature(
                      icon: Icons.people,
                      title: 'Customer Management',
                      description: 'Comprehensive customer profiles with order history and preferences',
                    ),
                    OnboardingFeature(
                      icon: Icons.security,
                      title: 'Secure Authentication',
                      description: 'Biometric login and advanced security features for data protection',
                    ),
                    OnboardingFeature(
                      icon: Icons.inventory,
                      title: 'Inventory Control',
                      description: 'Track garments, services, and supplies with real-time updates',
                    ),
                  ],
                ),
                _SplashPage(
                  title: 'Multi-Store Operations',
                  subtitle: 'Scale your business across multiple locations',
                  lottieAsset: 'Assets/mulltistore.json',
                  features: [
                    OnboardingFeature(
                      icon: Icons.store,
                      title: 'Store Management',
                      description: 'Seamlessly switch between multiple store locations',
                    ),
                    OnboardingFeature(
                      icon: Icons.group,
                      title: 'Staff Coordination',
                      description: 'Assign staff, track activities, and manage permissions',
                    ),
                    OnboardingFeature(
                      icon: Icons.sync,
                      title: 'Synchronized Settings',
                      description: 'Maintain consistent configurations across all branches',
                    ),
                    OnboardingFeature(
                      icon: Icons.trending_up,
                      title: 'Performance Tracking',
                      description: 'Monitor and compare performance across all locations',
                    ),
                  ],
                ),
                _SplashPage(
                  title: 'Analytics & Insights',
                  subtitle: 'Make data-driven decisions for your business',
                  lottieAsset: 'Assets/report.json',
                  features: [
                    OnboardingFeature(
                      icon: Icons.analytics,
                      title: 'Sales Analytics',
                      description: 'Comprehensive sales reports with trends and forecasting',
                    ),
                    OnboardingFeature(
                      icon: Icons.receipt_long,
                      title: 'Financial Reports',
                      description: 'Track payments, expenses, and profitability in real-time',
                    ),
                    OnboardingFeature(
                      icon: Icons.print,
                      title: 'Professional Invoices',
                      description: 'Generate and export branded invoices and receipts',
                    ),
                    OnboardingFeature(
                      icon: Icons.calendar_today,
                      title: 'Daily Operations',
                      description: 'Daily summaries and operational insights at your fingertips',
                    ),
                  ],
                ),
              ],
            ),
            Positioned(
              left: 16,
              right: 16,
              bottom: 24,
              child: Row(
                children: [
                  Row(
                    children: List.generate(3, (i) => _Dot(active: i == _page)),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: _next,
                    icon: Icon(_page < 2 ? Icons.arrow_forward_rounded : Icons.check_circle_rounded),
                    label: Text(_page < 2 ? 'Next' : 'Get Started'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2563EB),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      elevation: 2,
                      shadowColor: const Color(0xFF2563EB).withValues(alpha: 0.3),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SplashPage extends StatelessWidget {
  final String title;
  final String subtitle;
  final String lottieAsset;
  final List<OnboardingFeature> features;

  const _SplashPage({
    required this.title,
    required this.subtitle,
    required this.lottieAsset,
    this.features = const [],
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.height < 700;
    final animationHeight = isSmallScreen ? size.height * 0.25 : size.height * 0.32;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          // Animation Section
          SizedBox(
            height: animationHeight,
            child: Lottie.asset(
              lottieAsset,
              fit: BoxFit.contain,
            ),
          ),
          const SizedBox(height: 16),
          
          // Title and Subtitle
          Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 28, 
              fontWeight: FontWeight.w900,
              color: Color(0xFF1a1a1a),
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16, 
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Feature Cards
          if (features.isNotEmpty)
            Expanded(
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: Column(
                    children: features.map((feature) => _FeatureCard(feature: feature)).toList(),
                  ),
                ),
              ),
            ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

// Feature data model
class OnboardingFeature {
  final IconData icon;
  final String title;
  final String description;

  const OnboardingFeature({
    required this.icon,
    required this.title,
    required this.description,
  });
}

// Feature card widget
class _FeatureCard extends StatelessWidget {
  final OnboardingFeature feature;

  const _FeatureCard({required this.feature});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              feature.icon,
              color: Colors.blue[600],
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1a1a1a),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  feature.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Dot extends StatelessWidget {
  final bool active;
  const _Dot({required this.active});
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      margin: const EdgeInsets.only(right: 8),
      width: active ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: active ? const Color(0xFF2563EB) : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(6),
      ),
    );
  }
}