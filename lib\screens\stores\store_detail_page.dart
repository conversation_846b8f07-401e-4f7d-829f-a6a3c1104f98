import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/store_model.dart';
import '../../models/country_model.dart';
import '../../services/location_service.dart';
import '../../services/country_service.dart';
import '../../widgets/country_picker_dialog.dart';

class StoreDetailPage extends StatefulWidget {
  final Store? store;
  final bool isViewOnly;

  const StoreDetailPage({super.key, this.store, this.isViewOnly = false});

  @override
  State<StoreDetailPage> createState() => _StoreDetailPageState();
}

class _StoreDetailPageState extends State<StoreDetailPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _descriptionController;
  Country? _selectedCountry;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.store?.name);
    _addressController = TextEditingController(text: widget.store?.address);
    _phoneController = TextEditingController(text: widget.store?.phone);
    _emailController = TextEditingController(text: widget.store?.email);
    _descriptionController = TextEditingController(
      text: widget.store?.description,
    );

    // Load existing country if editing
    if (widget.store?.country != null && widget.store!.country!.isNotEmpty) {
      _loadExistingCountry(widget.store!.country!);
    }
  }

  Future<void> _loadExistingCountry(String countryName) async {
    try {
      final countryService = CountryService(supabase: Supabase.instance.client);
      final country = await countryService.getCountryByName(countryName);
      if (country != null && mounted) {
        setState(() {
          _selectedCountry = country;
        });
      }
    } catch (e) {
      debugPrint('Error loading existing country: $e');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.store != null && !widget.isViewOnly;
    final isViewing = widget.isViewOnly;
    final storeName = widget.store?.name ?? 'New Store';
    
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          isViewing 
              ? '$storeName Details' 
              : (isEditing ? 'Edit $storeName' : 'Add New Store'),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        actions: isViewing
            ? [
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton.filled(
                    icon: const Icon(Icons.edit_outlined, size: 20),
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              StoreDetailPage(store: widget.store),
                        ),
                      );
                    },
                    tooltip: 'Edit Store',
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ]
            : null,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Store Header Card (for viewing mode)
            if (isViewing && widget.store != null) _buildStoreHeaderCard(),
            
            // Store Information Card
            _buildInfoCard(
              title: 'Store Information',
              icon: Icons.store_outlined,
              color: Colors.blue,
              children: [
                if (widget.store?.storeNumber != null)
                  _buildStoreNumberChip(widget.store!.storeNumber!),
                _buildModernTextField(
                  controller: _nameController,
                  label: 'Store Name',
                  hint: 'Enter store name',
                  icon: Icons.business_outlined,
                  isRequired: true,
                  readOnly: widget.isViewOnly,
                ),
                _buildModernTextField(
                  controller: _descriptionController,
                  label: 'Description',
                  hint: 'Enter store description',
                  icon: Icons.description_outlined,
                  maxLines: 3,
                  readOnly: widget.isViewOnly,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Contact Information Card
            _buildInfoCard(
              title: 'Contact Information',
              icon: Icons.contact_phone_outlined,
              color: Colors.green,
              children: [
                _buildModernTextField(
                  controller: _phoneController,
                  label: 'Phone Number',
                  hint: 'Enter phone number',
                  icon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  isRequired: true,
                  readOnly: widget.isViewOnly,
                ),
                _buildModernTextField(
                  controller: _emailController,
                  label: 'Email Address',
                  hint: 'Enter email address',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  isRequired: true,
                  readOnly: widget.isViewOnly,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Location Information Card
            _buildInfoCard(
              title: 'Location Details',
              icon: Icons.location_on_outlined,
              color: Colors.orange,
              children: [
                _buildModernTextField(
                  controller: _addressController,
                  label: 'Address',
                  hint: 'Enter complete address',
                  icon: Icons.home_outlined,
                  maxLines: 2,
                  isRequired: true,
                  readOnly: widget.isViewOnly,
                ),
                _buildModernCountryField(),
                if (!isViewing) _buildAutoDetectButton(),
              ],
            ),

            const SizedBox(height: 24),

            // Action Buttons
            if (!widget.isViewOnly) _buildActionButtons(isEditing),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // Store Header Card for viewing mode
  Widget _buildStoreHeaderCard() {
    final store = widget.store!;
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue[300]!.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.store,
                size: 48,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              store.name,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            if (store.storeNumber != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  store.storeNumber!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Modern Info Card Container
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required MaterialColor color,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: color[700],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: color[700],
                  ),
                ),
              ],
            ),
          ),
          // Card Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  // Store Number Chip
  Widget _buildStoreNumberChip(String storeNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Wrap(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.tag,
                  size: 16,
                  color: Colors.blue[700],
                ),
                const SizedBox(width: 8),
                Text(
                  'Store #$storeNumber',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Modern Text Field
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    bool readOnly = false,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        readOnly: readOnly,
        maxLines: maxLines,
        keyboardType: keyboardType,
        style: const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          labelText: label + (isRequired ? ' *' : ''),
          hintText: hint,
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: readOnly ? Colors.grey[100] : Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: readOnly ? Colors.grey[600] : Colors.blue[600],
            ),
          ),
          labelStyle: TextStyle(
            color: Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(color: Colors.grey[400]),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.blue[400]!, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.red[400]!),
          ),
          filled: true,
          fillColor: readOnly ? Colors.grey[50] : Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        validator: isRequired && !readOnly
            ? (value) => value?.isEmpty == true ? '$label is required' : null
            : null,
      ),
    );
  }

  // Modern Country Field
  Widget _buildModernCountryField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: widget.isViewOnly ? null : _showCountryPicker,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.isViewOnly ? Colors.grey[50] : Colors.white,
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                margin: const EdgeInsets.only(right: 16),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: widget.isViewOnly ? Colors.grey[100] : Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.public,
                  size: 20,
                  color: widget.isViewOnly ? Colors.grey[600] : Colors.orange[600],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Country *',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (_selectedCountry?.flagEmoji != null) ...[
                          Text(
                            _selectedCountry!.flagEmoji!,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: Text(
                            _selectedCountry?.name ?? 'Select country',
                            style: TextStyle(
                              fontSize: 16,
                              color: _selectedCountry == null
                                  ? Colors.grey[500]
                                  : Colors.black87,
                              fontWeight: _selectedCountry == null
                                  ? FontWeight.normal
                                  : FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (!widget.isViewOnly)
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey[600],
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Auto Detect Button
  Widget _buildAutoDetectButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: _autoFillCountry,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.my_location,
                size: 18,
                color: Colors.orange[700],
              ),
              const SizedBox(width: 8),
              Text(
                'Auto-detect country',
                style: TextStyle(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Action Buttons
  Widget _buildActionButtons(bool isEditing) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              height: 52,
              child: ElevatedButton(
                onPressed: _saveStore,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                  shadowColor: Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      isEditing ? Icons.update : Icons.add_business,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isEditing ? 'Update Store' : 'Create Store',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker() {
    showCountryPicker(
      context: context,
      selectedCountry: _selectedCountry,
      onSelect: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
    );
  }

  Future<void> _saveStore() async {
    // Validate country selection
    if (_selectedCountry == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a country'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      try {
        final supabase = Supabase.instance.client;
        final storeData = {
          'name': _nameController.text.trim(),
          'address': _addressController.text.trim(),
          'phone': _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          'email': _emailController.text.trim().isEmpty
              ? null
              : _emailController.text.trim(),
          'description': _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          'country': _selectedCountry?.name ?? '',
          'is_active': true,
          'created_by': supabase.auth.currentUser?.id,
        };

        if (widget.store == null) {
          // Creating new store
          await supabase.from('stores').insert(storeData);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Store created successfully!'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(context);
          }
        } else {
          // Updating existing store
          await supabase
              .from('stores')
              .update(storeData)
              .eq('id', widget.store!.id);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Store updated successfully!'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(context);
          }
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving store: $error'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }

  Future<void> _autoFillCountry() async {
    Country? country = await LocationService.autoDetectCountry();
    if (!mounted) return;

    if (country != null) {
      setState(() {
        _selectedCountry = country;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Country auto-detected successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to auto-detect country.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
