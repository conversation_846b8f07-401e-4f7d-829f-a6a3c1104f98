import 'package:flutter/foundation.dart';
import 'payment_model.dart';

enum ExpenseCategory {
  rent,
  utilities,
  salaries,
  supplies,
  transport,
  maintenance,
  marketing,
  refund,
  misc;

  String get displayName {
    switch (this) {
      case ExpenseCategory.rent:
        return 'Rent';
      case ExpenseCategory.utilities:
        return 'Utilities';
      case ExpenseCategory.salaries:
        return 'Salaries';
      case ExpenseCategory.supplies:
        return 'Supplies';
      case ExpenseCategory.transport:
        return 'Transport';
      case ExpenseCategory.maintenance:
        return 'Maintenance';
      case ExpenseCategory.marketing:
        return 'Marketing';
      case ExpenseCategory.refund:
        return 'Refund';
      case ExpenseCategory.misc:
        return 'Misc';
    }
  }

  String get dbValue {
    switch (this) {
      case ExpenseCategory.rent:
        return 'rent';
      case ExpenseCategory.utilities:
        return 'utilities';
      case ExpenseCategory.salaries:
        return 'salaries';
      case ExpenseCategory.supplies:
        return 'supplies';
      case ExpenseCategory.transport:
        return 'transport';
      case ExpenseCategory.maintenance:
        return 'maintenance';
      case ExpenseCategory.marketing:
        return 'marketing';
      case ExpenseCategory.refund:
        return 'refund';
      case ExpenseCategory.misc:
        return 'misc';
    }
  }

  static ExpenseCategory fromString(String value) {
    switch (value.toLowerCase()) {
      case 'rent':
        return ExpenseCategory.rent;
      case 'utilities':
        return ExpenseCategory.utilities;
      case 'salaries':
        return ExpenseCategory.salaries;
      case 'supplies':
        return ExpenseCategory.supplies;
      case 'transport':
        return ExpenseCategory.transport;
      case 'maintenance':
        return ExpenseCategory.maintenance;
      case 'marketing':
        return ExpenseCategory.marketing;
      case 'refund':
        return ExpenseCategory.refund;
      default:
        return ExpenseCategory.misc;
    }
  }
}

@immutable
class ExpenseAllocation {
  final String id;
  final String expenseId;
  final String storeId;
  final double amount;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ExpenseAllocation({
    required this.id,
    required this.expenseId,
    required this.storeId,
    required this.amount,
    this.createdAt,
    this.updatedAt,
  });

  factory ExpenseAllocation.fromJson(Map<String, dynamic> json) {
    return ExpenseAllocation(
      id: (json['id'] as String?) ?? '',
      expenseId: (json['expense_id'] as String?) ?? '',
      storeId: (json['store_id'] as String?) ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at'] as String) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'expense_id': expenseId,
      'store_id': storeId,
      'amount': amount,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }
}

@immutable
class Expense {
  final String id;
  final ExpenseCategory category;
  final String? customCategoryId;
  final double amount;
  final DateTime expenseDate;
  final String? note;
  final String? vendor;
  final String? reference;
  final PaymentMethod paymentMethod;
  final String? storeId;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<ExpenseAllocation>? allocations;

  const Expense({
    required this.id,
    required this.category,
    this.customCategoryId,
    required this.amount,
    required this.expenseDate,
    this.note,
    this.vendor,
    this.reference,
    required this.paymentMethod,
    this.storeId,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.allocations,
  });

  bool get hasAllocations => allocations != null && allocations!.isNotEmpty;

  factory Expense.fromJson(Map<String, dynamic> json) {
    return Expense(
      id: (json['id'] as String?) ?? '',
      category: ExpenseCategory.fromString((json['category'] as String?) ?? 'misc'),
      customCategoryId: json['custom_category_id'] as String?,
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      expenseDate: json['expense_date'] != null
          ? DateTime.parse(json['expense_date'] as String)
          : DateTime.now(),
      note: json['note'] as String?,
      vendor: json['vendor'] as String?,
      reference: json['reference'] as String?,
      paymentMethod: PaymentMethod.fromString((json['payment_method'] as String?) ?? 'cash'),
      storeId: json['store_id'] as String?,
      createdBy: (json['created_by'] as String?) ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      allocations: json['allocations'] != null
          ? (json['allocations'] as List)
              .map((a) => ExpenseAllocation.fromJson(Map<String, dynamic>.from(a as Map)))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category.dbValue,
      'custom_category_id': customCategoryId,
      'amount': amount,
      'expense_date': expenseDate.toIso8601String().split('T')[0],
      'note': note,
      'vendor': vendor,
      'reference': reference,
      'payment_method': paymentMethod.dbValue,
      'store_id': storeId,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      if (allocations != null) 'allocations': allocations!.map((a) => a.toJson()).toList(),
    };
  }

  Expense copyWith({
    String? id,
    ExpenseCategory? category,
    String? customCategoryId,
    double? amount,
    DateTime? expenseDate,
    String? note,
    String? vendor,
    String? reference,
    PaymentMethod? paymentMethod,
    String? storeId,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ExpenseAllocation>? allocations,
  }) {
    return Expense(
      id: id ?? this.id,
      category: category ?? this.category,
      customCategoryId: customCategoryId ?? this.customCategoryId,
      amount: amount ?? this.amount,
      expenseDate: expenseDate ?? this.expenseDate,
      note: note ?? this.note,
      vendor: vendor ?? this.vendor,
      reference: reference ?? this.reference,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      storeId: storeId ?? this.storeId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      allocations: allocations ?? this.allocations,
    );
  }
}


