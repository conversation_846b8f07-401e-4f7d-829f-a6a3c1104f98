-- Combined Migration Script for Laundry Pro
-- This script combines all migrations into one file with proper dependency handling
-- Run this script to set up the complete database schema

-- ============================================
-- DROP STATEMENTS (in dependency order)
-- ============================================

-- Drop triggers first
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
DROP TRIGGER IF EXISTS update_stores_updated_at ON stores;
DROP TRIGGER IF EXISTS generate_store_number_trigger ON stores;

-- Drop functions
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS generate_store_number() CASCADE;
DROP FUNCTION IF EXISTS search_stores(TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_stores_by_country(TEXT) CASCADE;
DROP FUNCTION IF EXISTS search_countries(TEXT) CASCADE;

-- Drop views
DROP VIEW IF EXISTS active_stores;

-- Drop tables (in dependency order)
DROP TABLE IF EXISTS profiles CASCADE;
DROP TABLE IF EXISTS stores CASCADE;
DROP TABLE IF EXISTS countries CASCADE;

-- Drop sequences
DROP SEQUENCE IF EXISTS store_number_seq;

-- ============================================
-- EXTENSIONS
-- ============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================
-- UTILITY FUNCTIONS
-- ============================================

-- Create function to handle updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================
-- COUNTRIES TABLE
-- ============================================

-- Create countries table
CREATE TABLE countries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    phone_code TEXT,
    flag_emoji TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    CONSTRAINT unique_country_code UNIQUE (code)
);

-- Create indexes for countries
CREATE INDEX idx_countries_name ON countries(name);
CREATE INDEX idx_countries_code ON countries(code);
CREATE INDEX idx_countries_is_active ON countries(is_active);

-- Enable Row Level Security on countries
ALTER TABLE countries ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for countries
CREATE POLICY "Countries are viewable by all users" ON countries
    FOR SELECT USING (true);

-- ============================================
-- PROFILES TABLE
-- ============================================

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    country TEXT,
    phone TEXT,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create trigger for profiles updated_at
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Profiles are viewable by all authenticated users" ON profiles
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT TO authenticated WITH CHECK ((SELECT auth.uid()) = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE TO authenticated USING ((SELECT auth.uid()) = id);

-- ============================================
-- STORES TABLE
-- ============================================

-- Create sequence for store numbers with proper ownership
CREATE SEQUENCE IF NOT EXISTS store_number_seq START 1;

-- Grant usage on sequence to authenticated role and ensure proper ownership
ALTER SEQUENCE store_number_seq OWNER TO postgres;
GRANT USAGE, SELECT ON SEQUENCE store_number_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE store_number_seq TO anon;

-- Verify sequence exists and is accessible
DO $$
BEGIN
    PERFORM nextval('store_number_seq');
    PERFORM setval('store_number_seq', 1, false); -- Reset to start at 1
    RAISE NOTICE 'Store number sequence created and verified successfully';
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to create or access store_number_seq: %', SQLERRM;
END $$;

-- Create stores table
CREATE TABLE stores (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    store_number TEXT,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    description TEXT,
    country TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create function to generate store number with proper error handling
CREATE OR REPLACE FUNCTION generate_store_number()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    next_num INTEGER;
BEGIN
    IF NEW.store_number IS NULL THEN
        -- Get next sequence value with error handling
        SELECT nextval('public.store_number_seq'::regclass) INTO next_num;
        NEW.store_number := 'STR - ' || LPAD(next_num::text, 3, '0');
    END IF;
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Fallback to timestamp-based number if sequence fails
        NEW.store_number := 'STR - ' || LPAD(EXTRACT(EPOCH FROM NOW())::bigint::text, 10, '0');
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add security comment for documentation
COMMENT ON FUNCTION generate_store_number() IS 'Auto-generates sequential store numbers with format STR - 001. Uses secure search_path and SECURITY DEFINER for safety.';

-- Create trigger to auto-generate store numbers
CREATE TRIGGER generate_store_number_trigger
    BEFORE INSERT ON stores
    FOR EACH ROW
    EXECUTE FUNCTION generate_store_number();

-- Create trigger for stores updated_at
CREATE TRIGGER update_stores_updated_at 
    BEFORE UPDATE ON stores 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for stores
CREATE INDEX idx_stores_country ON stores(country);
CREATE INDEX idx_stores_is_active ON stores(is_active);
CREATE INDEX idx_stores_created_by ON stores(created_by);
CREATE INDEX idx_stores_created_at ON stores(created_at);
CREATE UNIQUE INDEX idx_stores_store_number ON stores(store_number);

-- Enable Row Level Security on stores
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;

-- RLS Policies for stores
CREATE POLICY "Stores are viewable by all authenticated users" ON stores
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert stores" ON stores
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can update stores" ON stores
    FOR UPDATE TO authenticated USING (true);

CREATE POLICY "Authenticated users can delete stores" ON stores
    FOR DELETE TO authenticated USING (true);

-- ============================================
-- VIEWS
-- ============================================

-- Create a view for active stores only
CREATE VIEW active_stores AS
SELECT * FROM stores WHERE is_active = true;

-- Enable RLS on the view
ALTER VIEW active_stores SET (security_invoker = true);

-- ============================================
-- SEARCH AND UTILITY FUNCTIONS
-- ============================================

-- Create function to search countries with alphabetical ordering
CREATE OR REPLACE FUNCTION search_countries(search_term TEXT)
RETURNS TABLE (
    id UUID,
    name TEXT,
    code TEXT,
    phone_code TEXT,
    flag_emoji TEXT,
    is_active BOOLEAN
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.name, c.code, c.phone_code, c.flag_emoji, c.is_active
    FROM public.countries c
    WHERE c.is_active = true 
    AND (
        c.name ILIKE '%' || search_term || '%' OR
        c.code ILIKE '%' || search_term || '%' OR
        c.phone_code ILIKE '%' || search_term || '%'
    )
    ORDER BY c.name ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function to get stores by country
CREATE OR REPLACE FUNCTION get_stores_by_country(country_name TEXT)
RETURNS TABLE (
    id UUID,
    store_number TEXT,
    name TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    description TEXT,
    country TEXT,
    is_active BOOLEAN,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT s.id, s.store_number, s.name, s.address, s.phone, s.email, s.description, 
           s.country, s.is_active, s.created_by, s.created_at, s.updated_at
    FROM public.stores s
    WHERE s.country = country_name AND s.is_active = true
    ORDER BY s.name;
END;
$$ LANGUAGE plpgsql;

-- Create function to search stores
CREATE OR REPLACE FUNCTION search_stores(search_term TEXT)
RETURNS TABLE (
    id UUID,
    store_number TEXT,
    name TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    description TEXT,
    country TEXT,
    is_active BOOLEAN,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT s.id, s.store_number, s.name, s.address, s.phone, s.email, s.description, 
           s.country, s.is_active, s.created_by, s.created_at, s.updated_at
    FROM public.stores s
    WHERE s.is_active = true AND (
        s.name ILIKE '%' || search_term || '%' OR
        s.address ILIKE '%' || search_term || '%' OR
        s.description ILIKE '%' || search_term || '%' OR
        s.store_number ILIKE '%' || search_term || '%'
    )
    ORDER BY s.name;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- AUTH FUNCTIONS AND TRIGGERS
-- ============================================

-- Function to create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, country)
    VALUES (
        NEW.id, 
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'country'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================
-- SEED DATA
-- ============================================

-- Insert common countries
INSERT INTO countries (name, code, phone_code, flag_emoji, is_active) VALUES
('Afghanistan', 'AF', '93', '🇦🇫', true),
('Albania', 'AL', '355', '🇦🇱', true),
('Algeria', 'DZ', '213', '🇩🇿', true),
('Argentina', 'AR', '54', '🇦🇷', true),
('Australia', 'AU', '61', '🇦🇺', true),
('Austria', 'AT', '43', '🇦🇹', true),
('Bangladesh', 'BD', '880', '🇧🇩', true),
('Belgium', 'BE', '32', '🇧🇪', true),
('Brazil', 'BR', '55', '🇧🇷', true),
('Canada', 'CA', '1', '🇨🇦', true),
('Chile', 'CL', '56', '🇨🇱', true),
('China', 'CN', '86', '🇨🇳', true),
('Colombia', 'CO', '57', '🇨🇴', true),
('Denmark', 'DK', '45', '🇩🇰', true),
('Egypt', 'EG', '20', '🇪🇬', true),
('Finland', 'FI', '358', '🇫🇮', true),
('France', 'FR', '33', '🇫🇷', true),
('Germany', 'DE', '49', '🇩🇪', true),
('Ghana', 'GH', '233', '🇬🇭', true),
('Greece', 'GR', '30', '🇬🇷', true),
('India', 'IN', '91', '🇮🇳', true),
('Indonesia', 'ID', '62', '🇮🇩', true),
('Italy', 'IT', '39', '🇮🇹', true),
('Japan', 'JP', '81', '🇯🇵', true),
('Kenya', 'KE', '254', '🇰🇪', true),
('Malaysia', 'MY', '60', '🇲🇾', true),
('Mexico', 'MX', '52', '🇲🇽', true),
('Netherlands', 'NL', '31', '🇳🇱', true),
('New Zealand', 'NZ', '64', '🇳🇿', true),
('Nigeria', 'NG', '234', '🇳🇬', true),
('Norway', 'NO', '47', '🇳🇴', true),
('Pakistan', 'PK', '92', '🇵🇰', true),
('Philippines', 'PH', '63', '🇵🇭', true),
('Poland', 'PL', '48', '🇵🇱', true),
('Portugal', 'PT', '351', '🇵🇹', true),
('Russia', 'RU', '7', '🇷🇺', true),
('Saudi Arabia', 'SA', '966', '🇸🇦', true),
('Singapore', 'SG', '65', '🇸🇬', true),
('South Africa', 'ZA', '27', '🇿🇦', true),
('South Korea', 'KR', '82', '🇰🇷', true),
('Spain', 'ES', '34', '🇪🇸', true),
('Sweden', 'SE', '46', '🇸🇪', true),
('Switzerland', 'CH', '41', '🇨🇭', true),
('Thailand', 'TH', '66', '🇹🇭', true),
('Turkey', 'TR', '90', '🇹🇷', true),
('United Arab Emirates', 'AE', '971', '🇦🇪', true),
('United Kingdom', 'GB', '44', '🇬🇧', true),
('United States', 'US', '1', '🇺🇸', true),
('Vietnam', 'VN', '84', '🇻🇳', true);

-- ============================================
-- MIGRATION COMPLETE
-- ============================================

-- Add comments for documentation
COMMENT ON TABLE countries IS 'Countries lookup table with country codes and phone codes';
COMMENT ON TABLE profiles IS 'User profiles extending auth.users with additional information';
COMMENT ON TABLE stores IS 'Laundry stores with auto-generated store numbers';
COMMENT ON VIEW active_stores IS 'View showing only active stores';
