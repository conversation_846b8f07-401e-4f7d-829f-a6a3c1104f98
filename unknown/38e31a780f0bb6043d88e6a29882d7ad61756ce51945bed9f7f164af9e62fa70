-- ============================================
-- PAYMENTS SYSTEM MIGRATION
-- ============================================

-- Add sequence for invoice numbers starting from 1000001 (idempotent)
CREATE SEQUENCE IF NOT EXISTS public.invoice_number_seq
    START WITH 1000001
    INCREMENT BY 1
    NO CYCLE;

-- Recreate payments table if requested (DANGEROUS in production)
DROP TABLE IF EXISTS public.payments CASCADE;

-- Drop existing payment constraints if they exist (for migration updates)
-- This allows the migration to be re-run safely
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payments') THEN
        ALTER TABLE public.payments DROP CONSTRAINT IF EXISTS payment_cash_receipt;
        ALTER TABLE public.payments DROP CONSTRAINT IF EXISTS payment_mpesa_transaction;
    END IF;
END $$;

-- Create payments table for detailed payment records
CREATE TABLE public.payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE NOT NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    method TEXT CHECK (method IN (
        'cash', 'mpesa_till', 'mpesa_paybill', 'mpesa_send_money'
    )) NOT NULL,
    
    -- Receipt/Reference fields
    receipt_number TEXT, -- For cash payments and internal reference
    transaction_code TEXT, -- For M-Pesa transactions (must be unique)
    channel_target TEXT, -- Till number, Paybill number, or recipient phone
    payer_phone TEXT, -- Optional payer phone number
    notes TEXT,
    
    -- Audit fields
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Constraints
    -- Removed cash receipt requirement - now optional for all methods
    CONSTRAINT payment_mpesa_transaction CHECK (
        (method LIKE 'mpesa_%' AND transaction_code IS NOT NULL) OR method NOT LIKE 'mpesa_%'
    )
);

-- Create indexes for payments (idempotent)
CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON public.payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON public.payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_method ON public.payments(method);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON public.payments(created_at);

-- Unique constraint for M-Pesa transaction codes (only when not null)
CREATE UNIQUE INDEX IF NOT EXISTS idx_payments_transaction_code_unique 
ON public.payments(transaction_code) 
WHERE transaction_code IS NOT NULL;

-- ============================================
-- UPDATE INVOICES TABLE
-- ============================================

-- Add auto-incrementing invoice number using sequence
-- Add column if not exists (idempotent)
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS invoice_sequence_number INTEGER;

-- Update existing invoices to have sequence numbers
UPDATE invoices 
SET invoice_sequence_number = nextval('invoice_number_seq')
WHERE invoice_sequence_number IS NULL;

-- Make invoice_sequence_number required for new records
ALTER TABLE invoices 
ALTER COLUMN invoice_sequence_number SET NOT NULL;

-- Add unique constraint on invoice sequence number
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'unique_invoice_sequence_number'
    ) THEN
        ALTER TABLE invoices 
        ADD CONSTRAINT unique_invoice_sequence_number UNIQUE (invoice_sequence_number);
    END IF;
END $$;

-- Ensure the sequence is set at least to the current max invoice_sequence_number
SELECT setval(
    'public.invoice_number_seq',
    GREATEST(
        COALESCE((SELECT MAX(invoice_sequence_number) FROM public.invoices), 1000000),
        (SELECT last_value FROM public.invoice_number_seq)
    ),
    true
);

-- ============================================
-- FUNCTIONS AND TRIGGERS
-- ============================================

-- Function to generate invoice number with sequence
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    -- Only generate if not provided
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_sequence_number := nextval('public.invoice_number_seq');
        NEW.invoice_number := 'INV:' || NEW.invoice_sequence_number::TEXT; -- with prefix
    END IF;
    
    -- If invoice_sequence_number is not set but invoice_number is provided
    IF NEW.invoice_sequence_number IS NULL THEN
        NEW.invoice_sequence_number := nextval('public.invoice_number_seq');
        -- Ensure invoice_number matches sequence with prefix
        NEW.invoice_number := 'INV:' || NEW.invoice_sequence_number::TEXT;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for invoice number generation
DROP TRIGGER IF EXISTS trigger_generate_invoice_number ON invoices;
CREATE TRIGGER trigger_generate_invoice_number
    BEFORE INSERT ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION generate_invoice_number();

-- Function to update invoice payment status based on payments
CREATE OR REPLACE FUNCTION update_invoice_payment_status()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    invoice_rec RECORD;
    total_paid DECIMAL(10,2);
    new_status TEXT;
    v_payment_method TEXT;
BEGIN
    -- Fetch the target invoice first
    SELECT *
    INTO invoice_rec
    FROM public.invoices
    WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id);

    -- If no invoice found, do nothing
    IF invoice_rec IS NULL THEN
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- Calculate total paid for the invoice
    SELECT COALESCE(SUM(amount), 0)
    INTO total_paid
    FROM public.payments
    WHERE invoice_id = invoice_rec.id;
    
    -- Determine new payment status
    IF total_paid = 0 THEN
        new_status := 'pending';
        v_payment_method := NULL;
    ELSIF total_paid >= invoice_rec.total_amount THEN
        new_status := 'paid';
        -- Get the most recent payment method for paid invoices
        SELECT method INTO v_payment_method
        FROM public.payments
        WHERE invoice_id = invoice_rec.id
        ORDER BY created_at DESC
        LIMIT 1;
    ELSE
        new_status := 'partial';
        v_payment_method := 'multiple';
    END IF;
    
    -- Update invoice
    UPDATE public.invoices
    SET 
        payment_status = new_status,
        payment_method = v_payment_method,
        paid_at = CASE 
            WHEN new_status = 'paid' THEN TIMEZONE('utc'::text, NOW())
            ELSE NULL 
        END,
        updated_at = TIMEZONE('utc'::text, NOW())
    WHERE id = invoice_rec.id;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for payment status updates
DROP TRIGGER IF EXISTS trigger_update_payment_status_insert ON payments;
CREATE TRIGGER trigger_update_payment_status_insert
    AFTER INSERT ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_invoice_payment_status();

DROP TRIGGER IF EXISTS trigger_update_payment_status_update ON payments;
CREATE TRIGGER trigger_update_payment_status_update
    AFTER UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_invoice_payment_status();

DROP TRIGGER IF EXISTS trigger_update_payment_status_delete ON payments;
CREATE TRIGGER trigger_update_payment_status_delete
    AFTER DELETE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_invoice_payment_status();

-- ============================================
-- HELPER FUNCTIONS
-- ============================================

-- Drop and recreate function to change return type (includes ALL invoice fields)
DROP FUNCTION IF EXISTS get_invoice_with_payments(UUID);

CREATE OR REPLACE FUNCTION get_invoice_with_payments(invoice_uuid UUID)
RETURNS TABLE (
    id UUID,
    invoice_number TEXT,
    invoice_sequence_number INTEGER,
    order_id UUID,
    user_id UUID,
    subtotal DECIMAL(10,2),
    tax_rate DECIMAL(5,2),
    tax_amount DECIMAL(10,2),
    delivery_fee DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    payment_status TEXT,
    payment_method TEXT,
    paid_at TIMESTAMP WITH TIME ZONE,
    invoice_date DATE,
    due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_paid DECIMAL(10,2),
    outstanding_amount DECIMAL(10,2),
    payments_count INTEGER
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id,
        i.invoice_number,
        i.invoice_sequence_number,
        i.order_id,
        i.user_id,
        i.subtotal,
        i.tax_rate,
        i.tax_amount,
        i.delivery_fee,
        i.discount_amount,
        i.total_amount,
        i.payment_status,
        i.payment_method,
        i.paid_at,
        i.invoice_date,
        i.due_date,
        i.created_at,
        i.updated_at,
        COALESCE(SUM(p.amount), 0) as total_paid,
        i.total_amount - COALESCE(SUM(p.amount), 0) as outstanding_amount,
        COUNT(p.id)::INTEGER as payments_count
    FROM public.invoices i
    LEFT JOIN public.payments p ON p.invoice_id = i.id
    WHERE i.id = invoice_uuid
    GROUP BY i.id, i.invoice_number, i.invoice_sequence_number, i.order_id, 
             i.user_id, i.subtotal, i.tax_rate, i.tax_amount, i.delivery_fee,
             i.discount_amount, i.total_amount, i.payment_status, i.payment_method, 
             i.paid_at, i.invoice_date, i.due_date, i.created_at, i.updated_at;
END;
$$ LANGUAGE plpgsql;

-- Function to get payments for an invoice
DROP FUNCTION IF EXISTS get_invoice_payments(UUID);

CREATE OR REPLACE FUNCTION get_invoice_payments(invoice_uuid UUID)
RETURNS TABLE (
    id UUID,
    invoice_id UUID,
    order_id UUID,
    amount DECIMAL(10,2),
    method TEXT,
    receipt_number TEXT,
    transaction_code TEXT,
    channel_target TEXT,
    payer_phone TEXT,
    notes TEXT,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.invoice_id,
        p.order_id,
        p.amount,
        p.method,
        p.receipt_number,
        p.transaction_code,
        p.channel_target,
        p.payer_phone,
        p.notes,
        p.created_by,
        p.created_at,
        p.updated_at
    FROM public.payments p
    WHERE p.invoice_id = invoice_uuid
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- UPDATE EXISTING ORDER FUNCTIONS
-- ============================================

-- Create order only (no invoice yet)
CREATE OR REPLACE FUNCTION create_order_only(
    p_user_id UUID,
    p_store_id UUID,
    p_service_id UUID,
    p_pickup_address_id UUID DEFAULT NULL,
    p_delivery_address_id UUID DEFAULT NULL,
    p_pickup_date DATE DEFAULT NULL,
    p_pickup_time_slot TEXT DEFAULT NULL,
    p_delivery_date DATE DEFAULT NULL,
    p_delivery_time_slot TEXT DEFAULT NULL,
    p_subtotal DECIMAL(10,2) DEFAULT 0.00,
    p_tax_amount DECIMAL(10,2) DEFAULT 0.00,
    p_delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    p_discount_amount DECIMAL(10,2) DEFAULT 0.00,
    p_total_amount DECIMAL(10,2) DEFAULT 0.00,
    p_special_instructions TEXT DEFAULT NULL
)
RETURNS TABLE (
    order_id UUID,
    order_number TEXT
)
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    new_order_id UUID;
    new_order_number TEXT;
BEGIN
    -- Create the order (order_number will be auto-generated by trigger)
    INSERT INTO public.orders (
        user_id, store_id, service_id, pickup_address_id, delivery_address_id,
        pickup_date, pickup_time_slot, delivery_date, delivery_time_slot,
        subtotal, tax_amount, delivery_fee, discount_amount, total_amount,
        special_instructions, status
        -- order_number field omitted to let trigger handle it
    ) VALUES (
        p_user_id, p_store_id, p_service_id, p_pickup_address_id, p_delivery_address_id,
        p_pickup_date, p_pickup_time_slot, p_delivery_date, p_delivery_time_slot,
        p_subtotal, p_tax_amount, p_delivery_fee, p_discount_amount, p_total_amount,
        p_special_instructions, 'pending'
        -- No value for order_number - let trigger handle it
    )
    RETURNING orders.id, orders.order_number INTO new_order_id, new_order_number;
    
    RETURN QUERY SELECT 
        new_order_id AS order_id, 
        new_order_number AS order_number;
END;
$$ LANGUAGE plpgsql;

-- Create invoice for existing order when payment is received
CREATE OR REPLACE FUNCTION create_invoice_for_order(
    p_order_id UUID,
    p_payment_method TEXT DEFAULT NULL
)
RETURNS TABLE (
    invoice_id UUID,
    invoice_number TEXT
)
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    new_invoice_id UUID;
    new_invoice_number TEXT;
    order_data RECORD;
BEGIN
    -- Get order details
    SELECT user_id, subtotal, tax_amount, delivery_fee, discount_amount, total_amount
    INTO order_data
    FROM public.orders
    WHERE id = p_order_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Order with id % not found', p_order_id;
    END IF;
    
    -- Check if invoice already exists for this order
    IF EXISTS (SELECT 1 FROM public.invoices WHERE order_id = p_order_id) THEN
        RAISE EXCEPTION 'Invoice already exists for order %', p_order_id;
    END IF;
    
    -- Create the invoice (invoice_number will be auto-generated by trigger)
    INSERT INTO public.invoices (
        order_id, user_id, subtotal, tax_amount, delivery_fee, 
        discount_amount, total_amount, payment_status, payment_method
        -- invoice_number field omitted to let trigger handle it
    ) VALUES (
        p_order_id, order_data.user_id, order_data.subtotal, order_data.tax_amount, 
        order_data.delivery_fee, order_data.discount_amount, order_data.total_amount, 
        'pending', p_payment_method
        -- No value for invoice_number - let trigger handle it
    )
    RETURNING invoices.id, invoices.invoice_number INTO new_invoice_id, new_invoice_number;
    
    RETURN QUERY SELECT 
        new_invoice_id AS invoice_id, 
        new_invoice_number AS invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Keep the original function for backward compatibility but rename it
CREATE OR REPLACE FUNCTION create_order_with_invoice(
    p_user_id UUID,
    p_store_id UUID,
    p_service_id UUID,
    p_pickup_address_id UUID DEFAULT NULL,
    p_delivery_address_id UUID DEFAULT NULL,
    p_pickup_date DATE DEFAULT NULL,
    p_pickup_time_slot TEXT DEFAULT NULL,
    p_delivery_date DATE DEFAULT NULL,
    p_delivery_time_slot TEXT DEFAULT NULL,
    p_subtotal DECIMAL(10,2) DEFAULT 0.00,
    p_tax_amount DECIMAL(10,2) DEFAULT 0.00,
    p_delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    p_discount_amount DECIMAL(10,2) DEFAULT 0.00,
    p_total_amount DECIMAL(10,2) DEFAULT 0.00,
    p_special_instructions TEXT DEFAULT NULL
)
RETURNS TABLE (
    order_id UUID,
    order_number TEXT,
    invoice_id UUID,
    invoice_number TEXT
)
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    order_result RECORD;
    invoice_result RECORD;
BEGIN
    -- Create order first
    SELECT * INTO order_result FROM create_order_only(
        p_user_id, p_store_id, p_service_id, p_pickup_address_id, p_delivery_address_id,
        p_pickup_date, p_pickup_time_slot, p_delivery_date, p_delivery_time_slot,
        p_subtotal, p_tax_amount, p_delivery_fee, p_discount_amount, p_total_amount,
        p_special_instructions
    );
    
    -- Create invoice for the order
    SELECT * INTO invoice_result FROM create_invoice_for_order(order_result.order_id);
    
    RETURN QUERY SELECT 
        order_result.order_id AS order_id, 
        order_result.order_number AS order_number, 
        invoice_result.invoice_id AS invoice_id, 
        invoice_result.invoice_number AS invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Process payment: Create invoice (if not exists) and optionally update order status
CREATE OR REPLACE FUNCTION process_payment_for_order(
    p_order_id UUID,
    p_payment_method TEXT DEFAULT NULL,
    p_payment_amount DECIMAL(10,2) DEFAULT NULL,
    p_update_order_status TEXT DEFAULT NULL,
    p_payment_notes TEXT DEFAULT NULL
)
RETURNS TABLE (
    invoice_id UUID,
    invoice_number TEXT,
    payment_id UUID,
    order_updated BOOLEAN
)
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    new_invoice_id UUID;
    new_invoice_number TEXT;
    new_payment_id UUID;
    order_updated BOOLEAN := FALSE;
    order_data RECORD;
    existing_invoice_id UUID;
BEGIN
    -- Get order details
    SELECT user_id, subtotal, tax_amount, delivery_fee, discount_amount, total_amount, status
    INTO order_data
    FROM public.orders
    WHERE id = p_order_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Order with id % not found', p_order_id;
    END IF;
    
    -- Check if invoice already exists
    SELECT id INTO existing_invoice_id
    FROM public.invoices
    WHERE order_id = p_order_id;
    
    IF existing_invoice_id IS NULL THEN
        -- Create new invoice
        INSERT INTO public.invoices (
            order_id, user_id, subtotal, tax_amount, delivery_fee, 
            discount_amount, total_amount, payment_status, payment_method
        ) VALUES (
            p_order_id, order_data.user_id, order_data.subtotal, order_data.tax_amount, 
            order_data.delivery_fee, order_data.discount_amount, order_data.total_amount, 
            'pending', p_payment_method
        )
        RETURNING invoices.id, invoices.invoice_number INTO new_invoice_id, new_invoice_number;
    ELSE
        -- Use existing invoice
        SELECT id, invoice_number INTO new_invoice_id, new_invoice_number
        FROM public.invoices
        WHERE id = existing_invoice_id;
    END IF;
    
    -- Create payment record if payment details provided
    IF p_payment_amount IS NOT NULL AND p_payment_amount > 0 THEN
        INSERT INTO public.payments (
            invoice_id, order_id, amount, method, notes, created_by
        ) VALUES (
            new_invoice_id, p_order_id, p_payment_amount, p_payment_method, 
            p_payment_notes, (SELECT auth.uid())
        )
        RETURNING id INTO new_payment_id;
        
        -- Update invoice payment status if fully paid
        IF p_payment_amount >= order_data.total_amount THEN
            UPDATE public.invoices 
            SET payment_status = 'paid', paid_at = NOW()
            WHERE id = new_invoice_id;
        ELSE
            UPDATE public.invoices 
            SET payment_status = 'partial'
            WHERE id = new_invoice_id;
        END IF;
    END IF;
    
    -- Update order status if requested
    IF p_update_order_status IS NOT NULL AND p_update_order_status != order_data.status THEN
        UPDATE public.orders 
        SET status = p_update_order_status
        WHERE id = p_order_id;
        
        -- Log status change
        INSERT INTO public.order_status_log (
            order_id, old_status, new_status, changed_by, notes
        ) VALUES (
            p_order_id, order_data.status, p_update_order_status, 
            (SELECT auth.uid()), 'Status updated during payment processing'
        );
        
        order_updated := TRUE;
    END IF;
    
    RETURN QUERY SELECT 
        new_invoice_id AS invoice_id, 
        new_invoice_number AS invoice_number,
        new_payment_id AS payment_id,
        order_updated AS order_updated;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STRICT GUARD: Prevent delivered/completed if invoice not paid
-- ============================================

CREATE OR REPLACE FUNCTION guard_paid_before_delivery()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    inv_status TEXT;
    inv_id UUID;
BEGIN
    -- Only guard when status is changing to delivered or completed
    IF TG_OP = 'UPDATE' AND NEW.status IN ('delivered','completed') AND (OLD.status IS DISTINCT FROM NEW.status) THEN
        -- Fetch invoice for the order
        SELECT id, payment_status INTO inv_id, inv_status
        FROM public.invoices
        WHERE order_id = NEW.id;

        -- If invoice is missing or not paid, block
        IF inv_id IS NULL OR inv_status IS DISTINCT FROM 'paid' THEN
            RAISE EXCEPTION 'Invoice must be paid before marking order % as %', NEW.order_number, NEW.status
            USING ERRCODE = 'check_violation';
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_guard_paid_before_delivery ON orders;
CREATE TRIGGER trigger_guard_paid_before_delivery
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION guard_paid_before_delivery();

-- Grant necessary permissions
GRANT USAGE, SELECT ON SEQUENCE invoice_number_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE order_number_seq TO authenticated;
GRANT ALL ON TABLE payments TO authenticated;
GRANT ALL ON TABLE orders TO authenticated;
GRANT ALL ON TABLE invoices TO authenticated;
GRANT ALL ON TABLE order_items TO authenticated;
GRANT EXECUTE ON FUNCTION generate_invoice_number() TO authenticated;
GRANT EXECUTE ON FUNCTION generate_order_number() TO authenticated;
GRANT EXECUTE ON FUNCTION update_invoice_payment_status() TO authenticated;
GRANT EXECUTE ON FUNCTION get_invoice_with_payments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_invoice_payments(UUID) TO authenticated;

-- ============================================
-- ENHANCED INVOICE FUNCTIONS WITH SECURITY DEFINER
-- ============================================

-- Drop existing function first to avoid return type conflicts
DROP FUNCTION IF EXISTS get_invoice_with_payments(UUID);

-- Enhanced get_invoice_with_payments function with proper security
CREATE OR REPLACE FUNCTION get_invoice_with_payments(invoice_uuid UUID)
RETURNS TABLE (
    id UUID,
    invoice_number TEXT,
    invoice_sequence_number INTEGER,
    order_id UUID,
    user_id UUID,
    subtotal DECIMAL(10,2),
    tax_rate DECIMAL(5,2),
    tax_amount DECIMAL(10,2),
    delivery_fee DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    payment_status TEXT,
    payment_method TEXT,
    paid_at TIMESTAMP WITH TIME ZONE,
    invoice_date DATE,
    due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_paid DECIMAL(10,2),
    outstanding_amount DECIMAL(10,2),
    payments_count INTEGER
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT 
        i.id,
        i.invoice_number,
        i.invoice_sequence_number,
        i.order_id,
        i.user_id,
        i.subtotal,
        i.tax_rate,
        i.tax_amount,
        i.delivery_fee,
        i.discount_amount,
        i.total_amount,
        i.payment_status,
        i.payment_method,
        i.paid_at,
        i.invoice_date,
        i.due_date,
        i.created_at,
        i.updated_at,
        COALESCE(SUM(p.amount), 0) as total_paid,
        i.total_amount - COALESCE(SUM(p.amount), 0) as outstanding_amount,
        COUNT(p.id)::INTEGER as payments_count
    FROM invoices i
    LEFT JOIN payments p ON p.invoice_id = i.id
    WHERE i.id = invoice_uuid
    GROUP BY i.id, i.invoice_number, i.invoice_sequence_number, i.order_id, 
             i.user_id, i.subtotal, i.tax_rate, i.tax_amount, i.delivery_fee,
             i.discount_amount, i.total_amount, i.payment_status, i.payment_method, 
             i.paid_at, i.invoice_date, i.due_date, i.created_at, i.updated_at;
$$;

-- Drop existing function first to avoid return type conflicts  
DROP FUNCTION IF EXISTS get_invoice_payments(UUID);

-- Enhanced get_invoice_payments function with proper security
CREATE OR REPLACE FUNCTION get_invoice_payments(invoice_uuid UUID)
RETURNS TABLE (
    id UUID,
    invoice_id UUID,
    order_id UUID,
    amount DECIMAL(10,2),
    method TEXT,
    receipt_number TEXT,
    transaction_code TEXT,
    channel_target TEXT,
    payer_phone TEXT,
    notes TEXT,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT 
        p.id,
        p.invoice_id,
        p.order_id,
        p.amount,
        p.method,
        p.receipt_number,
        p.transaction_code,
        p.channel_target,
        p.payer_phone,
        p.notes,
        p.created_by,
        p.created_at,
        p.updated_at
    FROM payments p
    WHERE p.invoice_id = invoice_uuid
    ORDER BY p.created_at DESC;
$$;
GRANT EXECUTE ON FUNCTION create_order_with_invoice TO authenticated;
GRANT EXECUTE ON FUNCTION create_order_only TO authenticated;
GRANT EXECUTE ON FUNCTION create_invoice_for_order TO authenticated;
GRANT EXECUTE ON FUNCTION process_payment_for_order TO authenticated;

-- ============================================
-- UPDATE EXISTING DATA
-- ============================================

-- Create invoices for existing orders that don't have them
INSERT INTO invoices (
    order_id, 
    user_id, 
    subtotal, 
    tax_amount, 
    delivery_fee, 
    discount_amount, 
    total_amount,
    payment_status,
    invoice_date
)
SELECT 
    o.id as order_id,
    o.user_id,
    o.subtotal,
    o.tax_amount,
    o.delivery_fee,
    o.discount_amount,
    o.total_amount,
    CASE 
        WHEN o.status IN ('delivered', 'completed') THEN 'paid'
        ELSE 'pending'
    END as payment_status,
    o.created_at::DATE as invoice_date
FROM orders o
LEFT JOIN invoices i ON i.order_id = o.id
WHERE i.id IS NULL;

-- Optional: Normalize existing invoice_number values to prefixed format 'INV:<sequence>'
UPDATE invoices
SET invoice_number = 'INV:' || invoice_sequence_number::TEXT
WHERE invoice_number IS DISTINCT FROM ('INV:' || invoice_sequence_number::TEXT);

-- ============================================
-- SECURITY: Enable RLS and create policies for payments
-- ============================================

ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if any to make migration idempotent
DROP POLICY IF EXISTS payments_select_authenticated ON public.payments;
DROP POLICY IF EXISTS payments_insert_authenticated ON public.payments;
DROP POLICY IF EXISTS payments_update_own ON public.payments;
DROP POLICY IF EXISTS payments_delete_own ON public.payments;

-- Allow authenticated users to read payments (broad, matches current app behavior)
CREATE POLICY payments_select_authenticated
ON public.payments
FOR SELECT
TO authenticated
USING (true);

-- Allow authenticated users to insert payments for themselves
CREATE POLICY payments_insert_authenticated
ON public.payments
FOR INSERT
TO authenticated
WITH CHECK (created_by = (select auth.uid()));

-- Allow creators to update their own payments
CREATE POLICY payments_update_own
ON public.payments
FOR UPDATE
TO authenticated
USING (created_by = (select auth.uid()))
WITH CHECK (created_by = (select auth.uid()));

-- Allow creators to delete their own payments
CREATE POLICY payments_delete_own
ON public.payments
FOR DELETE
TO authenticated
USING (created_by = (select auth.uid()));