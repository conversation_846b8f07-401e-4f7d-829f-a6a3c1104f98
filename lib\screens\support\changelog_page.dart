import 'package:flutter/material.dart';

class ChangelogPage extends StatelessWidget {
  const ChangelogPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Change Log'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Banner (unique styling)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.blue.shade600,
                    Colors.blue.shade400,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.25),
                    blurRadius: 18,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.change_circle_rounded,
                      color: Colors.white,
                      size: 36,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Change Log',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w800,
                              ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Track all updates, fixes and improvements',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(color: Colors.white.withValues(alpha: 0.9)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Version History
            Expanded(
              child: ListView(
                children: [
                  _buildVersionCard(
                    version: '1.0.102',
                    releaseDate: DateTime(2025, 1, 15),
                    status: 'Latest',
                    statusColor: Colors.green,
                    isLatest: true,
                    changes: [
                      '🔧 FIXED: Payment collection error due to missing database column',
                      '🔧 FIXED: Invoice service fallback for schema compatibility',
                      '🔧 FIXED: get_invoice_with_payments RPC function errors',
                      '🔧 FIXED: Payment processing when invoice_sequence_number missing',
                      '🚀 ENHANCED: Robust error handling in payment collection',
                      '🚀 ENHANCED: Database schema drift compatibility',
                      '📱 IMPROVED: Payment flow reliability and stability',
                      '📱 IMPROVED: Error recovery for database migration issues',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildVersionCard(
                    version: '1.0.101',
                    releaseDate: DateTime(2025, 8, 10),
                    status: 'Previous',
                    statusColor: Colors.blue,
                    isLatest: false,
                    changes: [
                      '✨ ADDED: Comprehensive Reports & Analytics system',
                      '✨ ADDED: Quick Overview dashboard with real-time stats',
                      '✨ ADDED: Sales & Revenue reporting with trends',
                      '✨ ADDED: Orders Analytics with performance metrics',
                      '✨ ADDED: Customer Insights with behavior analysis',
                      '✨ ADDED: Payment Methods breakdown reports',
                      '✨ ADDED: Invoice PDF generation and preview',
                      '✨ ADDED: PDF saving to Downloads folder',
                      '✨ ADDED: Invoice printing functionality',
                      '✨ ADDED: Payment recording system',
                      '✨ ADDED: Order payment status tracking',
                      '✨ ADDED: Currency support and selection',
                      '🔧 FIXED: Payment method ambiguity in database',
                      '🔧 FIXED: Invoice payments null type casting errors',
                      '🔧 FIXED: RenderFlex overflow issues in UI',
                      '🔧 FIXED: Quick Overview cards layout problems',
                      '🔧 FIXED: Report cards text visibility issues',
                      '🚀 ENHANCED: Paid orders with view/print options',
                      '🚀 ENHANCED: Modern gradient UI design system',
                      '🚀 ENHANCED: Card layouts with better shadows',
                      '🚀 ENHANCED: Text readability and contrast',
                      '🚀 ENHANCED: Touch-friendly interface elements',
                      '📱 IMPROVED: App navigation with drawer integration',
                      '📱 IMPROVED: File system permissions handling',
                      '📱 IMPROVED: Android FileProvider configuration',
                      '📱 IMPROVED: URL launching for file viewing',
                      '📱 IMPROVED: Overall app performance and stability',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildVersionCard(
                    version: '0.0.100',
                    releaseDate: DateTime(2025, 1, 10),
                    status: 'Previous',
                    statusColor: Colors.blue,
                    changes: [
                      '🎨 Implemented complete dark/light theme system',
                      '⚙️ Added comprehensive settings page',
                      '🔐 Integrated biometric authentication',
                      '📱 Improved UI/UX across all screens',
                      '🔄 Enhanced state management with Provider',
                      '💾 Added theme persistence with SharedPreferences',
                      '🏗️ Set up foundation for future features',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildVersionCard(
                    version: '0.0.50',
                    releaseDate: DateTime(2024, 12, 10),
                    status: 'Beta',
                    statusColor: Colors.orange,
                    changes: [
                      '🚀 Initial app release',
                      '🔐 Basic authentication system',
                      '📊 Dashboard foundation',
                      '👤 User profile management',
                      '🎯 Core app structure established',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildVersionCard(
                    version: '0.0.1',
                    releaseDate: DateTime(2024, 11, 10),
                    status: 'Alpha',
                    statusColor: Colors.red,
                    changes: [
                      '🎯 Project initialization',
                      '🏗️ Basic app architecture',
                      '📱 Flutter setup and configuration',
                      '🔧 Development environment setup',
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Coming Soon Section
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.rocket_launch,
                                color: Colors.blue,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'Coming Next',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Upcoming features in development:',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 12),
                          _buildUpcomingFeature('🧺 Advanced laundry workflow management'),
                          _buildUpcomingFeature('💳 Online payment gateway integration'),
                          _buildUpcomingFeature('📊 Advanced analytics with charts'),
                          _buildUpcomingFeature('📱 Push notifications for orders'),
                          _buildUpcomingFeature('🌐 Multi-language support'),
                          _buildUpcomingFeature('☁️ Cloud backup and sync'),
                          _buildUpcomingFeature('📧 Email invoice delivery'),
                          _buildUpcomingFeature('🎨 Custom branding options'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVersionCard({
    required String version,
    required DateTime releaseDate,
    required String status,
    required Color statusColor,
    required List<String> changes,
    bool isLatest = false,
  }) {
    final bool releasedWithin7Days = DateTime.now().difference(releaseDate).inDays <= 7;
    final String date = '${releaseDate.day.toString().padLeft(2, '0')}/${releaseDate.month.toString().padLeft(2, '0')}/${releaseDate.year}';
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    'Version $version',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                                    Wrap(
                      spacing: 8,
                      children: [
                        if (releasedWithin7Days)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.purple.withValues(alpha: 0.12),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.flash_on, size: 14, color: Colors.purple),
                                SizedBox(width: 4),
                                Text('New', style: TextStyle(color: Colors.purple, fontWeight: FontWeight.w700, fontSize: 12)),
                              ],
                            ),
                          ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: (isLatest ? Colors.green : statusColor).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: (isLatest ? Colors.green : statusColor).withValues(alpha: 0.3)),
                          ),
                          child: Text(
                            isLatest ? 'Latest' : status,
                            style: TextStyle(
                              color: isLatest ? Colors.green : statusColor,
                              fontWeight: FontWeight.w700,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              date,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            ...changes.map((change) => Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                change,
                style: const TextStyle(fontSize: 15),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingFeature(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(fontSize: 15),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }
}
