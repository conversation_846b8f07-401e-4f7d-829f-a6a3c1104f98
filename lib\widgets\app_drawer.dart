import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../services/staff_service.dart';
import '../screens/auth/login_page.dart';
import '../screens/settings_page.dart';
import '../screens/profile_page.dart';
import '../screens/stores/stores_page.dart';
import '../screens/orders/place_order_page.dart';
import '../screens/orders/order_history_page.dart';
import '../screens/services/services_page.dart';
import '../screens/items/garments_page.dart';
import '../screens/items/categories_page.dart';
import '../screens/customers/customer_list_page.dart';
import '../screens/staff/staff_list_page.dart';
import '../screens/payments/select_order_for_payment_page.dart';
import '../screens/reports/reports_page.dart';
import '../screens/expenses/expenses_page.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  bool _isCurrentUserAdmin = false;
  bool _isLoadingAdminStatus = true;
  bool _isItiemExpanded = false;
  bool _isOrdersExpanded = false;

  @override
  void initState() {
    super.initState();
    _checkAdminStatus();
  }

  void _checkAdminStatus() async {
    try {
      final staffService = StaffService(supabase: Supabase.instance.client);
      final isStaff = await staffService.isCurrentUserStaff();
      if (mounted) {
        setState(() {
          _isCurrentUserAdmin = isStaff; // Any staff member can access admin features
          _isLoadingAdminStatus = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCurrentUserAdmin = false;
          _isLoadingAdminStatus = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = AuthService(supabase: Supabase.instance.client);
    final user = authService.currentUser;
    final userDisplayName = authService.userDisplayName;
    final userEmail = user?.email ?? 'No email';

    return Drawer(
      child: Column(
        children: [
          // Header with user info
          UserAccountsDrawerHeader(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue, Colors.lightBlue],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            accountName: Row(
              children: [
                Text(
                  userDisplayName,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                if (!_isLoadingAdminStatus && _isCurrentUserAdmin) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.orange.withValues(alpha: 0.5)),
                    ),
                    child: const Text(
                      'ADMIN',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            accountEmail: Text(userEmail),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                userDisplayName.isNotEmpty
                    ? userDisplayName[0].toUpperCase()
                    : 'U',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
          ),

          // Navigation items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Dashboard
                _buildDrawerItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                ),

                // Stores
                _buildDrawerItem(
                  icon: Icons.store,
                  title: 'Stores',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const StoresPage()),
                    );
                  },
                ),

                // Itiem submenu
                ExpansionTile(
                  leading: const Icon(Icons.checkroom, color: Colors.blue),
                  title: const Text('Garments & Services  Management'),
                  initiallyExpanded: _isItiemExpanded,
                  onExpansionChanged: (expanded) {
                    setState(() {
                      _isItiemExpanded = expanded;
                    });
                  },
                  children: [
                    _buildSubDrawerItem(
                      icon: Icons.inventory_2,
                      title: 'Itiem Management',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => const GarmentsPage()),
                        );
                      },
                    ),
                    _buildSubDrawerItem(
                      icon: Icons.category,
                      title: 'Category Management',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => const CategoriesPage()),
                        );
                      },
                    ),
                    _buildSubDrawerItem(
                      icon: Icons.miscellaneous_services,
                      title: 'Service Management',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => const ServicesPage()),
                        );
                      },
                    ),
                  ],
                ),

                // Orders submenu
                ExpansionTile(
                  leading: const Icon(Icons.receipt_long, color: Colors.blue),
                  title: const Text('Orders'),
                  initiallyExpanded: _isOrdersExpanded,
                  onExpansionChanged: (expanded) {
                    setState(() {
                      _isOrdersExpanded = expanded;
                    });
                  },
                  children: [
                    _buildSubDrawerItem(
                      icon: Icons.history,
                      title: 'All Orders',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const OrderHistoryPage(),
                          ),
                        );
                      },
                    ),
                    _buildSubDrawerItem(
                      icon: Icons.add_shopping_cart,
                      title: 'New Order',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PlaceOrderPage(),
                          ),
                        );
                      },
                    ),
                    _buildSubDrawerItem(
                      icon: Icons.track_changes,
                      title: 'Track Orders',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const OrderHistoryPage(showActiveOnly: true),
                          ),
                        );
                      },
                    ),
                    _buildSubDrawerItem(
                      icon: Icons.payment,
                      title: 'Receive Payment',
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SelectOrderForPaymentPage(),
                          ),
                        );
                      },
                    ),
                  ],
                ),

                // Services menu item removed - now under Itiem submenu

                // Staff Management
                _buildDrawerItem(
                  icon: Icons.badge,
                  title: 'Staff Management',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const StaffListPage()),
                    );
                  },
                ),

                // Customer -> Customer Management
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'Customer Management',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const CustomerListPage()),
                    );
                  },
                ),

                // Expenses
                _buildDrawerItem(
                  icon: Icons.money_off,
                  title: 'Expenses',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const ExpensesPage()),
                    );
                  },
                ),

                // Reports & Analytics
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Reports & Analytics',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const ReportsPage()),
                    );
                  },
                ),

                const Divider(),

                // Profile
                _buildDrawerItem(
                  icon: Icons.person,
                  title: 'Profile',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const ProfilePage()),
                    );
                  },
                ),

                // Settings
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'Settings',
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const SettingsPage()),
                    );
                  },
                ),

                const SizedBox(height: 20),
                Center(
                  child: Text(
                    'More features coming soon!',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Logout button at bottom
          Container(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showLogoutDialog(context, authService),
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }

  Widget _buildSubDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 24,
        alignment: Alignment.center,
        child: Icon(icon, color: Colors.blue[700], size: 22),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 15,
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.only(left: 56, right: 24, top: 4, bottom: 4),
      dense: true,
    );
  }

  void _showLogoutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(ctx).pop(); // Close dialog
              Navigator.of(context).pop(); // Close drawer

              // Perform instant logout
              try {
                await authService.signOut();

                // Navigate to login page immediately
                if (context.mounted) {
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const LoginPage()),
                    (route) => false,
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error logging out: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
