import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/expense_model.dart';
import '../../models/custom_expense_category_model.dart';
import '../../models/payment_model.dart';
import '../../models/store_model.dart';
import '../../services/expense_service.dart';
import '../../services/custom_expense_category_service.dart';
import '../../services/store_service.dart';
import '../../services/currency_service.dart';

class ExpenseFormPage extends StatefulWidget {
  final Expense? initial;
  
  const ExpenseFormPage({super.key, this.initial});

  @override
  State<ExpenseFormPage> createState() => _ExpenseFormPageState();
}

class _ExpenseFormPageState extends State<ExpenseFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _expenseService = ExpenseService(supabase: Supabase.instance.client);
  final _customCategoryService = CustomExpenseCategoryService(supabase: Supabase.instance.client);
  final _storeService = StoreService(supabase: Supabase.instance.client);

  // Controllers
  final _amountController = TextEditingController();
  final _vendorController = TextEditingController();
  final _referenceController = TextEditingController();
  final _noteController = TextEditingController();

  // Form fields
  ExpenseCategory? _selectedCategory;
  CustomExpenseCategory? _selectedCustomCategory;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  DateTime _selectedDate = DateTime.now();
  Store? _selectedStore;
  bool _isMultiStore = false;
  List<Map<String, dynamic>> _allocations = [];

  // Data
  List<CustomExpenseCategory> _customCategories = [];
  List<Store> _stores = [];
  bool _loading = false;
  bool _loadingData = true;
  String _currencySymbol = r'$';

  @override
  void initState() {
    super.initState();
    _loadData().then((_) => _initializeForm());
    _loadCurrencySymbol();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _vendorController.dispose();
    _referenceController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrencySymbol() async {
    try {
      final symbol = await AppCurrencyService().getCurrencySymbol();
      if (mounted) {
        setState(() => _currencySymbol = symbol);
      }
    } catch (_) {
      // keep default
    }
  }

  Future<void> _loadData() async {
    setState(() => _loadingData = true);
    try {
      final customCategoriesFuture = _customCategoryService.listCustomCategories();
      final storesFuture = _storeService.getActiveStores();
      
      final results = await Future.wait([customCategoriesFuture, storesFuture]);
      
      setState(() {
        _customCategories = results[0] as List<CustomExpenseCategory>;
        _stores = results[1] as List<Store>;
        _loadingData = false;
      });
    } catch (e) {
      setState(() => _loadingData = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _initializeForm() {
    final expense = widget.initial;
    if (expense != null) {
      _selectedCategory = expense.customCategoryId == null ? expense.category : null;
      _selectedCustomCategory = expense.customCategoryId != null && _customCategories.isNotEmpty
          ? _customCategories.firstWhere((c) => c.id == expense.customCategoryId, orElse: () => _customCategories.first)
          : null;
      _selectedPaymentMethod = expense.paymentMethod;
      _selectedDate = expense.expenseDate;
      _selectedStore = expense.storeId != null && _stores.isNotEmpty
          ? _stores.firstWhere((s) => s.id == expense.storeId, orElse: () => _stores.first)
          : null;
      _isMultiStore = expense.hasAllocations;
      _allocations = expense.allocations?.map((a) => {
        'store_id': a.storeId,
        'amount': a.amount,
      }).toList() ?? [];
      
      _amountController.text = expense.amount.toString();
      _vendorController.text = expense.vendor ?? '';
      _referenceController.text = expense.reference ?? '';
      _noteController.text = expense.note ?? '';
    } else {
      // Initialize allocations for new expense
      if (_stores.isNotEmpty) {
        _allocations = [{'store_id': _stores.first.id, 'amount': 0.0}];
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.initial != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'Edit Expense' : 'Add Expense', 
                    style: const TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          if (_loadingData)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: _loadingData 
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildCategorySection(),
                  const SizedBox(height: 16),
                  _buildAmountSection(),
                  const SizedBox(height: 16),
                  _buildDateAndPaymentSection(),
                  const SizedBox(height: 16),
                  _buildStoreSection(),
                  const SizedBox(height: 16),
                  _buildDetailsSection(),
                  const SizedBox(height: 24),
                  _buildSubmitButton(),
                ],
              ),
            ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Category', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 12),
            Builder(builder: (context) {
              final items = _buildCategoryItems();
              final currentId = _currentCategoryId();
              final currentValue =
                  items.any((i) => i.value == currentId) ? currentId : null;

              return DropdownButtonFormField<String>(
                value: currentValue,
                isExpanded: true,
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: items,
                onChanged: (value) {
                  setState(() {
                    if (value == null) {
                      _selectedCategory = null;
                      _selectedCustomCategory = null;
                    } else if (value.startsWith('builtin:')) {
                      final key = value.substring('builtin:'.length);
                      _selectedCategory = ExpenseCategory.fromString(key);
                      _selectedCustomCategory = null;
                    } else if (value.startsWith('custom:')) {
                      final id = value.substring('custom:'.length);
                      _selectedCategory = null;
                      final matches =
                          _customCategories.where((c) => c.id == id).toList();
                      _selectedCustomCategory =
                          matches.isNotEmpty ? matches.first : null;
                    }
                  });
                },
                validator: (_) => (_selectedCategory == null &&
                        _selectedCustomCategory == null)
                    ? 'Please select a category'
                    : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  // Build a single list of category options (built-in + custom)
  List<DropdownMenuItem<String>> _buildCategoryItems() {
    final List<DropdownMenuItem<String>> items = [];
    for (final cat in ExpenseCategory.values) {
      items.add(
        DropdownMenuItem<String>(
          value: 'builtin:${cat.dbValue}',
          child: Row(
            children: [
              Icon(_getCategoryIcon(cat), size: 18, color: _getCategoryColor(cat)),
              const SizedBox(width: 8),
              Text(cat.displayName),
            ],
          ),
        ),
      );
    }
    for (final cat in _customCategories) {
      items.add(
        DropdownMenuItem<String>(
          value: 'custom:${cat.id}',
          child: Row(
            children: [
              Icon(cat.icon, size: 18, color: cat.color),
              const SizedBox(width: 8),
              Text(cat.displayName),
            ],
          ),
        ),
      );
    }
    return items;
  }

  String? _currentCategoryId() {
    if (_selectedCategory != null) return 'builtin:${_selectedCategory!.dbValue}';
    if (_selectedCustomCategory != null) return 'custom:${_selectedCustomCategory!.id}';
    return null;
  }

  Widget _buildAmountSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Amount', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 12),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: 'Amount *',
                prefixText: '$_currencySymbol ',
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) return 'Amount is required';
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) return 'Please enter a valid amount';
                return null;
              },
              onChanged: _onAmountChanged,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateAndPaymentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Date & Payment', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 20),
                          const SizedBox(width: 8),
                          Text(_selectedDate.toIso8601String().split('T').first),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<PaymentMethod>(
                    value: _selectedPaymentMethod,
                    decoration: const InputDecoration(
                      labelText: 'Payment Method',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    isExpanded: true,
                    items: PaymentMethod.values.map((method) => DropdownMenuItem(
                          value: method,
                          child: Text(method.displayName),
                        )).toList(),
                    onChanged: (value) {
                      setState(() => _selectedPaymentMethod = value!);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('Store Assignment', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                const Spacer(),
                Switch(
                  value: _isMultiStore,
                  onChanged: (value) {
                    setState(() {
                      _isMultiStore = value;
                      if (value) {
                        _selectedStore = null;
                        if (_allocations.isEmpty && _stores.isNotEmpty) {
                          _allocations = [{'store_id': _stores.first.id, 'amount': 0.0}];
                        }
                      } else {
                        _allocations.clear();
                        if (_stores.isNotEmpty) {
                          _selectedStore = _stores.first;
                        }
                      }
                    });
                  },
                ),
                const Text('Multi-store'),
              ],
            ),
            const SizedBox(height: 12),
            
            if (!_isMultiStore) ...[
              DropdownButtonFormField<Store?>(
                value: _selectedStore,
                decoration: const InputDecoration(
                  labelText: 'Store *',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                isExpanded: true,
                items: _stores.map((store) => DropdownMenuItem(
                      value: store,
                      child: Text('${store.name} ${store.storeNumber != null ? '(${store.storeNumber})' : ''}'),
                    )).toList(),
                onChanged: (value) {
                  setState(() => _selectedStore = value);
                },
                validator: (value) {
                  if (!_isMultiStore && value == null) return 'Please select a store';
                  return null;
                },
              ),
            ] else ...[
              ..._buildAllocationsSection(),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildAllocationsSection() {
    return [
      Row(
        children: [
          const Text('Store Allocations', style: TextStyle(fontWeight: FontWeight.w600)),
          const Spacer(),
          IconButton(
            onPressed: _addAllocation,
            icon: const Icon(Icons.add),
            tooltip: 'Add store allocation',
          ),
        ],
      ),
      const SizedBox(height: 8),
      ..._allocations.asMap().entries.map((entry) {
        final index = entry.key;
        final allocation = entry.value;
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  value: allocation['store_id'],
                  decoration: const InputDecoration(
                    labelText: 'Store',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    isDense: true,
                  ),
                  isExpanded: true,
                  items: _stores.map((store) => DropdownMenuItem(
                        value: store.id,
                        child: Text('${store.name} ${store.storeNumber != null ? '(${store.storeNumber})' : ''}'),
                      )).toList(),
                  onChanged: (value) {
                    setState(() {
                      _allocations[index]['store_id'] = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextFormField(
                  initialValue: allocation['amount'].toString(),
                  decoration: InputDecoration(
                    labelText: 'Amount',
                    prefixText: '$_currencySymbol ',
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    isDense: true,
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  onChanged: (value) {
                    final amount = double.tryParse(value) ?? 0.0;
                    setState(() {
                      _allocations[index]['amount'] = amount;
                    });
                  },
                ),
              ),
              IconButton(
                onPressed: _allocations.length > 1 ? () => _removeAllocation(index) : null,
                icon: const Icon(Icons.remove_circle_outline),
                tooltip: 'Remove allocation',
              ),
            ],
          ),
        );
      }).toList(),
      if (_isMultiStore && _allocations.isNotEmpty) ...[
        const SizedBox(height: 8),
        _buildAllocationSummary(),
      ],
    ];
  }

  Widget _buildAllocationSummary() {
    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;
    final allocatedAmount = _allocations.fold<double>(0.0, (sum, a) => sum + (a['amount'] as double));
    final difference = totalAmount - allocatedAmount;
    final isValid = difference.abs() < 0.01;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isValid
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Text('Total Amount: '),
              Text('$_currencySymbol${totalAmount.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
          Row(
            children: [
              const Text('Allocated: '),
              Text('$_currencySymbol${allocatedAmount.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
          Row(
            children: [
              const Text('Difference: '),
              Text(
                '$_currencySymbol${difference.toStringAsFixed(2)}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isValid ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          if (!isValid)
            const Text(
              'Allocations must equal the total amount',
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Additional Details', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 12),
            TextFormField(
              controller: _vendorController,
              decoration: const InputDecoration(
                labelText: 'Vendor',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _referenceController,
              decoration: const InputDecoration(
                labelText: 'Reference/Receipt #',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _noteController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: _loading ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        child: _loading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              )
            : Text(widget.initial != null ? 'Update Expense' : 'Create Expense'),
      ),
    );
  }

  void _onAmountChanged(String value) {
    if (_isMultiStore && _allocations.length == 1) {
      final amount = double.tryParse(value) ?? 0.0;
      setState(() {
        _allocations[0]['amount'] = amount;
      });
    }
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null) {
      setState(() => _selectedDate = picked);
    }
  }

  void _addAllocation() {
    if (_stores.isNotEmpty) {
      setState(() {
        _allocations.add({'store_id': _stores.first.id, 'amount': 0.0});
      });
    }
  }

  void _removeAllocation(int index) {
    setState(() {
      _allocations.removeAt(index);
    });
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate category selection
    if (_selectedCategory == null && _selectedCustomCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a category'), backgroundColor: Colors.red),
      );
      return;
    }

    // Validate multi-store allocations
    if (_isMultiStore) {
      final totalAmount = double.tryParse(_amountController.text) ?? 0.0;
      final allocatedAmount = _allocations.fold<double>(0.0, (sum, a) => sum + (a['amount'] as double));
      if ((totalAmount - allocatedAmount).abs() > 0.01) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Allocations must equal the total amount'), backgroundColor: Colors.red),
        );
        return;
      }
    }

    setState(() => _loading = true);

    try {
      final amount = double.parse(_amountController.text);
      final vendor = _vendorController.text.trim().isEmpty ? null : _vendorController.text.trim();
      final reference = _referenceController.text.trim().isEmpty ? null : _referenceController.text.trim();
      final note = _noteController.text.trim().isEmpty ? null : _noteController.text.trim();

      Expense expense;
      
      if (widget.initial != null) {
        // Update existing expense
        expense = await _expenseService.updateExpense(
          expenseId: widget.initial!.id,
          category: _selectedCategory,
          customCategoryId: _selectedCustomCategory?.id,
          amount: amount,
          date: _selectedDate,
          method: _selectedPaymentMethod,
          storeId: _isMultiStore ? null : _selectedStore?.id,
          vendor: vendor,
          reference: reference,
          note: note,
        );

        // Update allocations for multi-store
        if (_isMultiStore) {
          await _expenseService.setExpenseAllocations(widget.initial!.id, _allocations);
        }
      } else {
        // Create new expense
        if (_isMultiStore) {
          expense = await _expenseService.createExpenseMultiStore(
            category: _selectedCategory,
            customCategoryId: _selectedCustomCategory?.id,
            amount: amount,
            date: _selectedDate,
            method: _selectedPaymentMethod,
            allocations: _allocations,
            vendor: vendor,
            reference: reference,
            note: note,
          );
        } else {
          expense = await _expenseService.createExpenseSingleStore(
            category: _selectedCategory,
            customCategoryId: _selectedCustomCategory?.id,
            amount: amount,
            date: _selectedDate,
            method: _selectedPaymentMethod,
            storeId: _selectedStore!.id,
            vendor: vendor,
            reference: reference,
            note: note,
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(expense);
      }
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  IconData _getCategoryIcon(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.rent:
        return Icons.home;
      case ExpenseCategory.utilities:
        return Icons.flash_on;
      case ExpenseCategory.salaries:
        return Icons.people;
      case ExpenseCategory.supplies:
        return Icons.inventory;
      case ExpenseCategory.transport:
        return Icons.directions_car;
      case ExpenseCategory.maintenance:
        return Icons.build;
      case ExpenseCategory.marketing:
        return Icons.campaign;
      case ExpenseCategory.refund:
        return Icons.money_off;
      case ExpenseCategory.misc:
        return Icons.more_horiz;
    }
  }

  Color _getCategoryColor(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.rent:
        return Colors.brown;
      case ExpenseCategory.utilities:
        return Colors.yellow[700]!;
      case ExpenseCategory.salaries:
        return Colors.green;
      case ExpenseCategory.supplies:
        return Colors.blue;
      case ExpenseCategory.transport:
        return Colors.orange;
      case ExpenseCategory.maintenance:
        return Colors.grey;
      case ExpenseCategory.marketing:
        return Colors.purple;
      case ExpenseCategory.refund:
        return Colors.red;
      case ExpenseCategory.misc:
        return Colors.teal;
    }
  }
}

// Note: unified string value is used for dropdown to avoid equality issues
