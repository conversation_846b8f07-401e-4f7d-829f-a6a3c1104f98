import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/order_service.dart';
import '../../models/order_model.dart';
import '../../services/auth_service.dart';

class OrdersReportPage extends StatefulWidget {
  const OrdersReportPage({super.key});

  @override
  State<OrdersReportPage> createState() => _OrdersReportPageState();
}

class _OrdersReportPageState extends State<OrdersReportPage> {
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final AuthService _authService = AuthService(supabase: Supabase.instance.client);
  
  bool _isLoading = true;
  DateTimeRange? _selectedDateRange;
  String _creatorName = '';
  
  // Data
  List<Order> _orders = [];
  Map<String, int> _statusBreakdown = {};
  Map<String, dynamic> _orderStats = {};
  List<Map<String, dynamic>> _dailyOrders = [];

  @override
  void initState() {
    super.initState();
    // Default to current month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
    _creatorName = _authService.userDisplayName;
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      // Get all orders
      final allOrders = await _orderService.getUserOrders(Supabase.instance.client.auth.currentUser!.id);
      
      // Filter by date range
      final filteredOrders = allOrders.where((order) {
        if (_selectedDateRange == null) return true;
        return order.createdAt.isAfter(_selectedDateRange!.start) &&
               order.createdAt.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
      
      // Calculate status breakdown
      final Map<String, int> statusMap = {};
      for (final order in filteredOrders) {
        final status = order.status.displayName;
        statusMap[status] = (statusMap[status] ?? 0) + 1;
      }
      
      // Calculate daily orders
      final Map<String, int> dailyOrdersMap = {};
      for (final order in filteredOrders) {
        final dateKey = order.createdAt.toString().substring(0, 10);
        dailyOrdersMap[dateKey] = (dailyOrdersMap[dateKey] ?? 0) + 1;
      }
      
      final dailyOrdersList = dailyOrdersMap.entries.map((entry) => {
        'date': entry.key,
        'count': entry.value,
      }).toList();
      
      dailyOrdersList.sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));
      
      // Calculate completion rate
      final completedOrders = filteredOrders.where((o) => 
        o.status == OrderStatus.readyForPickup || o.status == OrderStatus.completed
      ).length;
      final completionRate = filteredOrders.isNotEmpty ? 
        (completedOrders / filteredOrders.length) * 100 : 0.0;
      
      // Calculate average processing time
      double avgProcessingTime = 0;
      int processedOrdersCount = 0;
      
      for (final order in filteredOrders) {
        if (order.status == OrderStatus.completed || order.status == OrderStatus.readyForPickup) {
          // Estimate processing time as time from creation to now
          // In a real system, you'd track actual completion timestamps
          final processingTime = DateTime.now().difference(order.createdAt).inHours;
          avgProcessingTime += processingTime;
          processedOrdersCount++;
        }
      }
      
      if (processedOrdersCount > 0) {
        avgProcessingTime = avgProcessingTime / processedOrdersCount;
      }
      
      setState(() {
        _orders = filteredOrders;
        _statusBreakdown = statusMap;
        _orderStats = {
          'totalOrders': filteredOrders.length,
          'completedOrders': completedOrders,
          'completionRate': completionRate,
          'avgProcessingTime': avgProcessingTime,
          'pendingOrders': filteredOrders.where((o) => o.status == OrderStatus.pending).length,
          'inProgressOrders': filteredOrders.where((o) => 
            o.status == OrderStatus.accepted || 
            o.status == OrderStatus.pickedUp || 
            o.status == OrderStatus.inProcess
          ).length,
        };
        _dailyOrders = dailyOrdersList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading orders data: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
      _loadData();
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.indigo;
      case OrderStatus.inProcess:
        return Colors.purple;
      case OrderStatus.readyForPickup:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.amber;
      case OrderStatus.completed:
        return Colors.teal;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders Analytics'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 4,
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'Select Date Range',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildUserCard(),
                  const SizedBox(height: 16),
                  // Date Range Display
                  _buildDateRangeCard(),
                  const SizedBox(height: 16),
                  
                  // Summary Stats
                  _buildSummaryStats(),
                  const SizedBox(height: 24),
                  
                  // Status Breakdown
                  _buildStatusBreakdown(),
                  const SizedBox(height: 24),
                  
                  // Daily Orders
                  _buildDailyOrdersSection(),
                  const SizedBox(height: 24),
                  
                  // Recent Orders
                  _buildRecentOrdersSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildUserCard() {
    final name = _creatorName.isEmpty ? 'User' : _creatorName;
    return Card(
      child: ListTile(
        leading: const Icon(Icons.person, color: Colors.blue),
        title: const Text('User'),
        subtitle: Text(name),
      ),
    );
  }

  Widget _buildDateRangeCard() {
    final startDate = _selectedDateRange?.start.toString().substring(0, 10) ?? 'N/A';
    final endDate = _selectedDateRange?.end.toString().substring(0, 10) ?? 'N/A';
    
    return Card(
      child: ListTile(
        leading: const Icon(Icons.date_range, color: Colors.blue),
        title: const Text('Date Range'),
        subtitle: Text('$startDate to $endDate'),
        trailing: const Icon(Icons.edit),
        onTap: _selectDateRange,
      ),
    );
  }

  Widget _buildSummaryStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Orders',
                    '${_orderStats['totalOrders'] ?? 0}',
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Completed',
                    '${_orderStats['completedOrders'] ?? 0}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Completion Rate',
                    '${(_orderStats['completionRate'] ?? 0).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Avg Processing',
                    '${(_orderStats['avgProcessingTime'] ?? 0).toStringAsFixed(1)}h',
                    Icons.access_time,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBreakdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Status Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_statusBreakdown.isEmpty)
              const Center(
                child: Text(
                  'No orders for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _statusBreakdown.length,
                itemBuilder: (context, index) {
                  final entry = _statusBreakdown.entries.elementAt(index);
                  final status = entry.key;
                  final count = entry.value;
                  final percentage = _orderStats['totalOrders'] > 0 
                    ? (count / _orderStats['totalOrders'] * 100).toStringAsFixed(1)
                    : '0.0';
                  
                  // Get color for status
                  Color color = Colors.grey;
                  IconData icon = Icons.help;
                  
                  switch (status) {
                    case 'Pending':
                      color = Colors.orange;
                      icon = Icons.hourglass_empty;
                      break;
                    case 'Accepted':
                      color = Colors.blue;
                      icon = Icons.check;
                      break;
                    case 'Picked Up':
                      color = Colors.indigo;
                      icon = Icons.local_shipping;
                      break;
                    case 'In Process':
                      color = Colors.purple;
                      icon = Icons.settings;
                      break;
                    case 'Completed':
                      color = Colors.green;
                      icon = Icons.check_circle;
                      break;
                    case 'Delivered':
                      color = Colors.teal;
                      icon = Icons.delivery_dining;
                      break;
                    case 'Cancelled':
                      color = Colors.red;
                      icon = Icons.cancel;
                      break;
                  }
                  
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: color.withValues(alpha: 0.1),
                      child: Icon(icon, color: color, size: 20),
                    ),
                    title: Text(status),
                    subtitle: Text('$percentage% of total orders'),
                    trailing: Text(
                      count.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                        fontSize: 16,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyOrdersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Daily Orders Trend',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_dailyOrders.isEmpty)
              const Center(
                child: Text(
                  'No orders data for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _dailyOrders.length,
                itemBuilder: (context, index) {
                  final day = _dailyOrders[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.withValues(alpha: 0.1),
                      child: Text(
                        '${day['count']}',
                        style: const TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(day['date'] as String),
                    subtitle: Text('${day['count']} orders placed'),
                    trailing: const Icon(
                      Icons.trending_up,
                      color: Colors.blue,
                      size: 20,
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentOrdersSection() {
    final recentOrders = _orders.take(10).toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Orders',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (recentOrders.isEmpty)
              const Center(
                child: Text(
                  'No recent orders',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recentOrders.length,
                itemBuilder: (context, index) {
                  final order = recentOrders[index];
                  final statusColor = _getStatusColor(order.status);
                  
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: statusColor.withValues(alpha: 0.1),
                      child: Text(
                        order.orderNumber.substring(order.orderNumber.length - 2),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(order.orderNumber),
                    subtitle: Text(order.createdAt.toString().substring(0, 16)),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        order.status.displayName,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}