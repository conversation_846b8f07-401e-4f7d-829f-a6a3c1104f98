class FAQ {
  final String question;
  final String answer;
  final String category;
  final bool isPinned;

  FAQ({
    required this.question,
    required this.answer,
    required this.category,
    this.isPinned = false,
  });

  factory FAQ.fromJson(Map<String, dynamic> json) {
    return FAQ(
      question: json['question'] as String,
      answer: json['answer'] as String,
      category: json['category'] as String,
      isPinned: json['isPinned'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'answer': answer,
      'category': category,
      'isPinned': isPinned,
    };
  }
}

class FAQCategory {
  final String name;
  final String icon;
  final List<FAQ> faqs;

  FAQCategory({
    required this.name,
    required this.icon,
    required this.faqs,
  });
}
