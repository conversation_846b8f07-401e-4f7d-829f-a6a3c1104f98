-- Staff Management Schema (profiles linked to auth.users)

-- ============================================
-- SAFETY DROPS
-- ============================================

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'staff_profiles') THEN
    DROP TRIGGER IF EXISTS update_staff_profiles_updated_at ON staff_profiles;
    DROP TRIGGER IF EXISTS sync_staff_ban_on_change ON staff_profiles;
  END IF;
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'staff_activities') THEN
    -- no triggers defined on activities
    NULL;
  END IF;
  DROP TABLE IF EXISTS staff_activities CASCADE;
  DROP TABLE IF EXISTS staff_profiles CASCADE;
  DROP FUNCTION IF EXISTS handle_new_staff() CASCADE;
  DROP FUNCTION IF EXISTS promote_to_staff(UUID, TEXT) CASCADE;
  DROP FUNCTION IF EXISTS promote_to_staff(UUID, TEXT, TEXT) CASCADE;
  DROP FUNCTION IF EXISTS promote_to_staff(UUID, TEXT, TEXT, TEXT) CASCADE;
  DROP FUNCTION IF EXISTS set_staff_role(UUID, TEXT) CASCADE;
  DROP FUNCTION IF EXISTS set_staff_active(UUID, BOOLEAN) CASCADE;
  DROP FUNCTION IF EXISTS sync_staff_ban(UUID, BOOLEAN) CASCADE;
  DROP FUNCTION IF EXISTS log_staff_activity(UUID, TEXT, JSONB, UUID) CASCADE;
END $$;

-- Ensure extensions and util function exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
  NEW.updated_at = TIMEZONE('utc'::text, NOW());
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

-- ============================================
-- STAFF PROFILES (activation + optional role)
-- ============================================

CREATE TABLE staff_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  auth_user_id UUID NULL REFERENCES auth.users(id) ON DELETE SET NULL,
  staff_id INTEGER UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  password TEXT,
  email_enabled BOOLEAN NOT NULL DEFAULT true,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_by UUID NULL DEFAULT auth.uid(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW())
);

CREATE INDEX idx_staff_profiles_is_active ON staff_profiles(is_active);
CREATE INDEX idx_staff_profiles_staff_id ON staff_profiles(staff_id);
CREATE INDEX idx_staff_profiles_email_enabled ON staff_profiles(email_enabled);
CREATE UNIQUE INDEX IF NOT EXISTS uq_staff_profiles_auth_user_id ON staff_profiles(auth_user_id);

-- Activity log table
CREATE TABLE staff_activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  staff_id UUID NOT NULL REFERENCES staff_profiles(id) ON DELETE CASCADE,
  action TEXT NOT NULL,              -- e.g., 'created','activated','deactivated','role_changed'
  details JSONB,                     -- arbitrary payload
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW())
);

CREATE INDEX idx_staff_activities_staff_id ON staff_activities(staff_id);
CREATE INDEX idx_staff_activities_created_at ON staff_activities(created_at);

-- updated_at trigger
CREATE TRIGGER update_staff_profiles_updated_at
  BEFORE UPDATE ON staff_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Prevent login for deactivated users by syncing auth.users.banned_until
-- ============================================

CREATE OR REPLACE FUNCTION sync_staff_ban(p_user UUID, p_active BOOLEAN)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  -- If not active, set banned_until far future; else clear ban
  UPDATE auth.users
  SET banned_until = CASE WHEN p_active THEN NULL ELSE NOW() + INTERVAL '100 years' END
  WHERE id = p_user;
END; $$ LANGUAGE plpgsql;

-- Trigger to call sync on insert/update
CREATE OR REPLACE FUNCTION handle_staff_change()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  IF NEW.auth_user_id IS NOT NULL THEN
    PERFORM public.sync_staff_ban(NEW.auth_user_id, NEW.is_active);
  END IF;
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

CREATE TRIGGER sync_staff_ban_on_change
  AFTER INSERT OR UPDATE OF is_active ON staff_profiles
  FOR EACH ROW EXECUTE FUNCTION handle_staff_change();

-- ============================================
-- Activity helper
-- ============================================

CREATE OR REPLACE FUNCTION public.log_staff_activity(p_staff UUID, p_action TEXT, p_details JSONB DEFAULT NULL, p_by UUID DEFAULT NULL)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.staff_activities(staff_id, action, details, created_by)
  VALUES (p_staff, p_action, p_details, p_by);
END; $$ LANGUAGE plpgsql;

-- ============================================
-- AUTO CREATE PROFILE ON SIGNUP (if metadata says is_staff)
-- ============================================

CREATE OR REPLACE FUNCTION handle_new_staff()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  next_staff_id INTEGER;
  v_profile_id UUID;
BEGIN
  IF (NEW.raw_user_meta_data ? 'is_staff' AND (NEW.raw_user_meta_data->>'is_staff')::boolean = true) THEN
    -- Generate next available staff_id (4 digits starting from 1000)
    SELECT COALESCE(MAX(staff_id), 999) + 1 
    INTO next_staff_id 
    FROM public.staff_profiles;
    
    -- Ensure it's at least 1000 (4 digits)
    IF next_staff_id < 1000 THEN
      next_staff_id := 1000;
    END IF;
    
    -- Only create the staff profile, don't modify auth.users during signup
    INSERT INTO public.staff_profiles(auth_user_id, staff_id, full_name, email, phone, password, email_enabled, is_active)
    VALUES (
      NEW.id,
      next_staff_id,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
      NEW.email,
      NEW.raw_user_meta_data->>'phone',
      NEW.raw_user_meta_data->>'password',
      COALESCE((NEW.raw_user_meta_data->>'email_enabled')::boolean, true),
      true
    )
    ON CONFLICT (auth_user_id) DO UPDATE SET
      email = EXCLUDED.email,
      full_name = EXCLUDED.full_name
    RETURNING id INTO v_profile_id;
    
    -- Log the activity (without trying to modify auth.users during trigger)
    INSERT INTO public.staff_activities(staff_id, action, details, created_by)
    VALUES (v_profile_id, 'created', jsonb_build_object('source', 'auto_signup', 'staff_id', next_staff_id), NEW.id);
  END IF;
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_auth_user_created_staff ON auth.users;
CREATE TRIGGER on_auth_user_created_staff
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_staff();

-- ============================================
-- RLS
-- ============================================

ALTER TABLE staff_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_activities ENABLE ROW LEVEL SECURITY;

-- Keep ONE permissive SELECT policy for performance
DROP POLICY IF EXISTS "All staff readable by authenticated" ON staff_profiles;
CREATE POLICY "All staff readable by authenticated" ON staff_profiles
  FOR SELECT TO authenticated USING (true);

-- Staff can update own profile
DROP POLICY IF EXISTS "Staff can update own profile" ON staff_profiles;
DROP POLICY IF EXISTS "Staff profiles update by authenticated" ON staff_profiles;
CREATE POLICY "Staff profiles update by authenticated" ON staff_profiles
  FOR UPDATE TO authenticated USING (true) WITH CHECK (true);

-- Activities readable by authenticated (adjust per needs)
DROP POLICY IF EXISTS "Staff activities readable by authenticated" ON staff_activities;
CREATE POLICY "Staff activities readable by authenticated" ON staff_activities
  FOR SELECT TO authenticated USING (true);

-- Allow inserts from authenticated users (client or RPC)
DROP POLICY IF EXISTS "No direct insert to staff_profiles" ON staff_profiles;
CREATE POLICY "Staff profiles insert by authenticated" ON staff_profiles
  FOR INSERT TO authenticated WITH CHECK (true);

DROP POLICY IF EXISTS "Staff profiles delete by authenticated" ON staff_profiles;
CREATE POLICY "Staff profiles delete by authenticated" ON staff_profiles
  FOR DELETE TO authenticated USING (true);

-- ============================================
-- Helper functions to manage staff
-- ============================================

CREATE OR REPLACE FUNCTION promote_to_staff(p_user UUID, p_full_name TEXT, p_phone TEXT DEFAULT NULL, p_password TEXT DEFAULT NULL, p_email_enabled BOOLEAN DEFAULT true)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  next_staff_id INTEGER;
  v_profile_id UUID;
BEGIN
  -- Generate next available staff_id (4 digits starting from 1000)
  SELECT COALESCE(MAX(staff_id), 999) + 1 
  INTO next_staff_id 
  FROM public.staff_profiles;
  
  -- Ensure it's at least 1000 (4 digits)
  IF next_staff_id < 1000 THEN
    next_staff_id := 1000;
  END IF;
  
  INSERT INTO public.staff_profiles(id, staff_id, full_name, email, phone, password, email_enabled, is_active)
  SELECT u.id, next_staff_id, COALESCE(p_full_name, u.email), u.email,
         p_phone, p_password, p_email_enabled, true
  FROM auth.users u
  WHERE u.id = p_user
  ON CONFLICT (id) DO NOTHING;
  PERFORM sync_staff_ban(p_user, true);
  PERFORM public.log_staff_activity(p_user, 'promoted', jsonb_build_object('staff_id', next_staff_id, 'phone', p_phone, 'email_enabled', p_email_enabled), current_setting('request.jwt.claim.sub', true)::uuid);
END; $$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION set_staff_active(p_user UUID, p_active BOOLEAN)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.staff_profiles SET is_active = p_active WHERE id = p_user;
  -- p_user is profile id; sync ban only if we have a linked auth user
  PERFORM public.sync_staff_ban(sp.auth_user_id, p_active)
  FROM public.staff_profiles sp
  WHERE sp.id = p_user AND sp.auth_user_id IS NOT NULL;
  PERFORM public.log_staff_activity(p_user, CASE WHEN p_active THEN 'activated' ELSE 'deactivated' END, NULL, current_setting('request.jwt.claim.sub', true)::uuid);
END; $$ LANGUAGE plpgsql;

-- ============================================
-- Create staff with email and temporary password
-- ============================================

-- Simple function to create staff profile for existing user
CREATE OR REPLACE FUNCTION create_staff_profile_for_user(
  p_user_id UUID,
  p_full_name TEXT,
  p_email TEXT,
  p_phone TEXT DEFAULT NULL,
  p_password TEXT DEFAULT NULL,
  p_email_enabled BOOLEAN DEFAULT true
)
RETURNS INTEGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  next_staff_id INTEGER;
  v_profile_id UUID;
BEGIN
  -- Generate next available staff_id (4 digits starting from 1000)
  SELECT COALESCE(MAX(staff_id), 999) + 1 
  INTO next_staff_id 
  FROM public.staff_profiles;
  
  -- Ensure it's at least 1000 (4 digits)
  IF next_staff_id < 1000 THEN
    next_staff_id := 1000;
  END IF;
  
  -- Create staff profile
  INSERT INTO public.staff_profiles(auth_user_id, staff_id, full_name, email, phone, password, email_enabled, is_active)
  VALUES (
    p_user_id,
    next_staff_id,
    p_full_name,
    p_email,
    p_phone,
    p_password,
    p_email_enabled,
    true
  )
  ON CONFLICT (auth_user_id) DO UPDATE SET
    staff_id = EXCLUDED.staff_id,
    full_name = EXCLUDED.full_name,
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    password = EXCLUDED.password,
    email_enabled = EXCLUDED.email_enabled,
    is_active = EXCLUDED.is_active
  RETURNING id INTO v_profile_id;

  -- Log the activity
  PERFORM public.log_staff_activity(
    v_profile_id, 
    'created_with_email', 
    jsonb_build_object(
      'staff_id', next_staff_id,
      'email', p_email,
      'phone', p_phone, 
      'email_enabled', p_email_enabled
    ), 
    p_user_id  -- Use the user_id as the creator for manual creation
  );
  
  RETURN next_staff_id;
END; $$ LANGUAGE plpgsql;

-- Function to ensure staff is properly activated (call after user creation)
CREATE OR REPLACE FUNCTION ensure_staff_active(p_user_id UUID)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  -- Ensure user is not banned (safe to call after user creation)
  PERFORM public.sync_staff_ban(p_user_id, true);
END; $$ LANGUAGE plpgsql;

-- Helper function to get staff temporary password (for admin use)
CREATE OR REPLACE FUNCTION get_staff_temp_password(p_user_id UUID)
RETURNS TEXT
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  temp_pass TEXT;
BEGIN
  SELECT raw_user_meta_data->>'temp_password' 
  INTO temp_pass
  FROM auth.users 
  WHERE id = p_user_id;
  
  RETURN temp_pass;
END; $$ LANGUAGE plpgsql;

-- Function to clear temporary password after first login
CREATE OR REPLACE FUNCTION clear_temp_password(p_user_id UUID)
RETURNS VOID
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  UPDATE auth.users 
  SET raw_user_meta_data = raw_user_meta_data - 'temp_password'
  WHERE id = p_user_id;
  
  PERFORM public.log_staff_activity(p_user_id, 'temp_password_cleared', NULL, p_user_id);
END; $$ LANGUAGE plpgsql;

-- ============================================
-- STAFF VERIFICATION
-- ============================================

-- Function to check if user is staff
CREATE OR REPLACE FUNCTION is_staff()
RETURNS BOOLEAN
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.staff_profiles 
    WHERE auth_user_id = auth.uid() 
    AND is_active = true
  );
END; $$ LANGUAGE plpgsql;

-- ============================================
-- STAFF CREATION FUNCTIONS (for any authenticated user)
-- ============================================

-- Function to create staff profile (accessible by any authenticated user)
CREATE OR REPLACE FUNCTION create_staff_profile(
  p_full_name TEXT,
  p_email TEXT,
  p_phone TEXT DEFAULT NULL,
  p_password TEXT DEFAULT NULL,
  p_email_enabled BOOLEAN DEFAULT true,
  p_auth_user_id UUID DEFAULT NULL
)
RETURNS UUID
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  next_staff_id INTEGER;
  v_profile_id UUID;
BEGIN
  -- Generate next available staff_id (4 digits starting from 1000)
  SELECT COALESCE(MAX(staff_id), 999) + 1 
  INTO next_staff_id 
  FROM public.staff_profiles;
  
  -- Ensure it's at least 1000 (4 digits)
  IF next_staff_id < 1000 THEN
    next_staff_id := 1000;
  END IF;

  -- Create staff profile
  INSERT INTO public.staff_profiles(auth_user_id, staff_id, full_name, email, phone, password, email_enabled, is_active)
  VALUES (
    p_auth_user_id,
    next_staff_id,
    p_full_name,
    p_email,
    p_phone,
    p_password,
    p_email_enabled,
    true
  )
  RETURNING id INTO v_profile_id;

  -- Ensure user is properly activated
  IF p_auth_user_id IS NOT NULL THEN
    PERFORM public.sync_staff_ban(p_auth_user_id, true);
  END IF;

  -- Log the activity
  PERFORM public.log_staff_activity(
    v_profile_id,
    'created_by_user', 
    jsonb_build_object(
      'staff_id', next_staff_id,
      'email', p_email,
      'phone', p_phone, 
      'email_enabled', p_email_enabled,
      'method', 'user_create'
    ), 
    auth.uid()
  );
  
  RETURN v_profile_id;
END; $$ LANGUAGE plpgsql;