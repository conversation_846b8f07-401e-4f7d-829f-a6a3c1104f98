import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/invoice_service.dart';
import '../../services/order_service.dart';

class PaymentsReportPage extends StatefulWidget {
  const PaymentsReportPage({super.key});

  @override
  State<PaymentsReportPage> createState() => _PaymentsReportPageState();
}

class _PaymentsReportPageState extends State<PaymentsReportPage> {
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  
  bool _isLoading = true;
  String _currencySymbol = '';
  DateTimeRange? _selectedDateRange;
  
  // Data
  List<Map<String, dynamic>> _paymentMethodBreakdown = [];
  Map<String, dynamic> _paymentStats = {};
  List<Map<String, dynamic>> _dailyPayments = [];

  @override
  void initState() {
    super.initState();
    // Default to current month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      _currencySymbol = await _orderService.getCurrencySymbol();
      
      // Get payment summary for date range
      final paymentSummary = await _invoiceService.getPaymentSummary(
        startDate: _selectedDateRange?.start,
        endDate: _selectedDateRange?.end,
      );
      
      // Calculate total payments and transactions
      double totalPayments = 0;
      int totalTransactions = 0;
      
      for (final payment in paymentSummary) {
        totalPayments += payment['total'] as double;
        totalTransactions += payment['count'] as int;
      }
      
      // Calculate average transaction value
      final avgTransactionValue = totalTransactions > 0 ? totalPayments / totalTransactions : 0.0;
      
      // Get most popular payment method
      String mostPopularMethod = 'N/A';
      int maxTransactions = 0;
      
      for (final payment in paymentSummary) {
        if (payment['count'] > maxTransactions) {
          maxTransactions = payment['count'] as int;
          mostPopularMethod = _getPaymentMethodDisplayName(payment['method'] as String);
        }
      }
      
      // Simulate daily payments data (in real app, you'd query payments by date)
      final Map<String, Map<String, dynamic>> dailyPaymentsMap = {};
      
      // For demo purposes, distribute payments across days
      if (_selectedDateRange != null && paymentSummary.isNotEmpty) {
        final daysDiff = _selectedDateRange!.end.difference(_selectedDateRange!.start).inDays;
        
        for (int i = 0; i <= daysDiff; i++) {
          final date = _selectedDateRange!.start.add(Duration(days: i));
          final dateKey = date.toString().substring(0, 10);
          
          // Simulate some payment activity (this would be real data in production)
          if (i % 3 == 0 && paymentSummary.isNotEmpty) { // Every 3rd day has payments
            final dailyAmount = (totalPayments / (daysDiff / 3 + 1));
            final dailyCount = (totalTransactions / (daysDiff / 3 + 1)).round();
            
            dailyPaymentsMap[dateKey] = {
              'date': dateKey,
              'amount': dailyAmount,
              'count': dailyCount,
            };
          }
        }
      }
      
      final dailyPaymentsList = dailyPaymentsMap.values.toList();
      dailyPaymentsList.sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));
      
      setState(() {
        _paymentMethodBreakdown = paymentSummary;
        _paymentStats = {
          'totalPayments': totalPayments,
          'totalTransactions': totalTransactions,
          'avgTransactionValue': avgTransactionValue,
          'mostPopularMethod': mostPopularMethod,
        };
        _dailyPayments = dailyPaymentsList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading payment data: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _getPaymentMethodDisplayName(String method) {
    switch (method) {
      case 'cash':
        return 'Cash';
      case 'mpesa_till':
        return 'M-Pesa Till';
      case 'mpesa_paybill':
        return 'M-Pesa Paybill';
      case 'mpesa_send_money':
        return 'M-Pesa Send Money';
      default:
        return method;
    }
  }

  Color _getPaymentMethodColor(String method) {
    switch (method) {
      case 'cash':
        return Colors.green;
      case 'mpesa_till':
        return Colors.blue;
      case 'mpesa_paybill':
        return Colors.indigo;
      case 'mpesa_send_money':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method) {
      case 'cash':
        return Icons.money;
      case 'mpesa_till':
        return Icons.phone_android;
      case 'mpesa_paybill':
        return Icons.payment;
      case 'mpesa_send_money':
        return Icons.send;
      default:
        return Icons.payment;
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Methods Report'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 4,
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'Select Date Range',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range Display
                  _buildDateRangeCard(),
                  const SizedBox(height: 16),
                  
                  // Payment Stats
                  _buildPaymentStats(),
                  const SizedBox(height: 24),
                  
                  // Payment Method Breakdown
                  _buildPaymentMethodBreakdown(),
                  const SizedBox(height: 24),
                  
                  // Daily Payments
                  _buildDailyPaymentsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildDateRangeCard() {
    final startDate = _selectedDateRange?.start.toString().substring(0, 10) ?? 'N/A';
    final endDate = _selectedDateRange?.end.toString().substring(0, 10) ?? 'N/A';
    
    return Card(
      child: ListTile(
        leading: const Icon(Icons.date_range, color: Colors.orange),
        title: const Text('Analysis Period'),
        subtitle: Text('$startDate to $endDate'),
        trailing: const Icon(Icons.edit),
        onTap: _selectDateRange,
      ),
    );
  }

  Widget _buildPaymentStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Payments',
                    '$_currencySymbol${(_paymentStats['totalPayments'] ?? 0).toStringAsFixed(2)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Transactions',
                    '${_paymentStats['totalTransactions'] ?? 0}',
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Avg Transaction',
                    '$_currencySymbol${(_paymentStats['avgTransactionValue'] ?? 0).toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Popular Method',
                    '${_paymentStats['mostPopularMethod'] ?? 'N/A'}',
                    Icons.star,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodBreakdown() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Method Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_paymentMethodBreakdown.isEmpty)
              const Center(
                child: Text(
                  'No payment data for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _paymentMethodBreakdown.length,
                itemBuilder: (context, index) {
                  final payment = _paymentMethodBreakdown[index];
                  final method = payment['method'] as String;
                  final count = payment['count'] as int;
                  final total = payment['total'] as double;
                  
                  final displayName = _getPaymentMethodDisplayName(method);
                  final color = _getPaymentMethodColor(method);
                  final icon = _getPaymentMethodIcon(method);
                  
                  // Calculate percentage
                  final totalPayments = _paymentStats['totalPayments'] as double;
                  final percentage = totalPayments > 0 
                    ? (total / totalPayments * 100).toStringAsFixed(1)
                    : '0.0';
                  
                  return ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: color.withValues(alpha: 0.1),
                      child: Icon(icon, color: color, size: 20),
                    ),
                    title: Text(displayName),
                    subtitle: Text('$count transactions • $percentage%'),
                    trailing: Text(
                      '$_currencySymbol${total.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Average per transaction:', style: TextStyle(color: Colors.grey[600])),
                                Text('$_currencySymbol${(total / count).toStringAsFixed(2)}'),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Share of total transactions:', style: TextStyle(color: Colors.grey[600])),
                                Text('${(count / (_paymentStats['totalTransactions'] as int) * 100).toStringAsFixed(1)}%'),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Share of total revenue:', style: TextStyle(color: Colors.grey[600])),
                                Text('$percentage%'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPaymentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Daily Payment Activity',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_dailyPayments.isEmpty)
              const Center(
                child: Text(
                  'No payment activity for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _dailyPayments.length,
                itemBuilder: (context, index) {
                  final day = _dailyPayments[index];
                  final date = day['date'] as String;
                  final amount = day['amount'] as double;
                  final count = day['count'] as int;
                  
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.orange.withValues(alpha: 0.1),
                      child: Text(
                        '$count',
                        style: const TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    title: Text(date),
                    subtitle: Text('$count transactions'),
                    trailing: Text(
                      '$_currencySymbol${amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}