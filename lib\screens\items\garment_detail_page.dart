import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/garment_model.dart';
import '../../services/garment_service.dart';

class GarmentDetailPage extends StatefulWidget {
  final Garment? garment;
  final bool readOnly;
  const GarmentDetailPage({super.key, this.garment, this.readOnly = false});

  @override
  State<GarmentDetailPage> createState() => _GarmentDetailPageState();
}

class _GarmentDetailPageState extends State<GarmentDetailPage> {
  final _formKey = GlobalKey<FormState>();
  final _svc = GarmentService(supabase: Supabase.instance.client);

  late String _name;
  String _category = 'clothing';
  String? _description;
  String? _icon;

  @override
  void initState() {
    super.initState();
    final g = widget.garment;
    if (g != null) {
      _name = g.name;
      _category = g.category;
      _description = g.description;
      _icon = g.icon;
    } else {
      _name = '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.garment != null && !widget.readOnly;
    final isCreate = widget.garment == null && !widget.readOnly;

    return Scaffold(
      appBar: AppBar(
        title: Text(isCreate ? 'Add Item' : isEdit ? 'Edit Item' : 'Item Detail'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: AbsorbPointer(
          absorbing: widget.readOnly,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  initialValue: _name,
                  decoration: const InputDecoration(labelText: 'Name'),
                  validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                  onChanged: (v) => _name = v.trim(),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _category,
                  items: const [
                    DropdownMenuItem(value: 'clothing', child: Text('Clothing')),
                    DropdownMenuItem(value: 'household', child: Text('Household Items')),
                    DropdownMenuItem(value: 'special', child: Text('Special Care')),
                  ],
                  onChanged: (v) => setState(() => _category = v ?? 'clothing'),
                  decoration: const InputDecoration(labelText: 'Category'),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  initialValue: _description,
                  decoration: const InputDecoration(labelText: 'Description'),
                  onChanged: (v) => _description = v.trim().isEmpty ? null : v.trim(),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  initialValue: _icon ?? '',
                  decoration: const InputDecoration(labelText: 'Icon (Material icon name)'),
                  onChanged: (v) => _icon = v.trim().isEmpty ? null : v.trim(),
                ),
                const SizedBox(height: 16),
                if (!widget.readOnly)
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          label: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _save,
                          icon: const Icon(Icons.save),
                          label: const Text('Save'),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;
    try {
      if (widget.garment == null) {
        final created = await _svc.createRaw(
          name: _name,
          category: _category,
          description: _description,
          icon: _icon,
        );
        if (!mounted) return;
        Navigator.pop(context, {'ok': true, 'action': 'created', 'garment': created});
      } else {
        final updated = await _svc.update(widget.garment!.id, {
          'name': _name,
          'category': _category,
          'description': _description,
          'icon': _icon,
        });
        if (!mounted) return;
        Navigator.pop(context, {'ok': true, 'action': 'updated', 'garment': updated});
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Save failed: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}
