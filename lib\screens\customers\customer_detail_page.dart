import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../services/customer_service.dart';
import '../../services/order_service.dart';
import '../../services/currency_service.dart' as currency_service;
import '../../models/customer_model.dart';
import '../../models/order_model.dart';
import '../../widgets/country_picker_dialog.dart';
import '../orders/order_detail_page.dart';

class CustomerDetailPage extends StatefulWidget {
  final String customerId;
  
  const CustomerDetailPage({super.key, required this.customerId});

  @override
  State<CustomerDetailPage> createState() => _CustomerDetailPageState();
}

class _CustomerDetailPageState extends State<CustomerDetailPage> with TickerProviderStateMixin {
  final CustomerService _service = CustomerService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final currency_service.AppCurrencyService _currencyService = currency_service.AppCurrencyService();
  
  Customer? _customer;
  List<CustomerAddress> _addresses = [];
  List<Order> _orders = [];
  
  bool _loading = true;
  bool _saving = false;
  bool _ordersLoading = false;
  bool _addressLoading = false;
  String _currencySymbol = '\$';

  final _nameCtrl = TextEditingController();
  final _emailCtrl = TextEditingController();
  final _phoneCtrl = TextEditingController();
  final _countryCtrl = TextEditingController();
  final _notesCtrl = TextEditingController();
  bool _active = true;
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCurrencySymbol();
    _load();
  }

  Future<void> _loadCurrencySymbol() async {
    try {
      final symbol = await _currencyService.getCurrencySymbol();
      if (mounted) {
        setState(() {
          _currencySymbol = symbol;
        });
      }
    } catch (e) {
      // Use default symbol on error
    }
  }

  String _formatAmount(double amount) {
    return '$_currencySymbol${amount.toStringAsFixed(2)}';
  }



  @override
  void dispose() {
    _nameCtrl.dispose();
    _emailCtrl.dispose();
    _phoneCtrl.dispose();
    _countryCtrl.dispose();
    _notesCtrl.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _load() async {
    setState(() { _loading = true; });
    
    try {
      // Get customer details
      final customer = await _service.getCustomer(widget.customerId);
      if (customer == null) {
        throw Exception('Customer not found');
      }
      
      // Load all data in parallel
      final results = await Future.wait([
        _service.getCustomerAddresses(widget.customerId),
        _loadOrders(),
      ]);
      
      final addresses = results[0] as List<CustomerAddress>;
      
      if (!mounted) return;
      
      setState(() {
        _customer = customer;
        _addresses = addresses;
        _loading = false;
        _nameCtrl.text = customer.fullName;
        _emailCtrl.text = customer.email ?? '';
        _phoneCtrl.text = customer.phone ?? '';
        _countryCtrl.text = customer.country ?? '';
        _notesCtrl.text = customer.notes ?? '';
        _active = customer.isActive;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() { _loading = false; });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading customer: $e')),
      );
    }
  }

  Future<List<Order>> _loadOrders() async {
    try {
      setState(() => _ordersLoading = true);
      final orders = await _orderService.getUserOrders(widget.customerId);
      if (mounted) {
        setState(() {
          _orders = orders;
          _ordersLoading = false;
        });
      }
      return orders;
    } catch (e) {
      if (mounted) setState(() => _ordersLoading = false);
      return [];
    }
  }



  Future<void> _saveActive(bool value) async {
    setState(() { _saving = true; });
    try {
      await _service.updateCustomer(widget.customerId, isActive: value);
      await _load();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating customer: $e')),
        );
      }
    } finally {
      setState(() { _saving = false; });
    }
  }

  Future<void> _saveProfile() async {
    setState(() { _saving = true; });
    try {
      await _service.updateCustomer(
        widget.customerId,
        fullName: _nameCtrl.text.trim(),
        email: _emailCtrl.text.trim().isEmpty ? null : _emailCtrl.text.trim(),
        phone: _phoneCtrl.text.trim().isEmpty ? null : _phoneCtrl.text.trim(),
        country: _countryCtrl.text.trim().isEmpty ? null : _countryCtrl.text.trim(),
        notes: _notesCtrl.text.trim().isEmpty ? null : _notesCtrl.text.trim(),
      );
      await _load();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Customer updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating customer: $e')),
        );
      }
    } finally {
      setState(() { _saving = false; });
    }
  }

  // Action methods

  Future<void> _sendEmail() async {
    if (_customer?.email == null || _customer!.email!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No email address available')),
      );
      return;
    }
    
    final url = Uri.parse('mailto:${_customer!.email}');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cannot send email')),
        );
      }
    }
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$label copied to clipboard')),
    );
  }

  // Address management methods
  void _showAddAddressDialog() {
    _showAddressDialog();
  }

  void _showEditAddressDialog(CustomerAddress address) {
    _showAddressDialog(address: address);
  }

  void _showAddressDialog({CustomerAddress? address}) {
    final isEditing = address != null;
    final formKey = GlobalKey<FormState>();
    final titleCtrl = TextEditingController(text: address?.title ?? 'Home');
    final line1Ctrl = TextEditingController(text: address?.addressLine1 ?? '');
    final line2Ctrl = TextEditingController(text: address?.addressLine2 ?? '');
    final cityCtrl = TextEditingController(text: address?.city ?? '');
    final stateCtrl = TextEditingController(text: address?.state ?? '');
    final postalCtrl = TextEditingController(text: address?.postalCode ?? '');
    final countryCtrl = TextEditingController(text: address?.country ?? '');
    
    bool isCountryPickerLoading = false;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(isEditing ? 'Edit Address' : 'Add New Address'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Address Title (Required)
                    TextFormField(
                      controller: titleCtrl,
                      decoration: const InputDecoration(
                        labelText: 'Address Title',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.label_outline),
                        hintText: 'e.g., Home, Office',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Address title is required';
                        }
                        return null;
                      },
                      textCapitalization: TextCapitalization.words,
                    ),
                    const SizedBox(height: 12),
                    
                    // Address Line 1 (Required)
                    TextFormField(
                      controller: line1Ctrl,
                      decoration: const InputDecoration(
                        labelText: 'Address Line 1 *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on_outlined),
                        hintText: 'Street address, building number',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Address line 1 is required';
                        }
                        return null;
                      },
                      textCapitalization: TextCapitalization.words,
                    ),
                    const SizedBox(height: 12),
                    
                    // Address Line 2 (Optional)
                    TextFormField(
                      controller: line2Ctrl,
                      decoration: const InputDecoration(
                        labelText: 'Address Line 2 (Optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on_outlined),
                        hintText: 'Apartment, suite, floor',
                      ),
                      textCapitalization: TextCapitalization.words,
                    ),
                    const SizedBox(height: 12),
                    
                    // City and State Row
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: cityCtrl,
                            decoration: const InputDecoration(
                              labelText: 'City *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.location_city),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'City is required';
                              }
                              return null;
                            },
                            textCapitalization: TextCapitalization.words,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            controller: stateCtrl,
                            decoration: const InputDecoration(
                              labelText: 'State/Province',
                              border: OutlineInputBorder(),
                            ),
                            textCapitalization: TextCapitalization.words,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    // Postal Code and Country Row
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: postalCtrl,
                            decoration: const InputDecoration(
                              labelText: 'Postal Code',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.markunread_mailbox),
                            ),
                            textCapitalization: TextCapitalization.characters,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: GestureDetector(
                            onTap: isCountryPickerLoading ? null : () async {
                              try {
                                setState(() => isCountryPickerLoading = true);
                                
                                await showCountryPicker(
                                  context: context,
                                  selectedCountry: null,
                                  onSelect: (country) {
                                    setState(() {
                                      countryCtrl.text = country.name;
                                    });
                                  },
                                );
                              } catch (e) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Error loading countries: $e'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              } finally {
                                setState(() => isCountryPickerLoading = false);
                              }
                            },
                            child: AbsorbPointer(
                              child: TextFormField(
                                controller: countryCtrl,
                                decoration: InputDecoration(
                                  labelText: 'Country *',
                                  border: const OutlineInputBorder(),
                                  prefixIcon: const Icon(Icons.public),
                                  suffixIcon: isCountryPickerLoading
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: Padding(
                                            padding: EdgeInsets.all(12.0),
                                            child: CircularProgressIndicator(strokeWidth: 2),
                                          ),
                                        )
                                      : const Icon(Icons.keyboard_arrow_down),
                                  hintText: 'Select country',
                                ),
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Country is required';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    // Required fields note
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '* Required fields',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState?.validate() == true) {
                  _saveAddress(
                    address,
                    titleCtrl.text,
                    line1Ctrl.text,
                    line2Ctrl.text,
                    cityCtrl.text,
                    stateCtrl.text,
                    postalCtrl.text,
                    countryCtrl.text,
                  );
                }
              },
              child: Text(isEditing ? 'Update' : 'Add'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveAddress(
    CustomerAddress? existingAddress,
    String title,
    String line1,
    String line2,
    String city,
    String state,
    String postal,
    String country,
  ) async {
    // Close the dialog first
    Navigator.of(context).pop();
    
    // Show loading state
    setState(() => _addressLoading = true);

    try {
      // Create the address object
      final address = CustomerAddress(
        id: existingAddress?.id,
        customerId: widget.customerId,
        title: title.trim(),
        addressLine1: line1.trim(),
        addressLine2: line2.trim().isEmpty ? null : line2.trim(),
        city: city.trim(),
        state: state.trim().isEmpty ? null : state.trim(),
        postalCode: postal.trim().isEmpty ? null : postal.trim(),
        country: country.trim(),
        isDefault: existingAddress?.isDefault ?? _addresses.isEmpty,
      );

      // Save the address
      if (existingAddress != null) {
        await _service.updateAddress(existingAddress.id!, address);
      } else {
        await _service.createAddress(address);
      }

      // Reload the addresses list
      await _reloadAddresses();
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text('Address ${existingAddress != null ? 'updated' : 'added'} successfully'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Handle different types of errors
      String errorMessage = 'Failed to save address';
      
      if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('duplicate')) {
        errorMessage = 'An address with this information already exists.';
      } else if (e.toString().contains('invalid')) {
        errorMessage = 'Invalid address information. Please check your input.';
      } else {
        errorMessage = 'Error saving address: ${e.toString()}';
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _showAddressDialog(address: existingAddress),
            ),
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _addressLoading = false);
    }
  }

  Future<void> _reloadAddresses() async {
    try {
      final addresses = await _service.getCustomerAddresses(widget.customerId);
      if (mounted) {
        setState(() => _addresses = addresses);
      }
    } catch (e) {
      // Ignore reload errors
    }
  }

  Future<void> _deleteAddress(CustomerAddress address) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Address'),
        content: Text('Are you sure you want to delete "${address.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _addressLoading = true);
      try {
        await _service.deleteAddress(address.id!);
        await _reloadAddresses();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting address: $e')),
          );
        }
      } finally {
        if (mounted) setState(() => _addressLoading = false);
      }
    }
  }

  Future<void> _setDefaultAddress(CustomerAddress address) async {
    setState(() => _addressLoading = true);
    try {
      await _service.setDefaultAddress(widget.customerId, address.id!);
      await _reloadAddresses();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Default address updated')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error setting default address: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _addressLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Scaffold(
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _customer == null
              ? const Center(child: Text('Customer not found'))
              : NestedScrollView(
                  headerSliverBuilder: (context, innerBoxIsScrolled) => [
                    SliverAppBar(
                      expandedHeight: 180,
                      pinned: true,
                      elevation: 0,
                      backgroundColor: colorScheme.primary,
                      foregroundColor: colorScheme.onPrimary,
                      actions: [
                        IconButton(
                          onPressed: _load,
                          icon: const Icon(Icons.refresh),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'edit':
                                // Switch to first tab for editing
                                _tabController.animateTo(0);
                                break;
                              case 'delete':
                                _showDeleteDialog();
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('Edit Customer'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('Delete Customer', style: TextStyle(color: Colors.red)),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ],
                        ),
                      ],
                      flexibleSpace: FlexibleSpaceBar(
                        background: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                colorScheme.primary,
                                colorScheme.primary.withValues(alpha: 0.8),
                              ],
                            ),
                          ),
                                                    child: SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(20, 8, 20, 8),
                  child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                                  // Profile Avatar and basic info
                                  Hero(
                                    tag: 'customer-${widget.customerId}',
                                    child: CircleAvatar(
                                      radius: 32,
                                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                                      child: CircleAvatar(
                                        radius: 28,
                                        backgroundColor: _customer!.isActive ? Colors.white : Colors.grey[300],
                                child: Text(
                                  _customer!.fullName.isNotEmpty ? _customer!.fullName[0].toUpperCase() : '?',
                                          style: TextStyle(
                                            color: _customer!.isActive ? colorScheme.primary : Colors.grey[600],
                                            fontSize: 24,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                    Text(
                                      _customer!.fullName,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (_customer!.email != null && _customer!.email!.isNotEmpty) ...[
                                    const SizedBox(height: 2),
                                      Text(
                                        _customer!.email!,
                                      style: const TextStyle(
                                        color: Colors.white70,
                                        fontSize: 14,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                  const SizedBox(height: 18),
                                                                    // Quick action buttons
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      if (_customer!.email != null && _customer!.email!.isNotEmpty)
                                        _buildActionButton(
                                          icon: Icons.email,
                                          onTap: _sendEmail,
                                          color: Colors.blue,
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 28),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      bottom: TabBar(
                        controller: _tabController,
                        labelColor: Colors.white,
                        unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                        indicatorColor: Colors.white,
                        indicatorWeight: 3,
                        tabs: const [
                          Tab(
                            icon: Icon(Icons.person, size: 26),
                            text: 'Profile',
                          ),
                          Tab(
                            icon: Icon(Icons.location_on, size: 26),
                            text: 'Addresses',
                          ),
                          Tab(
                            icon: Icon(Icons.shopping_bag, size: 26),
                            text: 'Orders',
                          ),
                          Tab(
                            icon: Icon(Icons.analytics, size: 26),
                            text: 'Analytics',
                          ),
                        ],
                        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                        unselectedLabelStyle: const TextStyle(fontSize: 11),
                        isScrollable: false,
                      ),
                    ),
                  ],
                  body: TabBarView(
                    controller: _tabController,
                                children: [
                      _buildProfileTab(),
                      _buildAddressesTab(),
                      _buildOrdersTab(),
                      _buildAnalyticsTab(),
                    ],
                  ),
                ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: IconButton(
        onPressed: onTap,
        icon: Icon(icon, color: Colors.white),
        iconSize: 20,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Addresses tab
        return FloatingActionButton(
          onPressed: _showAddAddressDialog,
          child: const Icon(Icons.add),
        );
      default:
        return null;
    }
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool isRequired = false,
    String? copyValue,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label + (isRequired ? ' *' : ''),
          labelStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          suffixIcon: copyValue != null && copyValue.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.copy_rounded,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onPressed: () => _copyToClipboard(copyValue, label),
                  tooltip: 'Copy $label',
                )
              : null,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.error,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          alignLabelWithHint: maxLines > 1,
        ),
      ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    _active ? Icons.check_circle : Icons.cancel,
                    color: _active ? Colors.green : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                                  Text(
                          'Customer Status',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          _active ? 'Active Customer' : 'Inactive Customer',
                                    style: TextStyle(
                                      color: _active ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                    ),
                  ),
                  Switch.adaptive(
                    value: _active,
                    onChanged: _saving ? null : (v) {
                      setState(() => _active = v);
                      _saveActive(v);
                    },
                              ),
                            ],
                          ),
                        ),
                      ),
                      
          const SizedBox(height: 16),
                      
                                // Modern Customer Information Form
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with glassmorphism effect
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.person_outline,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Customer Information',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              if (_customer!.createdAt != null)
                                Text(
                                  'Member since ${_customer!.createdAt!.toLocal().toString().split(' ')[0]}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Modern Form Fields
                  _buildModernTextField(
                    controller: _nameCtrl,
                    label: 'Full Name',
                    icon: Icons.person_outline,
                    isRequired: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildModernTextField(
                    controller: _emailCtrl,
                    label: 'Email Address',
                    icon: Icons.email_outlined,
                    keyboardType: TextInputType.emailAddress,
                    copyValue: _customer!.email,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildModernTextField(
                    controller: _phoneCtrl,
                    label: 'Phone Number',
                    icon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                    copyValue: _customer!.phone,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildModernTextField(
                    controller: _countryCtrl,
                    label: 'Country',
                    icon: Icons.public_outlined,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildModernTextField(
                    controller: _notesCtrl,
                    label: 'Notes',
                    icon: Icons.note_outlined,
                    maxLines: 3,
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Modern Save Button
                  Container(
                    width: double.infinity,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: _saving 
                        ? null 
                        : LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary,
                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                            ],
                          ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: _saving 
                        ? null 
                        : [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                    ),
                    child: ElevatedButton(
                      onPressed: _saving ? null : _saveProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _saving ? Colors.grey[300] : Colors.transparent,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: _saving 
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Saving Changes...',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          )
                        : const Text(
                            'Save Changes',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressesTab() {
    return _addressLoading
        ? const Center(child: CircularProgressIndicator())
        : _addresses.isEmpty
            ? _buildEmptyAddresses()
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _addresses.length,
                itemBuilder: (context, index) => _buildAddressCard(_addresses[index]),
              );
  }

  Widget _buildEmptyAddresses() {
    return Center(
                        child: Padding(
        padding: const EdgeInsets.all(32),
                          child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
                            children: [
            Icon(
              Icons.location_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Addresses Added',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
                                      const SizedBox(height: 8),
                                      Text(
              'Add an address to help with deliveries and pickups.',
                                        style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
                                      ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
                                        onPressed: _showAddAddressDialog,
                                        icon: const Icon(Icons.add),
                                        label: const Text('Add First Address'),
                                      ),
                                    ],
                                  ),
                                ),
    );
  }

  Widget _buildAddressCard(CustomerAddress address) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                                        backgroundColor: address.isDefault ? Colors.blue : Colors.grey[300],
                                        child: Icon(
                                          address.isDefault ? Icons.home : Icons.location_on,
                                          color: address.isDefault ? Colors.white : Colors.grey[600],
                                          size: 20,
                                        ),
                                      ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            address.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (address.isDefault) ...[
                            const SizedBox(width: 8),
                            Chip(
                                              label: const Text('Default', style: TextStyle(fontSize: 10)),
                                              backgroundColor: Colors.blue[100],
                                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        address.addressLine1,
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                      if (address.addressLine2 != null && address.addressLine2!.isNotEmpty) ...[
                        Text(
                          address.addressLine2!,
                          style: TextStyle(color: Colors.grey[700]),
                        ),
                      ],
                      Text(
                        '${address.city}, ${address.country}',
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showEditAddressDialog(address);
                        break;
                      case 'default':
                        _setDefaultAddress(address);
                        break;
                      case 'delete':
                        _deleteAddress(address);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Edit'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    if (!address.isDefault)
                      const PopupMenuItem(
                        value: 'default',
                        child: ListTile(
                          leading: Icon(Icons.home),
                          title: Text('Set as Default'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: Colors.red),
                        title: Text('Delete', style: TextStyle(color: Colors.red)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersTab() {
    return _ordersLoading
        ? const Center(child: CircularProgressIndicator())
        : _orders.isEmpty
            ? _buildEmptyOrders()
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _orders.length,
                itemBuilder: (context, index) => _buildOrderCard(_orders[index]),
              );
  }

  Widget _buildEmptyOrders() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Orders Yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This customer hasn\'t placed any orders yet.',
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(order.status),
          child: Text(
            order.statusIcon,
            style: const TextStyle(fontSize: 16),
          ),
        ),
        title: Text(
          order.orderNumber,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Status: ${order.status.displayName}'),
            Text('Total: ${_formatAmount(order.totalAmount)}'),
            Text('Date: ${order.createdAt.toLocal().toString().split(' ')[0]}'),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
                                      isThreeLine: true,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => OrderDetailPage(orderId: order.id),
            ),
          );
        },
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.accepted:
        return Colors.blue;
      case OrderStatus.pickedUp:
        return Colors.purple;
      case OrderStatus.inProcess:
        return Colors.amber;
      case OrderStatus.readyForPickup:
        return Colors.green;
      case OrderStatus.outForDelivery:
        return Colors.amber;
      case OrderStatus.completed:
        return Colors.teal;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  Widget _buildAnalyticsTab() {
    final totalOrders = _orders.length;
    final activeOrders = _orders.where((o) => o.isActive).length;
    final totalSpent = _orders.fold<double>(0.0, (sum, order) => sum + order.totalAmount);
    final averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0.0;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Currency indicator
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.attach_money,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'All amounts shown in $_currencySymbol',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          // Summary Stats
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildStatCard(
                'Total Orders',
                totalOrders.toString(),
                Icons.shopping_bag,
                Colors.blue,
              ),
              _buildStatCard(
                'Active Orders',
                activeOrders.toString(),
                Icons.pending_actions,
                Colors.orange,
              ),
              _buildStatCard(
                'Total Spent',
                _formatAmount(totalSpent),
                Icons.attach_money,
                Colors.green,
              ),
              _buildStatCard(
                'Avg. Order',
                _formatAmount(averageOrderValue),
                Icons.trending_up,
                Colors.purple,
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Order Status Distribution
          if (totalOrders > 0) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Order Status Distribution',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 16),
                    ..._buildStatusDistribution(),
                            ],
                          ),
                        ),
                      ),
            
            const SizedBox(height: 20),
            
            // Recent Activity
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Recent Orders',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 16),
                    ...(_orders.take(3).map((order) => ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: CircleAvatar(
                        radius: 16,
                        backgroundColor: _getStatusColor(order.status),
                        child: Text(
                          order.statusIcon,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      title: Text(order.orderNumber),
                      subtitle: Text(order.status.displayName),
                      trailing: Text(_formatAmount(order.totalAmount)),
                    ))),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildStatusDistribution() {
    final statusCounts = <OrderStatus, int>{};
    for (final order in _orders) {
      statusCounts[order.status] = (statusCounts[order.status] ?? 0) + 1;
    }

    return statusCounts.entries.map((entry) {
      final percentage = (entry.value / _orders.length * 100).round();
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: _getStatusColor(entry.key),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(entry.key.displayName)),
            Text('${entry.value} ($percentage%)'),
          ],
        ),
      );
    }).toList();
  }

    void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete "${_customer!.fullName}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);
              
              navigator.pop(); // Close dialog first
              
              try {
                await _service.deleteCustomer(widget.customerId);
                if (mounted) {
                  navigator.pop(); // Go back to customer list
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Customer deleted successfully')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Error deleting customer: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
                ),
    );
  }
}
