import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/auth_service.dart';
import '../dashboard/dashboard_page.dart';
import 'register_page.dart';
import 'forgot_password_page.dart';
import 'otp_verification_page.dart';
import 'package:email_validator/email_validator.dart';
import '../../services/biometric_service.dart';
import '../../services/settings_service.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../support/privacy_policy_page.dart';
import '../support/terms_of_service_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isOTPSending = false;
  bool _rememberMe = true;
  bool _obscurePassword = true;
  bool _useOTPLogin = false;
  Timer? _countdownTimer;
  int _countdownSeconds = 60;
  bool _canResend = false;
  late AuthService authService;
  late BiometricService biometricService;
  late SettingsService settingsService;
  bool _biometricAvailable = false;
  bool _biometricEnabled = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    authService = AuthService(supabase: Supabase.instance.client);
    biometricService = BiometricService();
    settingsService = SettingsService(supabase: Supabase.instance.client);
    _checkBiometricAvailability();
    _loadVersion();
  }

  Future<void> _loadVersion() async {
    final info = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        _appVersion = 'v${info.version}+${info.buildNumber}';
      });
    }
  }

  Future<void> _checkBiometricAvailability() async {
    final isAvailable = await biometricService.isBiometricAvailable();
    final isEnabled = await authService.isBiometricEnabled();

    if (mounted) {
      setState(() {
        _biometricAvailable = isAvailable;
        _biometricEnabled = isEnabled;
      });
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        if (_useOTPLogin) {
          await _verifyOTPLogin();
        } else {
          await authService.signIn(
            _emailController.text,
            _passwordController.text,
          );

          if (_rememberMe) {
            authService.saveCredentials(
              _emailController.text,
              _passwordController.text,
            );
          }

          // After successful login, ensure settings are set, then navigate
          final _ = await _maybePromptForSettings();
          if (!mounted) return;
          if (_biometricAvailable && !_biometricEnabled && !_useOTPLogin) {
            _offerBiometricSetup();
          } else {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const DashboardPage()),
            );
          }
        }
      } catch (e) {
        if (e is EmailNotVerifiedException) {
          _showVerificationDialog(_emailController.text.trim());
        } else {
          _showErrorDialog(e.toString());
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<bool> _maybePromptForSettings() async {
    final settings = await settingsService.getSettings();
    final companyMissing = settings?.companyName == null || (settings!.companyName?.trim().isEmpty ?? true);
    final currencyMissing = settings?.currencyCode == null || (settings!.currencyCode?.trim().isEmpty ?? true);
    if (!companyMissing && !currencyMissing) return false;

    String companyName = settings?.companyName ?? '';
    String currencyCode = settings?.currencyCode ?? '';

    final formKey = GlobalKey<FormState>();
    final companyCtrl = TextEditingController(text: companyName);
    final currencyCtrl = TextEditingController(text: currencyCode);

    if (!mounted) return false;
    final proceed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => AlertDialog(
        title: const Text('Complete Your Setup'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: companyCtrl,
                decoration: const InputDecoration(labelText: 'Company Name', border: OutlineInputBorder()),
                validator: (v) => v == null || v.trim().isEmpty ? 'Required' : null,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: currencyCtrl,
                decoration: const InputDecoration(labelText: 'Currency Code (e.g., USD)', border: OutlineInputBorder()),
                textCapitalization: TextCapitalization.characters,
                validator: (v) => v == null || v.trim().length < 3 ? '3-letter code' : null,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                await settingsService.upsertSettings(
                  companyName: companyCtrl.text.trim(),
                  currencyCode: currencyCtrl.text.trim(),
                );
                if (ctx.mounted) Navigator.of(ctx).pop(true);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
    return proceed ?? false;
  }

  Future<void> _sendOTPLogin() async {
    if (_emailController.text.isEmpty ||
        !EmailValidator.validate(_emailController.text)) {
      _showErrorDialog('Please enter a valid email address first.');
      return;
    }

    setState(() {
      _isOTPSending = true;
    });

    try {
      await authService.sendLoginOTP(_emailController.text.trim());
      _showInfoDialog('Login code sent to ${_emailController.text.trim()}');
      _startCountdown(); // Start countdown after sending OTP
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isOTPSending = false;
        });
      }
    }
  }

    void _startCountdown() {
    setState(() {
      _countdownSeconds = 60;
      _canResend = false;
    });

    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdownSeconds > 0) {
            _countdownSeconds--;
          } else {
            _canResend = true;
            timer.cancel();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _verifyOTPLogin() async {
    if (_otpController.text.isEmpty || _otpController.text.length != 6) {
      _showErrorDialog('Please enter a valid 6-digit OTP.');
      return;
    }

    try {
      await authService.verifyLoginOTP(
        _emailController.text.trim(),
        _otpController.text.trim(),
      );

      // Navigate to dashboard
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const DashboardPage()),
        );
      }
    } catch (e) {
      rethrow; // Re-throw to be caught by _submit()
    }
  }

  void _showInfoDialog(String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Code Sent'),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            child: const Text('Okay'),
            onPressed: () {
              Navigator.of(ctx).pop();
            },
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('An Error Occurred!'),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            child: const Text('Okay'),
            onPressed: () {
              Navigator.of(ctx).pop();
            },
          ),
        ],
      ),
    );
  }

  void _showVerificationDialog(String email) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.email, color: Colors.orange),
            SizedBox(width: 8),
            Text('Account Not Verified'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your account email has not been verified yet. You need to verify your email before you can log in.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Check your email ($email) for a verification code.',
                      style: const TextStyle(fontSize: 14, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(ctx).pop();
              // Navigate to verification page
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => OTPVerificationPage(
                    email: email,
                    isEmailVerification: true,
                  ),
                ),
              );
            },
            icon: const Icon(Icons.verified_user),
            label: const Text('Verify Now'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _offerBiometricSetup() {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Enable Biometric Login'),
        content: const Text(
          'Would you like to enable fingerprint/face recognition for faster login?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const DashboardPage()),
              );
            },
            child: const Text('Skip'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(ctx).pop();
              await _setupBiometric();
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  Future<void> _setupBiometric() async {
    try {
      final isAuthenticated = await biometricService
          .authenticateWithBiometrics();

      if (isAuthenticated) {
        await authService.enableBiometricAuth(
          _emailController.text,
          _passwordController.text,
        );

        if (mounted) {
          setState(() {
            _biometricEnabled = true;
          });
        }

        _showInfoDialog('Biometric authentication enabled successfully!');

        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const DashboardPage()),
            );
          }
        });
      }
    } catch (e) {
      _showErrorDialog(
        'Failed to setup biometric authentication: ${e.toString()}',
      );
    }
  }

  Future<void> _authenticateWithBiometric() async {
    if (!_biometricAvailable || !_biometricEnabled) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final isAuthenticated = await biometricService
          .authenticateWithBiometrics();

      if (isAuthenticated) {
        final credentials = await authService.getBiometricCredentials();
        if (credentials != null) {
          await authService.signIn(
            credentials['email']!,
            credentials['password']!,
          );

          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const DashboardPage()),
            );
          }
        }
      }
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _emailController.dispose();
    _passwordController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome Back'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(Icons.login, size: 80, color: Colors.blue),
                  const SizedBox(height: 20),
                  const Text(
                    'Sign In to Laundry Pro',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email.';
                      }
                      if (!EmailValidator.validate(value)) {
                        return 'Please enter a valid email.';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  if (!_useOTPLogin)
                    TextFormField(
                      controller: _passwordController,
                      decoration: InputDecoration(
                        labelText: 'Password',
                        prefixIcon: const Icon(Icons.lock),
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                      ),
                      obscureText: _obscurePassword,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your password.';
                        }
                        return null;
                      },
                    ),
                  if (_useOTPLogin)
                    TextFormField(
                      controller: _otpController,
                      decoration: const InputDecoration(
                        labelText: 'Enter OTP',
                        prefixIcon: Icon(Icons.lock_open),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      textAlign: TextAlign.center,
                      validator: (value) {
                        if (value == null ||
                            value.isEmpty ||
                            value.length != 6) {
                          return 'Please enter the OTP.';
                        }
                        return null;
                      },
                    ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Checkbox(
                        value: _useOTPLogin,
                        onChanged: (value) {
                          setState(() {
                            _useOTPLogin = value ?? false;
                            // Clear OTP field when toggling
                            if (!_useOTPLogin) {
                              _otpController.clear();
                            }
                          });
                        },
                      ),
                      const Text('Login with OTP'),
                    ],
                  ),
                  if (_useOTPLogin)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: _isOTPSending
                            ? const Center(child: CircularProgressIndicator())
                            : _canResend || _countdownSeconds == 60
                            ? OutlinedButton.icon(
                                onPressed: _sendOTPLogin,
                                icon: const Icon(Icons.send),
                                label: Text(
                                  _countdownSeconds == 60
                                      ? 'Send Login Code'
                                      : 'Resend Code',
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.blue,
                                  side: const BorderSide(color: Colors.blue),
                                ),
                              )
                            : OutlinedButton.icon(
                                onPressed: null,
                                icon: const Icon(Icons.timer),
                                label: Text(
                                  'Resend in ${_formatTime(_countdownSeconds)}',
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.grey,
                                  side: const BorderSide(color: Colors.grey),
                                ),
                              ),
                      ),
                    ),
                  if (!_useOTPLogin)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(
                          children: [
                            Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                            ),
                            const Text('Remember Me'),
                          ],
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) =>
                                    const ForgotPasswordPage(),
                              ),
                            );
                          },
                          child: const Text('Forgot Password?'),
                        ),
                      ],
                    ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : ElevatedButton(
                            onPressed: _submit,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text(
                              'Sign In',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                  ),
                  const SizedBox(height: 20),
                  if (_biometricAvailable && _biometricEnabled)
                    Column(
                      children: [
                        const Text('or'),
                        const SizedBox(height: 10),
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: OutlinedButton.icon(
                            onPressed: _authenticateWithBiometric,
                            icon: const Icon(Icons.fingerprint),
                            label: const Text('Sign in with Biometrics'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.blue,
                              side: const BorderSide(color: Colors.blue),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text("Don't have an account? "),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const RegisterPage(),
                            ),
                          );
                        },
                        child: const Text('Sign Up'),
                      ),
                    ],
                  ),
                  // Footer links
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const PrivacyPolicyPage(),
                            ),
                          );
                        },
                        child: const Text('Privacy Policy'),
                      ),
                      const Text(' • '),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const TermsOfServicePage(),
                            ),
                          );
                        },
                        child: const Text('Terms of Service'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _appVersion.isEmpty ? '' : _appVersion,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
