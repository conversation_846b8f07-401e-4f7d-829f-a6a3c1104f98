import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/custom_expense_category_model.dart';

class CustomExpenseCategoryService {
  final SupabaseClient _supabase;

  CustomExpenseCategoryService({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  // List all active custom categories for current user
  Future<List<CustomExpenseCategory>> listCustomCategories({bool? isActive}) async {
    try {
      final query = _supabase
          .from('custom_expense_categories')
          .select()
          .eq('is_active', isActive ?? true)
          .order('name', ascending: true);

      final response = await query;
      return (response as List)
          .map((r) => CustomExpenseCategory.fromJson(Map<String, dynamic>.from(r)))
          .toList();
    } catch (e) {
      throw 'Error fetching custom categories: $e';
    }
  }

  // Get a specific custom category
  Future<CustomExpenseCategory> getCustomCategory(String id) async {
    try {
      final response = await _supabase
          .from('custom_expense_categories')
          .select()
          .eq('id', id)
          .single();

      return CustomExpenseCategory.fromJson(response);
    } catch (e) {
      throw 'Error fetching custom category: $e';
    }
  }

  // Create a new custom category
  Future<CustomExpenseCategory> createCustomCategory({
    required String name,
    String? description,
    String? iconName,
    String? colorHex,
    bool isActive = true,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw 'User not authenticated';

      // Validate inputs
      if (name.trim().isEmpty) throw 'Category name cannot be empty';
      if (colorHex != null && !RegExp(r'^#[0-9A-Fa-f]{6}$').hasMatch(colorHex)) {
        throw 'Invalid color format. Use hex format like #FF5722';
      }
      if (iconName != null && !CustomExpenseCategory.availableIcons.contains(iconName)) {
        throw 'Invalid icon name';
      }

      final data = {
        'name': name.trim(),
        'description': description?.trim(),
        'icon_name': iconName,
        'color_hex': colorHex,
        'is_active': isActive,
        'created_by': user.id,
      };

      final response = await _supabase
          .from('custom_expense_categories')
          .insert(data)
          .select()
          .single();

      return CustomExpenseCategory.fromJson(response);
    } catch (e) {
      throw 'Error creating custom category: $e';
    }
  }

  // Update an existing custom category
  Future<CustomExpenseCategory> updateCustomCategory({
    required String id,
    String? name,
    String? description,
    String? iconName,
    String? colorHex,
    bool? isActive,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) {
        if (name.trim().isEmpty) throw 'Category name cannot be empty';
        updateData['name'] = name.trim();
      }
      if (description != null) updateData['description'] = description.trim();
      if (iconName != null) {
        if (!CustomExpenseCategory.availableIcons.contains(iconName)) {
          throw 'Invalid icon name';
        }
        updateData['icon_name'] = iconName;
      }
      if (colorHex != null) {
        if (!RegExp(r'^#[0-9A-Fa-f]{6}$').hasMatch(colorHex)) {
          throw 'Invalid color format. Use hex format like #FF5722';
        }
        updateData['color_hex'] = colorHex;
      }
      if (isActive != null) updateData['is_active'] = isActive;

      final response = await _supabase
          .from('custom_expense_categories')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

      return CustomExpenseCategory.fromJson(response);
    } catch (e) {
      throw 'Error updating custom category: $e';
    }
  }

  // Delete a custom category
  Future<void> deleteCustomCategory(String id) async {
    try {
      // Check if category is being used by any expenses
      final expensesUsingCategory = await _supabase
          .from('expenses')
          .select('id')
          .eq('custom_category_id', id)
          .limit(1);

      if ((expensesUsingCategory as List).isNotEmpty) {
        throw 'Cannot delete category because it is being used by existing expenses. Deactivate it instead.';
      }

      await _supabase
          .from('custom_expense_categories')
          .delete()
          .eq('id', id);
    } catch (e) {
      throw 'Error deleting custom category: $e';
    }
  }

  // Deactivate a custom category (soft delete)
  Future<CustomExpenseCategory> deactivateCustomCategory(String id) async {
    try {
      return await updateCustomCategory(id: id, isActive: false);
    } catch (e) {
      throw 'Error deactivating custom category: $e';
    }
  }

  // Check if a category name is available for the current user
  Future<bool> isCategoryNameAvailable(String name, {String? excludeId}) async {
    try {
      final trimmedName = name.trim();
      final query = excludeId != null
          ? _supabase
              .from('custom_expense_categories')
              .select('id')
              .eq('name', trimmedName)
              .neq('id', excludeId)
              .limit(1)
          : _supabase
              .from('custom_expense_categories')
              .select('id')
              .eq('name', trimmedName)
              .limit(1);

      final response = await query;
      return (response as List).isEmpty;
    } catch (e) {
      throw 'Error checking category name availability: $e';
    }
  }
}
