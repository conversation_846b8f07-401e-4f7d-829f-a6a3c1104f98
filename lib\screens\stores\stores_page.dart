import 'package:flutter/material.dart';
import '../../models/store_model.dart';
import '../../services/store_service.dart';
import 'store_detail_page.dart';
import 'dart:async';

class StoresPage extends StatefulWidget {
  const StoresPage({super.key});

  @override
  State<StoresPage> createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  final StoreService _storeService = StoreService();
  final TextEditingController _searchController = TextEditingController();
  List<Store> _stores = [];
  List<Store> _filteredStores = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'name_asc'; // name_asc, name_desc, newest, oldest
  String? _statusFilter; // null: all, 'active', 'inactive'

  @override
  void initState() {
    super.initState();
    _loadStores();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
        _applyFiltersAndSort();
      });
    });
  }

  Future<void> _loadStores() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final stores = await _storeService.getStores();
      setState(() {
        _stores = stores;
        _applyFiltersAndSort();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading stores: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stores'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          // Sort menu
          PopupMenuButton<String>(
            tooltip: 'Sort',
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
                _applyFiltersAndSort();
              });
            },
            itemBuilder: (context) => const [
              PopupMenuItem(value: 'name_asc', child: Text('Name (A → Z)')),
              PopupMenuItem(value: 'name_desc', child: Text('Name (Z → A)')),
              PopupMenuItem(value: 'newest', child: Text('Newest First')),
              PopupMenuItem(value: 'oldest', child: Text('Oldest First')),
            ],
          ),
          // Status filter
          PopupMenuButton<String?>(
            tooltip: 'Filter by status',
            icon: Icon(
              Icons.filter_list,
              color: _statusFilter == null
                  ? Colors.white
                  : (_statusFilter == 'active' ? Colors.greenAccent : Colors.orangeAccent),
            ),
            initialValue: _statusFilter,
            onSelected: (value) {
              setState(() {
                _statusFilter = value;
                _applyFiltersAndSort();
              });
            },
            itemBuilder: (context) => const [
              PopupMenuItem(value: null, child: Text('All')),
              PopupMenuItem(value: 'active', child: Text('Active')),
              PopupMenuItem(value: 'inactive', child: Text('Inactive')),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addStore,
            tooltip: 'Add New Store',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _stores.isEmpty
              ? _buildEmptyState()
              : Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search
                      TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search stores by name, number, or country...',
                          prefixIcon: const Icon(Icons.search, color: Colors.blue),
                          suffixIcon: _searchQuery.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear, color: Colors.grey),
                                  onPressed: () => _searchController.clear(),
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      // Filters summary chips
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          Chip(
                            avatar: Icon(_getSortIcon(), size: 16),
                            label: Text(_getSortLabel()),
                            backgroundColor: Colors.blue.withValues(alpha: 0.1),
                          ),
                          if (_statusFilter != null)
                            Chip(
                              avatar: Icon(
                                _statusFilter == 'active' ? Icons.check_circle : Icons.pause_circle,
                                color: _statusFilter == 'active' ? Colors.green : Colors.orange,
                                size: 16,
                              ),
                              label: Text(_statusFilter == 'active' ? 'Active' : 'Inactive'),
                              onDeleted: () {
                                setState(() {
                                  _statusFilter = null;
                                  _applyFiltersAndSort();
                                });
                              },
                            ),
                          Chip(
                            label: Text('${_filteredStores.length} stores'),
                            backgroundColor: Colors.grey.withValues(alpha: 0.1),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: _loadStores,
                          child: ListView.builder(
                            itemCount: _filteredStores.length,
                            itemBuilder: (context, index) {
                              return _buildStoreListItem(_filteredStores[index]);
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  IconData _getSortIcon() {
    switch (_sortBy) {
      case 'name_asc':
        return Icons.sort_by_alpha;
      case 'name_desc':
        return Icons.sort_by_alpha;
      case 'newest':
        return Icons.schedule;
      case 'oldest':
        return Icons.history;
      default:
        return Icons.sort;
    }
  }

  String _getSortLabel() {
    switch (_sortBy) {
      case 'name_asc':
        return 'Name (A → Z)';
      case 'name_desc':
        return 'Name (Z → A)';
      case 'newest':
        return 'Newest First';
      case 'oldest':
        return 'Oldest First';
      default:
        return 'Sort';
    }
  }

  void _applyFiltersAndSort() {
    List<Store> result = List.from(_stores);

    // Status filter
    if (_statusFilter == 'active') {
      result = result.where((s) => s.isActive).toList();
    } else if (_statusFilter == 'inactive') {
      result = result.where((s) => !s.isActive).toList();
    }

    // Search filter
    if (_searchQuery.isNotEmpty) {
      final q = _searchQuery.toLowerCase();
      result = result.where((s) {
        final name = s.name.toLowerCase();
        final number = (s.storeNumber ?? '').toLowerCase();
        final country = (s.country ?? '').toLowerCase();
        return name.contains(q) || number.contains(q) || country.contains(q);
      }).toList();
    }

    // Sort
    switch (_sortBy) {
      case 'name_asc':
        result.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
        break;
      case 'name_desc':
        result.sort((a, b) => b.name.toLowerCase().compareTo(a.name.toLowerCase()));
        break;
      case 'newest':
        result.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'oldest':
        result.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
    }

    _filteredStores = result;
  }

  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shimmer effect for title
          _buildShimmerContainer(height: 24, width: 150),
          const SizedBox(height: 16),
          
          // Shimmer effect for store cards
          Expanded(
            child: ListView.builder(
              itemCount: 3, // Show 3 placeholder items
              itemBuilder: (context, index) {
                return _buildShimmerStoreCard();
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildShimmerContainer({required double height, double? width, double borderRadius = 8}) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: _ShimmerEffect(),
    );
  }
  
  Widget _buildShimmerStoreCard() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                _buildShimmerContainer(height: 36, width: 36, borderRadius: 8),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildShimmerContainer(height: 20, width: 120),
                      const SizedBox(height: 4),
                      _buildShimmerContainer(height: 16, width: 80),
                    ],
                  ),
                ),
                _buildShimmerContainer(height: 24, width: 60, borderRadius: 12),
              ],
            ),
            const SizedBox(height: 12),
            
            // Address
            Row(
              children: [
                _buildShimmerContainer(height: 16, width: 16, borderRadius: 8),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildShimmerContainer(height: 16, width: double.infinity),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            // Phone
            Row(
              children: [
                _buildShimmerContainer(height: 16, width: 16, borderRadius: 8),
                const SizedBox(width: 8),
                _buildShimmerContainer(height: 16, width: 120),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildShimmerContainer(height: 32, width: 70),
                const SizedBox(width: 8),
                _buildShimmerContainer(height: 32, width: 80),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.store,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No stores available',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to add your first store',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addStore,
            icon: const Icon(Icons.add),
            label: const Text('Add Store'),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreListItem(Store store) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _viewStore(store),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with store name and status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: store.isActive 
                          ? Colors.green.withValues(alpha: 0.1) 
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.store,
                      color: store.isActive ? Colors.green : Colors.grey,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          store.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (store.storeNumber != null)
                          Container(
                            margin: const EdgeInsets.only(top: 4),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              store.storeNumber!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: store.isActive 
                          ? Colors.green.withValues(alpha: 0.1) 
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      store.isActive ? 'Active' : 'Inactive',
                      style: TextStyle(
                        fontSize: 12,
                        color: store.isActive ? Colors.green[700] : Colors.red[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Address
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      store.address,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Contact info
              if (store.phone != null && store.phone!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      store.phone!,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
              
              if (store.email != null && store.email!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.email,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      store.email!,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
              
              if (store.country != null && store.country!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.public,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      store.country!,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _editStore(store),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _confirmDeleteStore(store),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('Delete'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmDeleteStore(Store store) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Delete Store'),
        content: Text('Are you sure you want to delete "${store.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(ctx).pop();
              await _deleteStore(store.id);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('"${store.name}" deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteStore(String storeId) async {
    await _storeService.deleteStore(storeId);
    _loadStores();
  }

  void _addStore() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StoreDetailPage(),
      ),
    ).then((_) => _loadStores());
  }

  void _editStore(Store store) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreDetailPage(store: store),
      ),
    ).then((_) => _loadStores());
  }

  void _viewStore(Store store) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreDetailPage(store: store, isViewOnly: true),
      ),
    ).then((_) => _loadStores());
  }
}

// Shimmer effect widget
class _ShimmerEffect extends StatefulWidget {
  @override
  _ShimmerEffectState createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<_ShimmerEffect> {
  late Timer _timer;
  double _value = 0.0;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 1500), (_) {
      setState(() {
        _value = _value == 0.0 ? 1.0 : 0.0;
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1500),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[300]!,
            Colors.grey[100]!,
            Colors.grey[300]!,
          ],
          stops: [0.0, _value, 1.0],
        ),
      ),
    );
  }
}