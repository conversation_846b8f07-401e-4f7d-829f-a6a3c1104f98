import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/staff_profile_model.dart';

class StaffService {
  final SupabaseClient _supabase;
  StaffService({SupabaseClient? supabase}) : _supabase = supabase ?? Supabase.instance.client;

  Future<List<StaffProfile>> listStaff({
    String status = 'all', // all | active | inactive
    bool? emailEnabled,     // null = all, true/false filter
    String? search,
    String sortBy = 'staff_id', // staff_id | full_name | created_at
    bool ascending = true,
  }) async {
    // Build filters first (keeps builder as PostgrestFilterBuilder), then order at the end
    var query = _supabase.from('staff_profiles').select();
    if (status == 'active') {
      query = query.eq('is_active', true);
    } else if (status == 'inactive') {
      query = query.eq('is_active', false);
    }
    if (emailEnabled != null) {
      query = query.eq('email_enabled', emailEnabled);
    }
    final rows = await query.order(sortBy, ascending: ascending);
    var list = (rows as List).map((e) => StaffProfile.fromJson(e)).toList();
    if (search != null && search.trim().isNotEmpty) {
      final q = search.toLowerCase();
      list = list.where((s) =>
        s.fullName.toLowerCase().contains(q) ||
        (s.email ?? '').toLowerCase().contains(q) ||
        (s.phone ?? '').toLowerCase().contains(q) ||
        s.staffId.toString().contains(q)
      ).toList();
    }
    return list;
  }

  Future<StaffProfile?> getStaff(String id) async {
    final row = await _supabase.from('staff_profiles').select().eq('id', id).single();
    return StaffProfile.fromJson(row);
  }

  Future<void> setActive(String id, bool active) async {
    await _supabase.rpc('set_staff_active', params: {'p_user': id, 'p_active': active});
  }



  Future<void> deleteStaff(String id) async {
    await _supabase.from('staff_profiles').delete().eq('id', id);
  }

  // Check if current user is staff
  Future<bool> isCurrentUserStaff() async {
    try {
      final result = await _supabase.rpc('is_staff');
      return result as bool? ?? false;
    } catch (e) {
      return false;
    }
  }

  // Create staff with email (available to all authenticated users)
  Future<Map<String, dynamic>> createStaffWithEmail({
    String? email,
    required String fullName,
    String? phone,
    String? password,
    bool? emailEnabled,
  }) async {
    try {
      final actualPassword = password ?? 'TempPass${DateTime.now().millisecondsSinceEpoch}!';
      final useEmail = email != null && (emailEnabled ?? false);
      
      // Always create with a temporary email first to avoid trigger issues
      final tempEmail = useEmail ? email : 'staff_${DateTime.now().millisecondsSinceEpoch}@noemail.local';
      
      // Create user without triggering staff creation
      final response = await _supabase.auth.signUp(
        email: tempEmail,
        password: actualPassword,
        data: {
          // Don't set is_staff here to avoid trigger
          'full_name': fullName,
          'phone': phone,
          'password': actualPassword,
          'email_enabled': emailEnabled ?? false,
          'created_by_admin': true,
        },
      );
      
      if (response.user == null) {
        throw Exception('Failed to create user: No user returned from signup');
      }
      
      // Create staff profile manually using our function
      await _supabase.rpc('create_staff_profile_for_user', params: {
        'p_user_id': response.user!.id,
        'p_full_name': fullName,
        'p_email': useEmail ? email : null,
        'p_phone': phone,
        'p_password': actualPassword,
        'p_email_enabled': emailEnabled ?? false,
      });
      
      // Ensure user is properly activated
      await _supabase.rpc('ensure_staff_active', params: {
        'p_user_id': response.user!.id,
      });
      
      return {
        'user_id': response.user!.id,
        'temp_password': actualPassword,
      };
    } catch (e) {
      throw Exception('Error creating staff: $e');
    }
  }

  // Update staff profile
  Future<void> updateStaffProfile({
    required String id,
    String? fullName,
    String? email,
    String? phone,
    String? password,
    bool? emailEnabled,
  }) async {
    final updates = <String, dynamic>{};
    if (fullName != null) updates['full_name'] = fullName;
    if (email != null) updates['email'] = email;
    if (phone != null) updates['phone'] = phone;
    if (password != null) updates['password'] = password;
    if (emailEnabled != null) updates['email_enabled'] = emailEnabled;
    
    if (updates.isNotEmpty) {
      await _supabase.from('staff_profiles').update(updates).eq('id', id);
    }
  }

  Future<String?> getStaffTempPassword(String userId) async {
    return await _supabase.rpc('get_staff_temp_password', params: {
      'p_user_id': userId,
    });
  }

  Future<void> clearTempPassword(String userId) async {
    await _supabase.rpc('clear_temp_password', params: {
      'p_user_id': userId,
    });
  }
}
