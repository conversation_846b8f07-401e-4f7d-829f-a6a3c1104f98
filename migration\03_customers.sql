-- Customers & Customer Addresses Schema for Laundry Pro
-- Dedicated customer entities separate from auth users

-- ============================================
-- SAFETY DROPS (idempotent)
-- ============================================

-- Drop triggers first if tables exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customers') THEN
        DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
        DROP TRIGGER IF EXISTS normalize_customer_email_before_write ON customers;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customer_addresses') THEN
        DROP TRIGGER IF EXISTS update_customer_addresses_updated_at ON customer_addresses;
    END IF;
END $$;

-- Drop tables in dependency order
DROP TABLE IF EXISTS customer_addresses CASCADE;
DROP TABLE IF EXISTS customers CASCADE;

-- ============================================
-- EXTENSIONS AND UTILS
-- ============================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Ensure update_updated_at_column exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Normalize emails to lowercase on insert/update
CREATE OR REPLACE FUNCTION normalize_email()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    IF NEW.email IS NOT NULL THEN
        NEW.email := LOWER(TRIM(NEW.email));
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- CUSTOMERS
-- ============================================

CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name TEXT NOT NULL,
    email TEXT, -- Optional for walk-in customers
    phone TEXT,
    country TEXT,
    notes TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Unique index on normalized email when present
CREATE UNIQUE INDEX IF NOT EXISTS uq_customers_email_lower
    ON customers ((LOWER(email)))
    WHERE email IS NOT NULL;

-- Triggers
CREATE TRIGGER normalize_customer_email_before_write
    BEFORE INSERT OR UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION normalize_email();

CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Indexes for search/filter
CREATE INDEX IF NOT EXISTS idx_customers_full_name ON customers USING GIN (to_tsvector('simple', full_name));
CREATE INDEX IF NOT EXISTS idx_customers_is_active ON customers(is_active);
CREATE INDEX IF NOT EXISTS idx_customers_created_at ON customers(created_at);

-- RLS
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Basic policies (adjust per your org roles/security needs)
CREATE POLICY "Customers readable by authenticated" ON customers
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Customers insert by authenticated" ON customers
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Customers update by authenticated" ON customers
    FOR UPDATE TO authenticated USING (true);

-- Allow deletes by authenticated users (matches app behavior)
DROP POLICY IF EXISTS "Customers delete by authenticated" ON customers;
CREATE POLICY "Customers delete by authenticated" ON customers
    FOR DELETE TO authenticated USING (true);

-- ============================================
-- CUSTOMER ADDRESSES
-- ============================================

CREATE TABLE customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    title TEXT NOT NULL, -- 'Home', 'Office', 'Other'
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city TEXT NOT NULL,
    state TEXT,
    postal_code TEXT,
    country TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_customer_addresses_customer_id ON customer_addresses(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_is_default ON customer_addresses(is_default);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_is_active ON customer_addresses(is_active);

-- Triggers
CREATE TRIGGER update_customer_addresses_updated_at
    BEFORE UPDATE ON customer_addresses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS
ALTER TABLE customer_addresses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Customer addresses readable by authenticated" ON customer_addresses
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Customer addresses insert by authenticated" ON customer_addresses
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Customer addresses update by authenticated" ON customer_addresses
    FOR UPDATE TO authenticated USING (true);

-- Allow deletes by authenticated users for addresses as well
DROP POLICY IF EXISTS "Customer addresses delete by authenticated" ON customer_addresses;
CREATE POLICY "Customer addresses delete by authenticated" ON customer_addresses
    FOR DELETE TO authenticated USING (true);

-- ============================================
-- OPTIONAL: helper function to check email availability
-- ============================================

CREATE OR REPLACE FUNCTION is_customer_email_available(p_email TEXT)
RETURNS BOOLEAN
SET search_path = ''
AS $$
DECLARE
    v_exists BOOLEAN;
BEGIN
    IF p_email IS NULL THEN
        RETURN TRUE; -- treat null as available
    END IF;
    SELECT EXISTS (
        SELECT 1 FROM public.customers c WHERE c.email IS NOT NULL AND LOWER(c.email) = LOWER(p_email)
    ) INTO v_exists;
    RETURN NOT v_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION is_customer_email_available IS 'Returns true if no customer exists with the given email (case-insensitive).';



