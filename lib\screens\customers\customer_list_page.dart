import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/customer_service.dart';
import '../../models/customer_model.dart';
import 'customer_detail_page.dart';
import 'customer_registration_page.dart';

class CustomerListPage extends StatefulWidget {
  const CustomerListPage({super.key});

  @override
  State<CustomerListPage> createState() => _CustomerListPageState();
}

class _CustomerListPageState extends State<CustomerListPage> {
  final CustomerService _service = CustomerService(supabase: Supabase.instance.client);
  
  String _search = '';
  bool _activeOnly = true;
  bool _inactiveOnly = false;
  bool _sortAlphabetically = false;
  // Legacy future removed; list is now fully driven by paginated state
  int _totalCount = 0;
  final int _pageSize = 24;
  bool _isLoadingMore = false;
  List<Customer> _customers = [];

  @override
  void initState() {
    super.initState();
    // initial load with pagination
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshPaged();
    });
  }

  void _reload() {
    _refreshPaged();
  }

  Future<void> _refreshPaged() async {
    _customers = [];
    await _loadMore(reset: true);
  }

  Future<void> _loadMore({bool reset = false}) async {
    if (_isLoadingMore) return;
    setState(() { _isLoadingMore = true; });
    final offset = reset ? 0 : _customers.length;
    final isActive = _inactiveOnly ? false : (_activeOnly ? true : null);
    final result = await _service.getCustomersPaged(
      searchTerm: _search.isEmpty ? null : _search,
      isActive: isActive,
      offset: offset,
      limit: _pageSize,
      sortAlphabetically: _sortAlphabetically,
    );
    setState(() {
      _totalCount = result.totalCount;
      if (reset) {
        _customers = result.customers;
      } else {
        _customers.addAll(result.customers);
      }
      _isLoadingMore = false;
    });
  }

  void _showAddCustomerDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CustomerRegistrationPage(),
      ),
    ).then((_) => _reload());
  }

  Future<void> _toggleCustomerActive(Customer customer) async {
    final action = customer.isActive ? 'deactivate' : 'activate';
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${action[0].toUpperCase()}${action.substring(1)} Customer'),
        content: Text('Are you sure you want to $action ${customer.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: customer.isActive ? Colors.orange : Colors.green
            ),
            child: Text(action[0].toUpperCase() + action.substring(1)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _service.updateCustomer(customer.id!, isActive: !customer.isActive);
        _reload();
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('${customer.fullName} has been ${action}d')),
          );
        }
      } catch (e) {
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('Error ${action}ing customer: $e')),
          );
        }
      }
    }
  }

  Future<void> _showDeleteConfirmation(Customer customer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete ${customer.fullName}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _service.deleteCustomer(customer.id!);
        _reload();
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('${customer.fullName} has been deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('Error deleting customer: $e')),
          );
        }
      }
    }
  }

  void _showCustomerActions(Customer customer) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              customer.fullName,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('View Details'),
              onTap: () async {
                Navigator.pop(context);
                await Navigator.push(
                  context, 
                  MaterialPageRoute(
                    builder: (_) => CustomerDetailPage(customerId: customer.id!)
                  )
                );
                _reload();
              },
            ),
            ListTile(
              leading: Icon(customer.isActive ? Icons.pause_circle : Icons.play_circle),
              title: Text(customer.isActive ? 'Deactivate' : 'Activate'),
              onTap: () {
                Navigator.pop(context);
                _toggleCustomerActive(customer);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(customer);
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Management'),
        actions: [
          IconButton(onPressed: _reload, icon: const Icon(Icons.refresh)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCustomerDialog,
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // Search bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: TextField(
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      prefixIcon: Icon(Icons.search), 
                      hintText: 'Search customers',
                      border: InputBorder.none,
                    ),
                    onChanged: (v) { 
                      setState(() { _search = v; });
                      _reload(); 
                    },
                  ),
                ),
                const SizedBox(height: 8),
                // Filters and alphabet in a styled container
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          ChoiceChip(
                            label: const Text('Active'),
                            selected: _activeOnly && !_inactiveOnly,
                            onSelected: (v) {
                              setState(() {
                                _activeOnly = v;
                                if (v) _inactiveOnly = false;
                              });
                              _reload();
                            },
                          ),
                          ChoiceChip(
                            label: const Text('Inactive'),
                            selected: _inactiveOnly,
                            onSelected: (v) {
                              setState(() {
                                _inactiveOnly = v;
                                if (v) _activeOnly = false;
                              });
                              _reload();
                            },
                          ),
                          ChoiceChip(
                            label: const Text('Sort A–Z'),
                            selected: _sortAlphabetically,
                            onSelected: (v) {
                              setState(() { _sortAlphabetically = v; });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _customers.isEmpty && _isLoadingMore
                ? const Center(child: CircularProgressIndicator())
                : Builder(builder: (context) {
                var list = _customers;
                if (list.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'No customers found',
                          style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add your first customer to get started',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _showAddCustomerDialog,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Customer'),
                        ),
                      ],
                    ),
                  );
                }
                
                return NotificationListener<ScrollNotification>(
                  onNotification: (n) {
                    if (n.metrics.pixels >= n.metrics.maxScrollExtent - 300) {
                      if (_customers.length < _totalCount && !_isLoadingMore) {
                        _loadMore();
                      }
                    }
                    return false;
                  },
                  child: ListView.separated(
                  itemCount: list.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 8),
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                  itemBuilder: (context, i) {
                    final customer = list[i];
                    final subtitle = [
                      customer.email, 
                      customer.phone,
                      customer.country,
                    ].where((e) => (e ?? '').isNotEmpty).join(' • ');
                    
                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.03),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: customer.isActive ? Colors.blue : Colors.grey,
                          child: Text(
                            customer.fullName.isNotEmpty ? customer.fullName[0].toUpperCase() : '?',
                            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ),
                        title: Text(
                          customer.fullName,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: customer.isActive ? Colors.black87 : Colors.grey,
                          ),
                        ),
                        subtitle: subtitle.isNotEmpty ? Text(subtitle) : null,
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (customer.isActive) 
                              const Icon(Icons.check_circle, color: Colors.green, size: 20)
                            else 
                              const Icon(Icons.pause_circle, color: Colors.grey, size: 20),
                            const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.more_vert),
                              onPressed: () => _showCustomerActions(customer),
                            ),
                          ],
                        ),
                        onTap: () async {
                          await Navigator.push(
                            context, 
                            MaterialPageRoute(
                              builder: (_) => CustomerDetailPage(customerId: customer.id!)
                            )
                          );
                          _reload();
                        },
                      ),
                    );
                  },
                ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Total: $_totalCount'),
                if (_customers.length < _totalCount)
                  ElevatedButton.icon(
                    onPressed: _isLoadingMore ? null : () => _loadMore(),
                    icon: const Icon(Icons.expand_more),
                    label: Text(_isLoadingMore ? 'Loading…' : 'Load more'),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
