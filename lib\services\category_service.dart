import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CategoryService {
  final SupabaseClient _supabase;

  CategoryService({SupabaseClient? supabase}) 
      : _supabase = supabase ?? Supabase.instance.client;

  /// Get all unique categories from the garments table
  Future<List<String>> getAllCategories() async {
    try {
      final response = await _supabase
          .from('garments')
          .select('category')
          .order('category');

      final categories = (response as List<dynamic>)
          .map((item) => item['category'] as String)
          .toSet() // Remove duplicates
          .toList();

      return categories;
    } catch (e) {
      debugPrint('Error fetching categories: $e');
      return ['clothing', 'household', 'special']; // Fallback to default categories
    }
  }

  /// Get category statistics (count of garments per category)
  Future<Map<String, int>> getCategoryStats() async {
    try {
      final response = await _supabase
          .from('garments')
          .select('category')
          .eq('is_active', true);

      final Map<String, int> stats = {};
      for (final item in response as List<dynamic>) {
        final category = item['category'] as String;
        stats[category] = (stats[category] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      debugPrint('Error fetching category stats: $e');
      return {};
    }
  }

  /// Update category for all garments (rename category)
  Future<bool> renameCategory(String oldCategory, String newCategory) async {
    try {
      await _supabase
          .from('garments')
          .update({'category': newCategory})
          .eq('category', oldCategory);
      
      return true;
    } catch (e) {
      debugPrint('Error renaming category: $e');
      return false;
    }
  }

  /// Check if category is in use by any garments
  Future<bool> isCategoryInUse(String category) async {
    try {
      final response = await _supabase
          .from('garments')
          .select('id')
          .eq('category', category)
          .limit(1);

      return (response as List).isNotEmpty;
    } catch (e) {
      debugPrint('Error checking category usage: $e');
      return false;
    }
  }

  /// Delete category (move all garments to 'other' category)
  Future<bool> deleteCategory(String category) async {
    try {
      // Move all garments from this category to 'other'
      await _supabase
          .from('garments')
          .update({'category': 'other'})
          .eq('category', category);
      
      return true;
    } catch (e) {
      debugPrint('Error deleting category: $e');
      return false;
    }
  }

  /// Get built-in categories that cannot be modified
  List<String> getBuiltInCategories() {
    return ['clothing', 'household', 'special'];
  }

  /// Check if a category is built-in
  bool isBuiltInCategory(String category) {
    return getBuiltInCategories().contains(category);
  }

  /// Get display name for category
  String getCategoryDisplayName(String category) {
    switch (category) {
      case 'clothing':
        return 'Clothing';
      case 'household':
        return 'Household Items';
      case 'special':
        return 'Special Care';
      case 'other':
        return 'Other';
      default:
        // Convert snake_case to Title Case
        return category
            .split('_')
            .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
            .join(' ');
    }
  }

  /// Validate category name
  String? validateCategoryName(String name, {String? excludeCategory}) {
    if (name.trim().isEmpty) {
      return 'Category name cannot be empty';
    }
    
    final categoryKey = name.trim().toLowerCase().replaceAll(' ', '_');
    
    if (categoryKey == excludeCategory) {
      return null; // Same as current, no validation error
    }
    
    if (categoryKey.length < 2) {
      return 'Category name must be at least 2 characters';
    }
    
    if (categoryKey.length > 50) {
      return 'Category name must be less than 50 characters';
    }
    
    // Check for invalid characters
    if (!RegExp(r'^[a-z0-9_]+$').hasMatch(categoryKey)) {
      return 'Category name can only contain letters, numbers, and underscores';
    }
    
    return null;
  }

  /// Convert display name to category key
  String displayNameToKey(String displayName) {
    return displayName.trim().toLowerCase().replaceAll(' ', '_');
  }
}
