import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/staff_service.dart';
import '../../models/staff_profile_model.dart';

class StaffDetailPage extends StatefulWidget {
  final String staffId;
  const StaffDetailPage({super.key, required this.staffId});

  @override
  State<StaffDetailPage> createState() => _StaffDetailPageState();
}

class _StaffDetailPageState extends State<StaffDetailPage> {
  final StaffService _service = StaffService(supabase: Supabase.instance.client);
  StaffProfile? _staff;
  bool _loading = true;
  bool _saving = false;

  final _nameCtrl = TextEditingController();
  final _emailCtrl = TextEditingController();
  final _phoneCtrl = TextEditingController();
  final _passwordCtrl = TextEditingController();
  bool _emailEnabled = true;
  bool _active = true;
  bool _obscurePassword = true;

  Future<void> _load() async {
    setState(() { _loading = true; });
    final s = await _service.getStaff(widget.staffId);
    if (!mounted) return;
    setState(() {
      _staff = s;
      _loading = false;
      if (s != null) {
        _nameCtrl.text = s.fullName;
        _emailCtrl.text = s.email ?? '';
        _phoneCtrl.text = s.phone ?? '';
        _passwordCtrl.text = s.password ?? '';
        _emailEnabled = s.emailEnabled;
        _active = s.isActive;
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _load();
  }

  @override
  void dispose() {
    _nameCtrl.dispose();
    _emailCtrl.dispose();
    _phoneCtrl.dispose();
    _passwordCtrl.dispose();
    super.dispose();
  }

  Future<void> _saveActive(bool value) async {
    setState(() { _saving = true; });
    await _service.setActive(widget.staffId, value);
    await _load();
    setState(() { _saving = false; });
  }

  Future<void> _saveProfile() async {
    setState(() { _saving = true; });
    await _service.updateStaffProfile(
      id: widget.staffId,
      fullName: _nameCtrl.text.trim(),
      email: _emailCtrl.text.trim().isEmpty ? null : _emailCtrl.text.trim(),
      phone: _phoneCtrl.text.trim().isEmpty ? null : _phoneCtrl.text.trim(),
      password: _passwordCtrl.text.trim().isEmpty ? null : _passwordCtrl.text.trim(),
      emailEnabled: _emailEnabled,
    );
    await _load();
    setState(() { _saving = false; });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Staff Detail',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          actions: [IconButton(onPressed: _load, icon: const Icon(Icons.refresh))],
          bottom: const TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: TextStyle(fontWeight: FontWeight.w600),
            indicatorColor: Colors.white,
            tabs: [
              Tab(icon: Icon(Icons.person), text: 'Details'),
              Tab(icon: Icon(Icons.timeline), text: 'Activity'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            _buildDetailBody(),
            _buildActivityTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailBody() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_staff == null) {
      return const Center(child: Text('Staff not found'));
    }
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                child: Text(
                  _staff!.fullName.isNotEmpty ? _staff!.fullName[0].toUpperCase() : '?',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _staff!.fullName,
                  style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
              ),
              Switch.adaptive(
                value: _active,
                onChanged: _saving ? null : (v) => _saveActive(v),
              ),
              const SizedBox(width: 8),
              Text(_active ? 'Active' : 'Inactive'),
            ],
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.badge, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Staff ID: ${_staff!.staffId}',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(Icons.email, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Email: ${_staff!.emailEnabled ? "Enabled" : "Disabled"}',
                        style: TextStyle(
                          fontSize: 16,
                          color: _staff!.emailEnabled ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _nameCtrl,
            decoration: const InputDecoration(
              labelText: 'Full name',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 12),
          SwitchListTile(
            title: const Text('Email Enabled'),
            subtitle: const Text('Allow this staff member to receive emails'),
            value: _emailEnabled,
            onChanged: _saving ? null : (v) => setState(() {
              _emailEnabled = v;
            }),
          ),
          if (_emailEnabled) ...[
            const SizedBox(height: 12),
            TextField(
              controller: _emailCtrl,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
          ],
          TextField(
            controller: _phoneCtrl,
            decoration: const InputDecoration(
              labelText: 'Phone',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _passwordCtrl,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Password',
              border: const OutlineInputBorder(),
              helperText: 'Leave empty to keep current password',
              suffixIcon: IconButton(
                icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() {
                  _obscurePassword = !_obscurePassword;
                }),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _saving ? null : _saveProfile,
              child: _saving
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
                        SizedBox(width: 8),
                        Text('Saving...'),
                      ],
                    )
                  : const Text('Save Changes'),
            ),
          ),
          if (_saving) ...[
            const SizedBox(height: 12),
            const LinearProgressIndicator(minHeight: 2),
          ],
        ],
      ),
    );
  }

  Widget _buildActivityTab() {
    // Placeholder activity timeline for the staff member
    return ListView(
      padding: const EdgeInsets.all(16),
      children: const [
        SizedBox(height: 12),
        Center(child: Icon(Icons.timeline, size: 64, color: Colors.grey)),
        SizedBox(height: 12),
        Center(child: Text('Activity feed not implemented yet')),
      ],
    );
  }
}
