import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/store_model.dart';

class StoreService {
  final SupabaseClient _supabase;

  StoreService({SupabaseClient? supabase}) 
      : _supabase = supabase ?? Supabase.instance.client;

  // Get all stores (alias for getStores)
  Future<List<Store>> getAllStores() async {
    return getStores();
  }

  // Get all stores
  Future<List<Store>> getStores() async {
    try {
      final response = await _supabase
          .from('stores')
          .select()
          .order('created_at', ascending: false);
      
      return (response as List<dynamic>)
          .map((storeMap) => Store.fromMap(storeMap as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting stores: $e');
      return [];
    }
  }

  // Add a new store
  Future<bool> addStore(Map<String, dynamic> storeData) async {
    try {
      await _supabase.from('stores').insert(storeData);
      return true;
    } catch (e) {
      debugPrint('Error adding store: $e');
      return false;
    }
  }

  // Update an existing store
  Future<bool> updateStore(String storeId, Map<String, dynamic> storeData) async {
    try {
      await _supabase
          .from('stores')
          .update(storeData)
          .eq('id', storeId);
      return true;
    } catch (e) {
      debugPrint('Error updating store: $e');
      return false;
    }
  }

  // Delete a store
  Future<bool> deleteStore(String storeId) async {
    try {
      await _supabase
          .from('stores')
          .delete()
          .eq('id', storeId);
      return true;
    } catch (e) {
      debugPrint('Error deleting store: $e');
      return false;
    }
  }

  // Get a store by ID
  Future<Store?> getStoreById(String storeId) async {
    try {
      final response = await _supabase
          .from('stores')
          .select()
          .eq('id', storeId)
          .single();
      
      return Store.fromMap(response);
    } catch (e) {
      debugPrint('Error getting store by ID: $e');
      return null;
    }
  }

  // Toggle store active status
  Future<bool> toggleStoreStatus(String storeId) async {
    try {
      final store = await getStoreById(storeId);
      if (store != null) {
        await _supabase
            .from('stores')
            .update({'is_active': !store.isActive})
            .eq('id', storeId);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error toggling store status: $e');
      return false;
    }
  }

  // Get active stores only
  Future<List<Store>> getActiveStores() async {
    try {
      final response = await _supabase
          .from('stores')
          .select()
          .eq('is_active', true)
          .order('created_at', ascending: false);
      
      return (response as List<dynamic>)
          .map((storeMap) => Store.fromMap(storeMap as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting active stores: $e');
      return [];
    }
  }

  // Search stores by term
  Future<List<Store>> searchStores(String searchTerm) async {
    try {
      final response = await _supabase.rpc('search_stores', params: {
        'search_term': searchTerm,
      });
      
      return (response as List<dynamic>)
          .map((storeMap) => Store.fromMap(storeMap as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error searching stores: $e');
      return [];
    }
  }

  // Get stores by country
  Future<List<Store>> getStoresByCountry(String country) async {
    try {
      final response = await _supabase.rpc('get_stores_by_country', params: {
        'country_name': country,
      });
      
      return (response as List<dynamic>)
          .map((storeMap) => Store.fromMap(storeMap as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error getting stores by country: $e');
      return [];
    }
  }
}
