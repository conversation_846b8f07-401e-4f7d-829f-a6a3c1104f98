import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/service_model.dart';

class ServiceService {
  final SupabaseClient _supabase;

  ServiceService({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  Future<List<Service>> getAll({bool activeOnly = false}) async {
    try {
      final base = _supabase.from('services').select();
      final List<Map<String, dynamic>> response = activeOnly
          ? await base.eq('is_active', true).order('name')
          : await base.order('name');
      return response.map((s) => Service.fromJson(s)).toList();
    } catch (e) {
      debugPrint('Error fetching services: $e');
      return [];
    }
  }

  Future<List<Service>> search(String term) async {
    try {
      final List<Map<String, dynamic>> response = await _supabase
          .from('services')
          .select()
          .ilike('name', '%$term%')
          .order('name');
      return response.map((s) => Service.fromJson(s)).toList();
    } catch (e) {
      debugPrint('Error searching services: $e');
      return [];
    }
  }

  Future<Service?> getById(String id) async {
    try {
      final Map<String, dynamic> response = await _supabase
          .from('services')
          .select()
          .eq('id', id)
          .single();
      return Service.fromJson(response);
    } catch (_) {
      return null;
    }
  }

  Future<Service> create(Service data) async {
    try {
      final Map<String, dynamic> response = await _supabase
          .from('services')
          .insert(data.toJson()
            ..remove('id')
            ..remove('created_at')
            ..remove('updated_at'))
          .select()
          .single();
      return Service.fromJson(response);
    } catch (e) {
      throw 'Error creating service: $e';
    }
  }

  Future<Service> createRaw({
    required String name,
    String? description,
    double basePrice = 0,
    double? pricePerKg,
    double? pricePerItem,
    required String pricingType,
    int estimatedHours = 24,
    bool isActive = true,
    String? icon,
  }) async {
    try {
      final Map<String, dynamic> response = await _supabase
          .from('services')
          .insert({
            'name': name,
            'description': description,
            'base_price': basePrice,
            'price_per_kg': pricePerKg,
            'price_per_item': pricePerItem,
            'pricing_type': pricingType,
            'estimated_hours': estimatedHours,
            'is_active': isActive,
            'icon': icon,
          })
          .select()
          .single();
      return Service.fromJson(response);
    } catch (e) {
      throw 'Error creating service: $e';
    }
  }

  Future<Service> update(String id, Map<String, dynamic> updates) async {
    try {
      await _supabase
          .from('services')
          .update(updates)
          .eq('id', id);
      final updated = await getById(id);
      if (updated == null) {
        throw 'Update applied but service not readable (check RLS policies)';
      }
      return updated;
    } catch (e) {
      throw 'Error updating service: $e';
    }
  }

  Future<void> delete(String id) async {
    try {
      await _supabase.from('services').delete().eq('id', id);
    } catch (e) {
      throw 'Error deleting service: $e';
    }
  }

  Future<void> toggleActive(String id, bool isActive) async {
    try {
      await _supabase.from('services').update({'is_active': isActive}).eq('id', id);
    } catch (e) {
      throw 'Error toggling service: $e';
    }
  }
}
