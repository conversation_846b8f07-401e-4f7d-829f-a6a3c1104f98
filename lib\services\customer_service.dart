import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/customer_model.dart';

class CustomerService {
  final SupabaseClient _supabase;

  CustomerService({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  Future<bool> isEmailAvailable(String email) async {
    try {
      if (email.trim().isEmpty) return true; // treat empty as available
      final result = await _supabase.rpc('is_customer_email_available', params: {
        'p_email': email,
      });
      return (result as bool?) ?? true;
    } catch (e) {
      debugPrint('Error checking email availability: $e');
      // If RPC unavailable, fallback to select
      try {
        final rows = await _supabase
            .from('customers')
            .select('id')
            .filter('email', 'ilike', email)
            .limit(1);
        return (rows as List).isEmpty;
      } catch (_) {
        return true; // do not block on error
      }
    }
  }

  Future<Customer> createCustomer(Customer customer, {List<CustomerAddress>? addresses}) async {
    try {
      final insertData = customer.toJson();
      insertData.remove('id');
      final inserted = await _supabase
          .from('customers')
          .insert(insertData)
          .select()
          .single();

      final created = Customer.fromJson(inserted);

      if (addresses != null && addresses.isNotEmpty) {
        final toInsert = addresses
            .map((a) => a.copyWith(customerId: created.id).toJson())
            .toList();
        for (final row in toInsert) {
          row.remove('id');
        }
        await _supabase.from('customer_addresses').insert(toInsert);
      }

      return created;
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  Future<List<Customer>> getCustomers({String? searchTerm, bool? onlyActive}) async {
    try {
      var request = _supabase.from('customers').select();
      if (onlyActive == true) {
        request = request.filter('is_active', 'eq', true);
      }
      final rows = await request.order('created_at', ascending: false);
      var list = (rows as List).map((e) => Customer.fromJson(e)).toList();
      if (searchTerm != null && searchTerm.trim().isNotEmpty) {
        final q = searchTerm.toLowerCase();
        list = list.where((c) =>
          c.fullName.toLowerCase().contains(q) ||
          (c.email ?? '').toLowerCase().contains(q) ||
          (c.phone ?? '').toLowerCase().contains(q)
        ).toList();
      }
      return list;
    } catch (e) {
      debugPrint('Error fetching customers: $e');
      return [];
    }
  }

  Future<({List<Customer> customers, int totalCount})> getCustomersPaged({
    String? searchTerm,
    bool? isActive, // null = both
    required int offset,
    required int limit,
    bool sortAlphabetically = false,
  }) async {
    try {
      // Build filters on select() so we can use eq/or helpers
      final table = _supabase.from('customers');
      var countQuery = table.select('id');
      var dataQuery = table.select();

      if (isActive != null) {
        countQuery = countQuery.eq('is_active', isActive);
        dataQuery = dataQuery.eq('is_active', isActive);
      }
      if (searchTerm != null && searchTerm.trim().isNotEmpty) {
        final q = searchTerm.trim();
        countQuery = countQuery.or('full_name.ilike.%$q%,email.ilike.%$q%,phone.ilike.%$q%');
        dataQuery = dataQuery.or('full_name.ilike.%$q%,email.ilike.%$q%,phone.ilike.%$q%');
      }

      // Total count (simple approach: fetch ids and count)
      final allIds = await countQuery;
      final total = (allIds as List).length;

      // Paged data
      final rows = await dataQuery
          .order(sortAlphabetically ? 'full_name' : 'created_at', ascending: sortAlphabetically)
          .range(offset, offset + limit - 1);

      final customers = (rows as List).map((e) => Customer.fromJson(e)).toList();
      return (customers: customers, totalCount: total);
    } catch (e) {
      debugPrint('Error fetching paged customers: $e');
      return (customers: <Customer>[], totalCount: 0);
    }
  }

  Future<List<CustomerAddress>> getCustomerAddresses(String customerId) async {
    try {
      final rows = await _supabase
          .from('customer_addresses')
          .select()
          .filter('customer_id', 'eq', customerId)
          .filter('is_active', 'eq', true)
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);
      return (rows as List).map((e) => CustomerAddress.fromJson(e)).toList();
    } catch (e) {
      throw Exception('Failed to fetch addresses: $e');
    }
  }

  Future<void> setDefaultAddress(String customerId, String addressId) async {
    await _supabase
        .from('customer_addresses')
        .update({'is_default': false})
        .filter('customer_id', 'eq', customerId);
    await _supabase
        .from('customer_addresses')
        .update({'is_default': true})
        .filter('id', 'eq', addressId);
  }

  Future<Customer?> getCustomer(String id) async {
    try {
      final row = await _supabase
          .from('customers')
          .select()
          .eq('id', id)
          .single();
      return Customer.fromJson(row);
    } catch (e) {
      debugPrint('Error fetching customer: $e');
      return null;
    }
  }

  Future<Customer> updateCustomer(String id, {
    String? fullName,
    String? email,
    String? phone,
    String? country,
    String? notes,
    bool? isActive,
  }) async {
    try {
      final updates = <String, dynamic>{};
      if (fullName != null) updates['full_name'] = fullName;
      if (email != null) updates['email'] = email;
      if (phone != null) updates['phone'] = phone;
      if (country != null) updates['country'] = country;
      if (notes != null) updates['notes'] = notes;
      if (isActive != null) updates['is_active'] = isActive;
      
      if (updates.isNotEmpty) {
        updates['updated_at'] = DateTime.now().toIso8601String();
        final updated = await _supabase
            .from('customers')
            .update(updates)
            .eq('id', id)
            .select()
            .single();
        return Customer.fromJson(updated);
      }
      
      // If no updates, return current customer
      final customer = await getCustomer(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }
      return customer;
    } catch (e) {
      throw Exception('Failed to update customer: $e');
    }
  }

  Future<void> deleteCustomer(String id) async {
    try {
      await _supabase.from('customers').delete().eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete customer: $e');
    }
  }

  Future<CustomerAddress> createAddress(CustomerAddress address) async {
    try {
      final insertData = address.toJson();
      insertData.remove('id');
      final inserted = await _supabase
          .from('customer_addresses')
          .insert(insertData)
          .select()
          .single();
      return CustomerAddress.fromJson(inserted);
    } catch (e) {
      throw Exception('Failed to create address: $e');
    }
  }

  Future<CustomerAddress> updateAddress(String id, CustomerAddress address) async {
    try {
      final updateData = address.toJson();
      updateData.remove('id');
      updateData['updated_at'] = DateTime.now().toIso8601String();
      final updated = await _supabase
          .from('customer_addresses')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();
      return CustomerAddress.fromJson(updated);
    } catch (e) {
      throw Exception('Failed to update address: $e');
    }
  }

  Future<void> deleteAddress(String id) async {
    try {
      await _supabase.from('customer_addresses').delete().eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete address: $e');
    }
  }
}



