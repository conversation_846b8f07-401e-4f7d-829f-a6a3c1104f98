# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment variables
.env
.env.local
.env.production
.env.staging

# Additional Flutter/Dart ignores
*.lock
!pubspec.lock
*.g.dart
*.freezed.dart
*.mocks.dart

# iOS specific
ios/Pods/
ios/Runner.xcworkspace/xcshareddata/
ios/Runner.xcworkspace/xcuserdata/

# Android specific
android/.gradle/
android/local.properties
android/key.properties

# Web specific
web/firebase-messaging-sw.js

# Test coverage
coverage/
*.lcov

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini