import '../models/faq_model.dart';

class FAQService {
  // In a real app, this would fetch from an API or database
  Future<List<FAQCategory>> getFAQCategories() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      FAQCategory(
        name: 'General',
        icon: '📱',
        faqs: [
          FAQ(
            question: 'What is Laundry Pro?',
            answer: 'Laundry Pro is a comprehensive laundry business management application designed to help laundry business owners manage orders, customers, payments, and reports efficiently.',
            category: 'General',
            isPinned: true,
          ),
          FAQ(
            question: 'Do I need internet connection to use the app?',
            answer: 'Yes. Laundry Pro requires an active internet connection. Features such as orders, payments, reports, and syncing operate online. Offline mode is currently not supported.',
            category: 'General',
          ),
          FAQ(
            question: 'Is my data secure?',
            answer: 'Yes, we take data security seriously. All your business data is stored securely on your device and, if you choose to use cloud backup, it\'s encrypted before being stored in the cloud.',
            category: 'General',
          ),
        ],
      ),
      FAQCategory(
        name: 'Orders & Services',
        icon: '🧺',
        faqs: [
          FAQ(
            question: 'How do I create a new order?',
            answer: 'To create a new order, navigate to the Orders section and tap on "Place New Order". Then select a customer (or create a new one), add garments and services, set the pickup date, and save the order.',
            category: 'Orders & Services',
            isPinned: true,
          ),
          FAQ(
            question: 'Can I edit an order after it\'s created?',
            answer: 'Yes, you can edit orders that haven\'t been marked as completed. Simply open the order details and tap on the edit button to make changes.',
            category: 'Orders & Services',
          ),
          FAQ(
            question: 'How do I add custom services?',
            answer: 'Go to the Services section, tap on "Add New Service", fill in the service details including name, price, and description, then save it. Your custom service will now be available when creating orders.',
            category: 'Orders & Services',
          ),
        ],
      ),
      FAQCategory(
        name: 'Payments & Invoices',
        icon: '💰',
        faqs: [
          FAQ(
            question: 'How do I record a payment?',
            answer: 'To record a payment, go to the Payments section, select "Receive Payment", choose the order(s) being paid for, enter the amount received, select payment method, and save the transaction.',
            category: 'Payments & Invoices',
            isPinned: true,
          ),
          FAQ(
            question: 'Can I generate an invoice for my customers?',
            answer: 'Yes, you can generate professional invoices for any order. Open the order details and tap on "Generate Invoice". You can then preview, share, or print the invoice.',
            category: 'Payments & Invoices',
          ),
          FAQ(
            question: 'What payment methods are supported?',
            answer: 'Laundry Pro supports recording various payment methods including cash, credit/debit cards, mobile money, bank transfers, and checks. You can also add custom payment methods in the settings.',
            category: 'Payments & Invoices',
          ),
        ],
      ),
      FAQCategory(
        name: 'Customers',
        icon: '👥',
        faqs: [
          FAQ(
            question: 'How do I add a new customer?',
            answer: 'To add a new customer, go to the Customers section and tap on "Add New Customer". Fill in the customer details such as name, phone number, and address, then save.',
            category: 'Customers',
          ),
          FAQ(
            question: 'Can I import my existing customer list?',
            answer: 'Yes, you can import customers from a CSV file. Go to Settings > Data Management > Import Data and follow the instructions to import your customer list.',
            category: 'Customers',
          ),
          FAQ(
            question: 'How do I view a customer\'s order history?',
            answer: 'Open the customer details page by selecting a customer from the Customers list. Their complete order history will be displayed, showing all past and current orders.',
            category: 'Customers',
          ),
        ],
      ),
      FAQCategory(
        name: 'Reports & Analytics',
        icon: '📊',
        faqs: [
          FAQ(
            question: 'What types of reports are available?',
            answer: 'Laundry Pro offers various reports including sales reports, order reports, customer reports, payment reports, and staff performance reports. These help you gain insights into your business performance.',
            category: 'Reports & Analytics',
          ),
          FAQ(
            question: 'Can I export reports?',
            answer: 'Yes, all reports can be exported as PDF or CSV files. Open the report you want to export and tap on the export icon in the top right corner.',
            category: 'Reports & Analytics',
          ),
          FAQ(
            question: 'How often are analytics updated?',
            answer: 'Analytics are updated in real-time as you add new orders, record payments, or make other changes in the app.',
            category: 'Reports & Analytics',
          ),
        ],
      ),
      FAQCategory(
        name: 'Account & Settings',
        icon: '⚙️',
        faqs: [
          FAQ(
            question: 'How do I change the app theme?',
            answer: 'Go to Settings > Appearance > Theme and select your preferred theme (Light, Dark, or System Default).',
            category: 'Account & Settings',
          ),
          FAQ(
            question: 'Can I use the app on multiple devices?',
            answer: 'Yes, with a premium subscription, you can sync your data across multiple devices. Set up the app on each device using the same account credentials.',
            category: 'Account & Settings',
          ),
          FAQ(
            question: 'How do I backup my data?',
            answer: 'Go to Settings > Data Management > Backup Data. You can choose to backup to your device storage or to the cloud (requires premium subscription).',
            category: 'Account & Settings',
          ),
        ],
      ),
    ];
  }

  Future<List<FAQ>> getPinnedFAQs() async {
    final categories = await getFAQCategories();
    final allFAQs = categories.expand((category) => category.faqs).toList();
    return allFAQs.where((faq) => faq.isPinned).toList();
  }

  Future<List<FAQ>> searchFAQs(String query) async {
    if (query.isEmpty) return [];
    
    final categories = await getFAQCategories();
    final allFAQs = categories.expand((category) => category.faqs).toList();
    
    return allFAQs.where((faq) => 
      faq.question.toLowerCase().contains(query.toLowerCase()) || 
      faq.answer.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}
