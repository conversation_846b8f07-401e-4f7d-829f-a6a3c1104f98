import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/staff_service.dart';
import '../../services/currency_service.dart' as currency_service;
import '../../services/auth_service.dart';

class StaffReportPage extends StatefulWidget {
  const StaffReportPage({super.key});

  @override
  State<StaffReportPage> createState() => _StaffReportPageState();
}

class _StaffReportPageState extends State<StaffReportPage> {
  final _supabase = Supabase.instance.client;
  final StaffService _staffService = StaffService();
  final currency_service.AppCurrencyService _currency = currency_service.AppCurrencyService();
  final AuthService _authService = AuthService(supabase: Supabase.instance.client);

  static const String _allKey = '__ALL__';

  bool _isLoading = true;
  String _currencySymbol = '';
  String _currentUserName = '';

  String _rangePreset = 'This Month';
  DateTime _start = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime _end = DateTime.now();

  Map<String, String> _staffIdToName = {}; // id -> name
  List<_StaffTotal> _staffTotals = [];
  double _grandTotal = 0.0;
  String? _compareA = _allKey;
  String? _compareB;

  // For selected staff breakdown (staff -> store totals)
  Map<String, double> _selectedStaffStoreTotals = {};
  // Cache for store names to avoid showing raw IDs
  final Map<String, String> _storeIdToName = {};

  @override
  void initState() {
    super.initState();
    _currentUserName = _authService.userDisplayName;
    _init();
  }

  Future<void> _init() async {
    setState(() => _isLoading = true);
    _currencySymbol = await _currency.getCurrencySymbol();
    await _loadData();
  }

  void _applyPreset(String preset) {
    final now = DateTime.now();
    if (preset == 'This Month') {
      _start = DateTime(now.year, now.month, 1);
      _end = now;
    } else if (preset == 'Last Month') {
      final last = DateTime(now.year, now.month - 1, 1);
      _start = last;
      _end = DateTime(last.year, last.month + 1, 0, 23, 59, 59, 999);
    } else if (preset == 'This Year') {
      _start = DateTime(now.year, 1, 1);
      _end = now;
    }
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // Load staff (all statuses)
      final staff = await _staffService.listStaff(status: 'all');
      String fallbackLabel(String id) => 'User ${id.substring(0, 6)}…';
      String displayNameFromList(String id) {
        try {
          final s = staff.firstWhere((e) => e.id == id);
          if (s.fullName.trim().isNotEmpty) return s.fullName;
          if ((s.email ?? '').trim().isNotEmpty) return s.email!;
        } catch (_) {}
        return fallbackLabel(id);
      }
      _staffIdToName = {
        _allKey: 'All Staff',
        for (final s in staff)
          s.id: (s.fullName.trim().isNotEmpty)
              ? s.fullName
              : ((s.email ?? '').trim().isNotEmpty ? s.email! : fallbackLabel(s.id)),
      };

      // payments grouped by created_by (staff) and store via orders join
      List<dynamic> payRows = [];
      try {
        var q = _supabase
            .from('payments')
            .select('amount, created_at, created_by, orders!inner(id, store_id)');
        q = q.gte('created_at', _start.toIso8601String());
        q = q.lte('created_at', _end.toIso8601String());
        payRows = await q;
      } catch (_) {
        // Fallback without join: fetch payments then map order->store
        var q = _supabase.from('payments').select('amount, created_at, created_by, order_id');
        q = q.gte('created_at', _start.toIso8601String());
        q = q.lte('created_at', _end.toIso8601String());
        final pays = await q as List<dynamic>;
        final orderIds = pays.map((e) => e['order_id'] as String).toSet().toList();
        final orders = orderIds.isEmpty
            ? <dynamic>[]
            : await _supabase.from('orders').select('id, store_id').inFilter('id', orderIds);
        final idToStore = {for (final o in orders) o['id']: o['store_id']};
        payRows = pays.map((p) => {
              'amount': p['amount'],
              'created_at': p['created_at'],
              'created_by': p['created_by'],
              'orders': {'id': p['order_id'], 'store_id': idToStore[p['order_id']]}
            }).toList();
      }

      final Map<String, double> byStaff = {};
      double grand = 0.0;
      for (final r in payRows) {
        final amount = (r['amount'] as num).toDouble();
        grand += amount;
        final staffId = r['created_by'] as String? ?? '';
        byStaff.update(staffId, (v) => v + amount, ifAbsent: () => amount);
      }

      // Ensure names for any staff ids not in list (e.g., inactive or non-staff users)
      final unknownIds = byStaff.keys.where((id) => !_staffIdToName.containsKey(id)).toList();
      if (unknownIds.isNotEmpty) {
        try {
          final rows = await _supabase
              .from('staff_profiles')
              .select('id, full_name, email')
              .inFilter('id', unknownIds);
          for (final row in (rows as List<dynamic>)) {
            final id = row['id'] as String;
            final full = (row['full_name'] as String?)?.trim() ?? '';
            final email = (row['email'] as String?)?.trim() ?? '';
            _staffIdToName[id] = full.isNotEmpty ? full : (email.isNotEmpty ? email : fallbackLabel(id));
          }
        } catch (_) {
          // ignore and fallback
        }
        for (final id in unknownIds) {
          _staffIdToName.putIfAbsent(id, () => displayNameFromList(id));
        }
      }

      final totals = byStaff.entries
          .map((e) => _StaffTotal(
                staffId: e.key,
                staffName: _staffIdToName[e.key] ?? displayNameFromList(e.key),
                total: e.value,
              ))
          .toList()
        ..sort((a, b) => b.total.compareTo(a.total));

      // default compareB
      _compareB ??= totals.isNotEmpty ? totals.first.staffId : _allKey;

      // If a specific staff selected in compareA or B, compute store breakdown for first non-all
      final focusId = _compareA != _allKey ? _compareA : (_compareB != _allKey ? _compareB : null);
      _selectedStaffStoreTotals = {};
      if (focusId != null && focusId != _allKey) {
        final Map<String, double> map = {};
        final Set<String> storeIds = {};
        for (final r in payRows.where((e) => e['created_by'] == focusId)) {
          final storeId = r['orders']?['store_id'] as String?;
          if (storeId == null) continue;
          final amount = (r['amount'] as num).toDouble();
          map.update(storeId, (v) => v + amount, ifAbsent: () => amount);
          storeIds.add(storeId);
        }
        _selectedStaffStoreTotals = map;

        // Resolve store names for display (fallback to short id)
        if (storeIds.isNotEmpty) {
          final unknown = storeIds.where((id) => !_storeIdToName.containsKey(id)).toList();
          if (unknown.isNotEmpty) {
            try {
              final rows = await _supabase
                  .from('stores')
                  .select('id, name')
                  .inFilter('id', unknown);
              for (final row in (rows as List<dynamic>)) {
                final id = row['id'] as String;
                final name = ((row['name'] as String?) ?? '').trim();
                _storeIdToName[id] = name.isNotEmpty ? name : 'Store ${id.substring(0, 6)}…';
              }
            } catch (_) {
              // ignore lookup errors
            }
          }
        }
      }

      if (!mounted) return;
      setState(() {
        _staffTotals = totals;
        _grandTotal = grand;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load staff report: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Build options map that includes any staff ids present in totals (even if not in staff list)
    final Map<String, String> optionsMap = {
      _allKey: 'All Staff',
      ..._staffIdToName,
      for (final t in _staffTotals) t.staffId: t.staffName,
    };
    final String valueA = optionsMap.containsKey(_compareA) ? (_compareA ?? _allKey) : _allKey;
    final String valueB = optionsMap.containsKey(_compareB) ? (_compareB ?? _allKey) : _allKey;
    return Scaffold(
      appBar: AppBar(
        title: Text('Staff Reports • $_currentUserName'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadData, icon: const Icon(Icons.refresh))],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _rangePreset,
                        decoration: const InputDecoration(labelText: 'Range', border: OutlineInputBorder()),
                        items: const [
                          DropdownMenuItem(value: 'This Month', child: Text('This Month')),
                          DropdownMenuItem(value: 'Last Month', child: Text('Last Month')),
                          DropdownMenuItem(value: 'This Year', child: Text('This Year')),
                          DropdownMenuItem(value: 'Custom', child: Text('Custom')),
                        ],
                        onChanged: (v) async {
                          if (v == null) return;
                          setState(() => _rangePreset = v);
                          if (v != 'Custom') {
                            _applyPreset(v);
                            await _loadData();
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (_rangePreset == 'Custom')
                      Expanded(
                        child: _DateRangePicker(
                          start: _start,
                          end: _end,
                          onChanged: (s, e) async {
                            setState(() {
                              _start = s;
                              _end = e;
                            });
                            await _loadData();
                          },
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: valueA,
                        decoration: const InputDecoration(labelText: 'Compare A', border: OutlineInputBorder()),
                        items: optionsMap.entries
                            .map((e) => DropdownMenuItem<String>(value: e.key, child: Text(e.value)))
                            .toList(),
                        onChanged: (v) async {
                          setState(() => _compareA = v);
                          await _loadData();
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: valueB,
                        decoration: const InputDecoration(labelText: 'Compare B', border: OutlineInputBorder()),
                        items: optionsMap.entries
                            .map((e) => DropdownMenuItem<String>(value: e.key, child: Text(e.value)))
                            .toList(),
                        onChanged: (v) async {
                          setState(() => _compareB = v);
                          await _loadData();
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    children: [
                      _buildTotalsCard(),
                      const SizedBox(height: 12),
                      _buildCompareCard(),
                      const SizedBox(height: 12),
                      if ((_compareA != null && _compareA != _allKey) || (_compareB != null && _compareB != _allKey))
                        _buildSelectedStaffStorePerf(),
                      const SizedBox(height: 12),
                      _buildStaffList(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsCard() {
    final formattedGrand = _currencySymbol.isEmpty
        ? _grandTotal.toStringAsFixed(2)
        : '$_currencySymbol${_grandTotal.toStringAsFixed(2)}';
    return Card(
      elevation: 2,
      child: ListTile(
        leading: const Icon(Icons.summarize, color: Colors.blue),
        title: const Text('Total Sales (Selected Range)', style: TextStyle(fontWeight: FontWeight.w700)),
        subtitle: Text('${_staffTotals.length} staff'),
        trailing: Text(formattedGrand, style: const TextStyle(fontWeight: FontWeight.w800)),
      ),
    );
  }

  _StaffTotal _resolve(String? id) {
    if (id == null) return _StaffTotal.empty();
    if (id == _allKey) return _StaffTotal(staffId: _allKey, staffName: 'All Staff', total: _grandTotal);
    return _staffTotals.firstWhere((e) => e.staffId == id, orElse: () => _StaffTotal(staffId: id, staffName: _staffIdToName[id] ?? '-', total: 0.0));
  }

  Widget _buildCompareCard() {
    final a = _resolve(_compareA);
    final b = _resolve(_compareB);
    final diff = b.total - a.total;
    final up = diff >= 0;
    final diffText = _currencySymbol.isEmpty ? diff.abs().toStringAsFixed(2) : '$_currencySymbol${diff.abs().toStringAsFixed(2)}';
    final percent = a.total == 0 ? (b.total > 0 ? 100.0 : 0.0) : (diff / a.total) * 100.0;
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Compare Staff', style: TextStyle(fontWeight: FontWeight.w800)),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(child: _miniTile(a)),
                const SizedBox(width: 8),
                Expanded(child: _miniTile(b)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (up ? Colors.green : Colors.redAccent).withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(up ? Icons.arrow_upward : Icons.arrow_downward, size: 14, color: up ? Colors.green : Colors.redAccent),
                      const SizedBox(width: 4),
                      Text('${up ? '+' : '-'}${percent.abs().toStringAsFixed(1)}%', style: TextStyle(color: up ? Colors.green : Colors.redAccent, fontWeight: FontWeight.w700)),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Text('${up ? '+' : '-'}$diffText', style: const TextStyle(fontWeight: FontWeight.w700)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _miniTile(_StaffTotal t) {
    final totalText = _currencySymbol.isEmpty ? t.total.toStringAsFixed(2) : '$_currencySymbol${t.total.toStringAsFixed(2)}';
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(t.staffName, maxLines: 1, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 6),
        Text(totalText, style: const TextStyle(fontWeight: FontWeight.w800, color: Colors.blue)),
      ]),
    );
  }

  Widget _buildSelectedStaffStorePerf() {
    final entries = _selectedStaffStoreTotals.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('Selected Staff — Sales by Store', style: TextStyle(fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          for (final e in entries)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(
                children: [
                  Expanded(child: Text(_storeIdToName[e.key] ?? 'Store ${e.key.substring(0, 6)}…')),
                  Text(_currencySymbol.isEmpty ? e.value.toStringAsFixed(2) : '$_currencySymbol${e.value.toStringAsFixed(2)}',
                      style: const TextStyle(fontWeight: FontWeight.w700)),
                ],
              ),
            ),
        ]),
      ),
    );
  }

  Widget _buildStaffList() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('Sales by Staff', style: TextStyle(fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          for (final t in _staffTotals)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(children: [
                Expanded(child: Text(t.staffName)),
                Text(_currencySymbol.isEmpty ? t.total.toStringAsFixed(2) : '$_currencySymbol${t.total.toStringAsFixed(2)}',
                    style: const TextStyle(fontWeight: FontWeight.w700)),
              ]),
            ),
        ]),
      ),
    );
  }
}

class _StaffTotal {
  final String staffId;
  final String staffName;
  final double total;
  _StaffTotal({required this.staffId, required this.staffName, required this.total});
  factory _StaffTotal.empty() => _StaffTotal(staffId: '', staffName: '-', total: 0.0);
}

class _DateRangePicker extends StatelessWidget {
  final DateTime start;
  final DateTime end;
  final void Function(DateTime, DateTime) onChanged;
  const _DateRangePicker({required this.start, required this.end, required this.onChanged});

  Future<void> _pickStart(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: start,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) onChanged(DateTime(picked.year, picked.month, picked.day), end);
  }

  Future<void> _pickEnd(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: end,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) onChanged(start, DateTime(picked.year, picked.month, picked.day, 23, 59, 59, 999));
  }

  @override
  Widget build(BuildContext context) {
    String two(int v) => v.toString().padLeft(2, '0');
    String fmt(DateTime d) => '${two(d.day)}/${two(d.month)}/${d.year}';
    return Row(children: [
      Expanded(
        child: OutlinedButton.icon(
          onPressed: () => _pickStart(context),
          icon: const Icon(Icons.date_range),
          label: Text('Start: ${fmt(start)}'),
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        child: OutlinedButton.icon(
          onPressed: () => _pickEnd(context),
          icon: const Icon(Icons.event_available),
          label: Text('End: ${fmt(end)}'),
        ),
      ),
    ]);
  }
}


