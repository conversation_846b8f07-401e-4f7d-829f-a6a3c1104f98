import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/garment_model.dart';

class GarmentService {
  final SupabaseClient _supabase;

  GarmentService({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  Future<List<Garment>> getAll({bool activeOnly = false}) async {
    try {
      final base = _supabase.from('garments').select();
      final List<Map<String, dynamic>> response = activeOnly
          ? await base.eq('is_active', true).order('name')
          : await base.order('name');
      return response.map((g) => Garment.fromJson(g)).toList();
    } catch (e) {
      debugPrint('Error fetching garments: $e');
      return [];
    }
  }

  Future<List<Garment>> search(String term) async {
    try {
      final List<Map<String, dynamic>> response = await _supabase
          .from('garments')
          .select()
          .or('name.ilike.%$term%,description.ilike.%$term%')
          .order('name');
      return response.map((g) => Garment.fromJson(g)).toList();
    } catch (e) {
      debugPrint('Error searching garments: $e');
      return [];
    }
  }

  Future<Garment?> getById(String id) async {
    try {
      final Map<String, dynamic> response = await _supabase
          .from('garments')
          .select()
          .eq('id', id)
          .single();
      return Garment.fromJson(response);
    } catch (_) {
      return null;
    }
  }

  Future<Garment> createRaw({
    required String name,
    required String category, // 'clothing', 'household', 'special'
    String? description,
    String? icon,
    bool isActive = true,
  }) async {
    try {
      final Map<String, dynamic> response = await _supabase
          .from('garments')
          .insert({
            'name': name,
            'category': category,
            'description': description,
            'icon': icon,
            'is_active': isActive,
          })
          .select()
          .single();
      return Garment.fromJson(response);
    } catch (e) {
      throw 'Error creating garment: $e';
    }
  }

  Future<Garment> update(String id, Map<String, dynamic> updates) async {
    try {
      await _supabase.from('garments').update(updates).eq('id', id);
      final updated = await getById(id);
      if (updated == null) {
        throw 'Update applied but garment not readable (check RLS policies)';
      }
      return updated;
    } catch (e) {
      throw 'Error updating garment: $e';
    }
  }

  Future<void> delete(String id) async {
    try {
      await _supabase.from('garments').delete().eq('id', id);
    } catch (e) {
      throw 'Error deleting garment: $e';
    }
  }
}
