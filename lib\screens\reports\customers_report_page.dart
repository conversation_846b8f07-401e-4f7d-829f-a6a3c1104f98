import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/customer_service.dart';
import '../../services/order_service.dart';
import '../../services/invoice_service.dart';
import '../../models/customer_model.dart';

class CustomersReportPage extends StatefulWidget {
  const CustomersReportPage({super.key});

  @override
  State<CustomersReportPage> createState() => _CustomersReportPageState();
}

class _CustomersReportPageState extends State<CustomersReportPage> {
  final CustomerService _customerService = CustomerService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  
  bool _isLoading = true;
  String _currencySymbol = '';
  DateTimeRange? _selectedDateRange;
  
  // Data
  // List<Customer> _customers = []; // Removed unused field
  List<Map<String, dynamic>> _topCustomers = [];
  Map<String, dynamic> _customerStats = {};

  @override
  void initState() {
    super.initState();
    // Default to current month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      _currencySymbol = await _orderService.getCurrencySymbol();
      
      // Get all customers
      final customers = await _customerService.getCustomers();
      
      // Get all orders for analysis
      final orders = await _orderService.getUserOrders(Supabase.instance.client.auth.currentUser!.id);
      
      // Filter orders by date range
      final filteredOrders = orders.where((order) {
        if (_selectedDateRange == null) return true;
        return order.createdAt.isAfter(_selectedDateRange!.start) &&
               order.createdAt.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
      
      // Calculate customer analytics
      final Map<String, Map<String, dynamic>> customerAnalytics = {};
      
      for (final order in filteredOrders) {
        final customerId = order.userId;
        
        if (!customerAnalytics.containsKey(customerId)) {
          customerAnalytics[customerId] = {
            'ordersCount': 0,
            'totalSpent': 0.0,
            'lastOrderDate': order.createdAt,
          };
        }
        
        customerAnalytics[customerId]!['ordersCount']++;
        
        // Try to get invoice for revenue calculation
        try {
          final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
          if (invoice.isPaid) {
            customerAnalytics[customerId]!['totalSpent'] += invoice.totalAmount;
          }
        } catch (e) {
          // Invoice might not exist
        }
        
        // Update last order date
        final lastDate = customerAnalytics[customerId]!['lastOrderDate'] as DateTime;
        if (order.createdAt.isAfter(lastDate)) {
          customerAnalytics[customerId]!['lastOrderDate'] = order.createdAt;
        }
      }
      
      // Create top customers list
      final topCustomersList = <Map<String, dynamic>>[];
      
      for (final customer in customers) {
        final analytics = customerAnalytics[customer.id];
        if (analytics != null) {
          topCustomersList.add({
            'customer': customer,
            'ordersCount': analytics['ordersCount'],
            'totalSpent': analytics['totalSpent'],
            'lastOrderDate': analytics['lastOrderDate'],
            'averageOrderValue': analytics['ordersCount'] > 0 
              ? analytics['totalSpent'] / analytics['ordersCount'] 
              : 0.0,
          });
        }
      }
      
      // Sort by total spent
      topCustomersList.sort((a, b) => 
        (b['totalSpent'] as double).compareTo(a['totalSpent'] as double)
      );
      
      // Calculate stats
      final totalCustomersWithOrders = topCustomersList.length;
      final totalRevenue = topCustomersList.fold<double>(
        0, (sum, customer) => sum + (customer['totalSpent'] as double)
      );
      final avgOrdersPerCustomer = totalCustomersWithOrders > 0 
        ? topCustomersList.fold<int>(0, (sum, customer) => sum + (customer['ordersCount'] as int)) / totalCustomersWithOrders
        : 0.0;
      final avgRevenuePerCustomer = totalCustomersWithOrders > 0 
        ? totalRevenue / totalCustomersWithOrders 
        : 0.0;
      
      // Calculate new vs returning customers
      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      
      int newCustomers = 0;
      int returningCustomers = 0;
      
      for (final customerData in topCustomersList) {
        final ordersCount = customerData['ordersCount'] as int;
        final lastOrderDate = customerData['lastOrderDate'] as DateTime;
        
        if (ordersCount == 1 && lastOrderDate.isAfter(thirtyDaysAgo)) {
          newCustomers++;
        } else if (ordersCount > 1) {
          returningCustomers++;
        }
      }
      
      setState(() {
        _topCustomers = topCustomersList.take(20).toList();
        _customerStats = {
          'totalCustomers': customers.length,
          'activeCustomers': totalCustomersWithOrders,
          'totalRevenue': totalRevenue,
          'avgOrdersPerCustomer': avgOrdersPerCustomer,
          'avgRevenuePerCustomer': avgRevenuePerCustomer,
          'newCustomers': newCustomers,
          'returningCustomers': returningCustomers,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading customer data: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Insights'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 4,
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'Select Date Range',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range Display
                  _buildDateRangeCard(),
                  const SizedBox(height: 16),
                  
                  // Customer Stats
                  _buildCustomerStats(),
                  const SizedBox(height: 24),
                  
                  // Customer Segmentation
                  _buildCustomerSegmentation(),
                  const SizedBox(height: 24),
                  
                  // Top Customers
                  _buildTopCustomersSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildDateRangeCard() {
    final startDate = _selectedDateRange?.start.toString().substring(0, 10) ?? 'N/A';
    final endDate = _selectedDateRange?.end.toString().substring(0, 10) ?? 'N/A';
    
    return Card(
      child: ListTile(
        leading: const Icon(Icons.date_range, color: Colors.purple),
        title: const Text('Analysis Period'),
        subtitle: Text('$startDate to $endDate'),
        trailing: const Icon(Icons.edit),
        onTap: _selectDateRange,
      ),
    );
  }

  Widget _buildCustomerStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Customer Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Customers',
                    '${_customerStats['totalCustomers'] ?? 0}',
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Active Customers',
                    '${_customerStats['activeCustomers'] ?? 0}',
                    Icons.person_pin,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Avg Orders/Customer',
                    '${(_customerStats['avgOrdersPerCustomer'] ?? 0).toStringAsFixed(1)}',
                    Icons.shopping_cart,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Avg Revenue/Customer',
                    '$_currencySymbol${(_customerStats['avgRevenuePerCustomer'] ?? 0).toStringAsFixed(2)}',
                    Icons.monetization_on,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSegmentation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Customer Segmentation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.green.withValues(alpha: 0.1),
                child: const Icon(Icons.person_add, color: Colors.green, size: 20),
              ),
              title: const Text('New Customers'),
              subtitle: const Text('First-time customers (last 30 days)'),
              trailing: Text(
                '${_customerStats['newCustomers'] ?? 0}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                  fontSize: 16,
                ),
              ),
            ),
            
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue.withValues(alpha: 0.1),
                child: const Icon(Icons.repeat, color: Colors.blue, size: 20),
              ),
              title: const Text('Returning Customers'),
              subtitle: const Text('Customers with multiple orders'),
              trailing: Text(
                '${_customerStats['returningCustomers'] ?? 0}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                  fontSize: 16,
                ),
              ),
            ),
            
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.orange.withValues(alpha: 0.1),
                child: const Icon(Icons.person_outline, color: Colors.orange, size: 20),
              ),
              title: const Text('Inactive Customers'),
              subtitle: const Text('Registered but no orders'),
              trailing: Text(
                '${(_customerStats['totalCustomers'] ?? 0) - (_customerStats['activeCustomers'] ?? 0)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCustomersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top Customers by Revenue',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_topCustomers.isEmpty)
              const Center(
                child: Text(
                  'No customer data for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _topCustomers.length,
                itemBuilder: (context, index) {
                  final customerData = _topCustomers[index];
                  final customer = customerData['customer'] as Customer;
                  final ordersCount = customerData['ordersCount'] as int;
                  final totalSpent = customerData['totalSpent'] as double;
                  final avgOrderValue = customerData['averageOrderValue'] as double;
                  final lastOrderDate = customerData['lastOrderDate'] as DateTime;
                  
                  // Determine customer tier
                  Color tierColor;
                  String tier;
                  IconData tierIcon;
                  
                  if (totalSpent >= 1000) {
                    tier = 'VIP';
                    tierColor = Colors.purple;
                    tierIcon = Icons.star;
                  } else if (totalSpent >= 500) {
                    tier = 'Gold';
                    tierColor = Colors.orange;
                    tierIcon = Icons.workspace_premium;
                  } else if (totalSpent >= 200) {
                    tier = 'Silver';
                    tierColor = Colors.grey;
                    tierIcon = Icons.military_tech;
                  } else {
                    tier = 'Bronze';
                    tierColor = Colors.brown;
                    tierIcon = Icons.person;
                  }
                  
                  return ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: tierColor.withValues(alpha: 0.1),
                      child: Icon(tierIcon, color: tierColor, size: 20),
                    ),
                    title: Text(customer.fullName),
                    subtitle: Text('$tier Customer • $ordersCount orders'),
                    trailing: Text(
                      '$_currencySymbol${totalSpent.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: tierColor,
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Phone:', style: TextStyle(color: Colors.grey[600])),
                                Text(customer.phone ?? 'N/A'),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Avg Order Value:', style: TextStyle(color: Colors.grey[600])),
                                Text('$_currencySymbol${avgOrderValue.toStringAsFixed(2)}'),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Last Order:', style: TextStyle(color: Colors.grey[600])),
                                Text(lastOrderDate.toString().substring(0, 10)),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}