import 'package:flutter/foundation.dart';

@immutable
class Country {
  final String id;
  final String name;
  final String code;
  final String? phoneCode;
  final String? flagEmoji;
  final bool isActive;

  const Country({
    required this.id,
    required this.name,
    required this.code,
    this.phoneCode,
    this.flagEmoji,
    this.isActive = true,
  });

  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      phoneCode: json['phone_code'] as String?,
      flagEmoji: json['flag_emoji'] as String?,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'phone_code': phoneCode,
      'flag_emoji': flagEmoji,
      'is_active': isActive,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Country && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;

  String get displayName => flagEmoji != null ? '$flagEmoji $name' : name;
  
  String get fullDisplayName => flagEmoji != null 
      ? '$flagEmoji $name ($code)' 
      : '$name ($code)';

  @override
  String toString() => '$name ($code)';
}
