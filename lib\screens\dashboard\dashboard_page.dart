import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/auth_service.dart';
import '../../widgets/app_drawer.dart';
import '../../services/settings_service.dart';
import '../orders/place_order_page.dart';
import '../orders/order_history_page.dart';
import '../stores/stores_page.dart';
import '../settings_page.dart';
import '../payments/select_order_for_payment_page.dart';
import '../expenses/expenses_page.dart';
import '../../services/order_service.dart';
import '../../services/invoice_service.dart';
import '../../services/currency_service.dart' as currency_service;
import '../../models/order_model.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  late AuthService authService;
  late OrderService _orderService;
  late InvoiceService _invoiceService;
  late currency_service.AppCurrencyService _currencyService;
  late SettingsService _settingsService;
  String? _companyName;
  bool _companyNameLoaded = false;
  bool get _companyNameMissing => _companyNameLoaded && (_companyName == null || _companyName?.trim().isEmpty == true);

  int _pendingOrders = 0;
  int _readyForPickup = 0;
  int _paymentsDue = 0;
  String _todaysSales = '—';
  String _thisWeeksSales = '—';
  int _ordersThisWeek = 0;
  String _thisMonthsSales = '—';
  bool _loadingMetrics = true;
  String _currencySymbol = '';

  // Today vs Yesterday metrics
  String _todayVsYesterdayPercentText = '—';
  String _todayVsYesterdayAmountText = '—';
  bool _todayVsYesterdayUp = true;

  // Compact formatter for large numbers/currency values (e.g., 10123 -> 10.1k)
  String _compactValue(String value, {double threshold = 1000}) {
    if (value.trim().isEmpty || value == '—') return value;
    // Extract non-digit prefix (currency or text)
    final prefix = value.replaceAll(RegExp(r'[0-9.,+-]'), '').trim();
    final numericPart = value.replaceAll(RegExp(r'[^0-9.-]'), '');
    final parsed = double.tryParse(numericPart.replaceAll(',', ''));
    if (parsed == null || parsed.abs() < threshold) return value;
    double n = parsed.abs();
    String suffix;
    double divisor;
    if (n >= 1e9) {
      suffix = 'b';
      divisor = 1e9;
    } else if (n >= 1e6) {
      suffix = 'm';
      divisor = 1e6;
    } else {
      suffix = 'k';
      divisor = 1e3;
    }
    final compact = (n / divisor);
    final decimals = compact >= 100 ? 0 : 1;
    final compactStr = compact.toStringAsFixed(decimals);
    final sign = parsed < 0 ? '-' : '';
    final pre = prefix.isNotEmpty ? prefix : '';
    return '$sign$pre$compactStr$suffix';
  }

  String get _currentMonthLabel {
    final now = DateTime.now();
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${monthNames[now.month - 1]} ${now.year}';
  }

  @override
  void initState() {
    super.initState();
    authService = AuthService(supabase: Supabase.instance.client);
    _orderService = OrderService(supabase: Supabase.instance.client);
    _invoiceService = InvoiceService(supabase: Supabase.instance.client);
    _currencyService = currency_service.AppCurrencyService();
    _settingsService = SettingsService(supabase: Supabase.instance.client);
    _loadCurrencySymbol();
    _loadMetrics();
    
    // Check company name after page has loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadCompanyName();
        }
      });
    });
  }

  Future<void> _loadCurrencySymbol() async {
    try {
      final symbol = await _currencyService.getCurrencySymbol();
      if (!mounted) return;
      setState(() => _currencySymbol = symbol);
    } catch (_) {
      // Ignore; defaults will be used by formatters
    }
  }

  Future<void> _loadCompanyName() async {
    try {
      final settings = await _settingsService.getSettings();
      if (!mounted) return;
      setState(() {
        final name = settings?.companyName?.trim();
        _companyName = (name == null || name.isEmpty) ? null : name;
        _companyNameLoaded = true;
      });
    } catch (_) {
      // If there's an error loading settings, mark as loaded but keep current state
      if (mounted) {
        setState(() {
          _companyNameLoaded = true;
        });
      }
    }
  }

  Future<void> _loadMetrics() async {
    if (!mounted) return;
    setState(() => _loadingMetrics = true);
    try {
      // Get user ID for filtering user-specific data
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        throw 'User not authenticated';
      }

      // Fetch user's orders and filter by status locally
      debugPrint('Dashboard: Fetching user orders for userId: $userId');
      final userOrders = await _orderService.getUserOrders(userId);
      debugPrint('Dashboard: Found ${userOrders.length} total orders');
      
      final pending = userOrders.where((order) => order.status == OrderStatus.pending).toList();
      final completed = userOrders.where((order) => order.status == OrderStatus.completed).toList();
      debugPrint('Dashboard: Pending orders: ${pending.length}, Completed orders: ${completed.length}');
      
      // Get unpaid invoices for the user
      debugPrint('Dashboard: Fetching unpaid invoices');
      final unpaid = await _invoiceService.getUnpaidInvoices();
      debugPrint('Dashboard: Found ${unpaid.length} unpaid invoices');

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      // Start of week (Monday)
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      // Start of month
      final startOfMonth = DateTime(now.year, now.month, 1);
      debugPrint('Dashboard: Fetching today\'s payment summary from $startOfDay to $now');
      final summary = await _invoiceService.getPaymentSummary(
        startDate: startOfDay,
        endDate: now,
      );
      debugPrint('Dashboard: Today\'s payment summary: ${summary.length} entries');
      final double totalToday = summary.fold<double>(0.0, (sum, row) => sum + ((row['total'] as num).toDouble()));
      debugPrint('Dashboard: Today\'s total: $totalToday');
      final formattedTotal = await _currencyService.formatAmount(totalToday);

      // Yesterday sales
      final startOfYesterday = startOfDay.subtract(const Duration(days: 1));
      final yesterdaySummary = await _invoiceService.getPaymentSummary(
        startDate: startOfYesterday,
        endDate: startOfDay,
      );
      final double totalYesterday =
          yesterdaySummary.fold<double>(0.0, (sum, row) => sum + ((row["total"] as num).toDouble()));

      final double diff = totalToday - totalYesterday;
      final bool isUp = diff >= 0;
      final double percentChange = totalYesterday == 0
          ? (totalToday > 0 ? 100.0 : 0.0)
          : (diff / totalYesterday) * 100.0;
      final String percentText = '${isUp ? '+' : '-'}${percentChange.abs().toStringAsFixed(1)}%';
      final String diffAmountAbsFormatted = await _currencyService.formatAmount(diff.abs());
      final String diffText = '${isUp ? '+' : '-'}$diffAmountAbsFormatted';

      // Weekly sales using payments
      final weeklySummary = await _invoiceService.getPaymentSummary(
        startDate: startOfWeek,
        endDate: now,
      );
      final double totalWeek =
          weeklySummary.fold<double>(0.0, (sum, row) => sum + ((row['total'] as num).toDouble()));
      final formattedWeekTotal = await _currencyService.formatAmount(totalWeek);

      // Weekly orders count from the user's orders
      final weeklyOrders = userOrders.where((order) {
        final orderDate = order.createdAt;
        return orderDate.isAfter(startOfWeek) && orderDate.isBefore(now.add(const Duration(days: 1)));
      }).toList();
      final int weeklyOrdersCount = weeklyOrders.length;

      // Monthly sales
      final monthlySummary = await _invoiceService.getPaymentSummary(
        startDate: startOfMonth,
        endDate: now,
      );
      final double totalMonth =
          monthlySummary.fold<double>(0.0, (sum, row) => sum + ((row['total'] as num).toDouble()));
      final formattedMonthTotal = await _currencyService.formatAmount(totalMonth);

      if (!mounted) return;
      setState(() {
        _pendingOrders = pending.length;
        _readyForPickup = completed.length;
        _paymentsDue = unpaid.length;
        _todaysSales = formattedTotal;
        _thisWeeksSales = formattedWeekTotal;
        _ordersThisWeek = weeklyOrdersCount;
        _thisMonthsSales = formattedMonthTotal;
        _loadingMetrics = false;
        _todayVsYesterdayUp = isUp;
        _todayVsYesterdayPercentText = percentText;
        _todayVsYesterdayAmountText = diffText;
      });
    } catch (e) {
      debugPrint('Error loading dashboard metrics: $e');
      if (!mounted) return;
      setState(() {
        _loadingMetrics = false;
        // Set default values when there's an error
        _pendingOrders = 0;
        _readyForPickup = 0;
        _paymentsDue = 0;
        _todaysSales = '—';
        _thisWeeksSales = '—';
        _ordersThisWeek = 0;
        _thisMonthsSales = '—';
        _todayVsYesterdayPercentText = '—';
        _todayVsYesterdayAmountText = '—';
      });

      // Show error to user if needed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load dashboard data: ${e.toString()}'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadMetrics,
            ),
          ),
        );
      }
    }
  }

  Widget _buildMonthlyHighlightCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade700, Colors.green.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.shade200.withValues(alpha: 0.5),
            blurRadius: 14,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.calendar_month, color: Colors.white, size: 36),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sales — $_currentMonthLabel${_currencySymbol.isNotEmpty ? ' (' '$_currencySymbol' ')' : ''}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 6),
                _loadingMetrics
                    ? const SizedBox(height: 18, width: 18, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                    : Text(
                        _compactValue(_thisMonthsSales),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.3,
                        ),
                      ),
                const SizedBox(height: 8),
                Text(
                  'As of ${DateTime.now().day.toString().padLeft(2, '0')} ${_currentMonthLabel.split(' ').first}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 10),
                _buildTodayVsYesterdayRow(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayVsYesterdayRow() {
    final Color upColor = Colors.lightGreenAccent.shade100;
    final Color downColor = Colors.redAccent.shade100;
    final IconData arrow = _todayVsYesterdayUp ? Icons.arrow_upward : Icons.arrow_downward;
    final Color chipColor = _todayVsYesterdayUp ? upColor : downColor;

    return Wrap(
      spacing: 8,
      runSpacing: 6,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(arrow, size: 14, color: Colors.white),
              const SizedBox(width: 4),
              Text(
                _todayVsYesterdayPercentText,
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: chipColor.withValues(alpha: 0.25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _todayVsYesterdayAmountText,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
          ),
        ),
        Text(
          'Today vs Yesterday',
          style: TextStyle(color: Colors.white.withValues(alpha: 0.9)),
        ),
      ],
    );
  }

  // Logout functionality is now handled by AppDrawer

  @override
  Widget build(BuildContext context) {
    // Removed unused user and displayName variables as welcome card was removed

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        toolbarHeight: 72,
        leadingWidth: 56,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => Scaffold.of(context).openDrawer(),
            tooltip: 'Menu',
          ),
        ),
        titleSpacing: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _companyName?.isNotEmpty == true ? _companyName! : 'Dashboard',
              style: const TextStyle(fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: 4),
            Wrap(
              spacing: 6,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Icon(
                  Icons.calendar_month,
                  size: 16,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
                Text(
                  _currentMonthLabel + (_currencySymbol.isNotEmpty ? '  (' '$_currencySymbol' ')' : ''),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadMetrics,
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.payments_outlined),
                tooltip: 'Payments Due',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SelectOrderForPaymentPage(),
                    ),
                  );
                },
              ),
              if (_paymentsDue > 0)
                Positioned(
                  right: 10,
                  top: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.redAccent,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      _paymentsDue.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 8),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade700, Colors.blue.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
        ),
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
      ),
      drawer: const AppDrawer(),
      body: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                if (_companyNameMissing) _buildCompanyNameNotice(context),
                _buildMonthlyHighlightCard(),
                const SizedBox(height: 24),
                Text(
                  'Today',
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                LayoutBuilder(
                  builder: (context, constraints) {
                    const spacing = 12.0;
                    final itemWidth = (constraints.maxWidth - spacing) / 2;
                    return Wrap(
                      spacing: spacing,
                      runSpacing: spacing,
                      children: [
                        _buildMetricPill(
                          color: Colors.teal,
                          icon: Icons.attach_money,
                          label:
                              "Today's Sales${_currencySymbol.isNotEmpty ? ' (' '$_currencySymbol' ')' : ''}",
                          value: _compactValue(_todaysSales),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                        _buildMetricPill(
                          color: Colors.orange,
                          icon: Icons.pending_actions,
                          label: 'Pending Orders',
                          value: _pendingOrders.toString(),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                        _buildMetricPill(
                          color: Colors.deepPurple,
                          icon: Icons.check_circle_outline,
                          label: 'Ready for Pickup',
                          value: _readyForPickup.toString(),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                        _buildMetricPill(
                          color: Colors.redAccent,
                          icon: Icons.payments_outlined,
                          label: 'Payments Due',
                          value: _paymentsDue.toString(),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 24),
                Text(
                  'This Week',
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                LayoutBuilder(
                  builder: (context, constraints) {
                    const spacing = 12.0;
                    final itemWidth = (constraints.maxWidth - spacing) / 2;
                    return Wrap(
                      spacing: spacing,
                      runSpacing: spacing,
                      children: [
                        _buildMetricPill(
                          color: Colors.indigo,
                          icon: Icons.trending_up,
                          label:
                              "This Week's Sales${_currencySymbol.isNotEmpty ? ' (' '$_currencySymbol' ')' : ''}",
                          value: _compactValue(_thisWeeksSales),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                        _buildMetricPill(
                          color: Colors.blueGrey,
                          icon: Icons.calendar_view_week,
                          label: 'Orders This Week',
                          value: _ordersThisWeek.toString(),
                          loading: _loadingMetrics,
                          width: itemWidth,
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 24),
                const SizedBox(height: 24),
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
              ]),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            sliver: SliverGrid.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildActionCard(
                  icon: Icons.add_shopping_cart,
                  title: 'New Order',
                  subtitle: 'Create a new laundry order',
                  color: Colors.green,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PlaceOrderPage(),
                      ),
                    );
                  },
                ),
                _buildActionCard(
                  icon: Icons.history,
                  title: 'Order History',
                  subtitle: 'Check your past orders',
                  color: Colors.blue,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OrderHistoryPage(),
                      ),
                    );
                  },
                ),
                _buildActionCard(
                  icon: Icons.receipt_long,
                  title: 'Expenses',
                  subtitle: 'Manage expenses',
                  color: Colors.brown,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ExpensesPage(),
                      ),
                    );
                  },
                ),
                _buildActionCard(
                  icon: Icons.track_changes,
                  title: 'Track Orders',
                  subtitle: 'Monitor active orders',
                  color: Colors.orange,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OrderHistoryPage(showActiveOnly: true),
                      ),
                    );
                  },
                ),
                _buildActionCard(
                  icon: Icons.payments_outlined,
                  title: 'Receive Payment',
                  subtitle: 'Record a customer payment',
                  color: Colors.teal,
                  badgeText: _paymentsDue > 0 ? _paymentsDue.toString() : null,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SelectOrderForPaymentPage(),
                      ),
                    );
                  },
                ),
                _buildActionCard(
                  icon: Icons.store,
                  title: 'Find Stores',
                  subtitle: 'Browse nearby stores',
                  color: Colors.purple,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const StoresPage(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SliverToBoxAdapter(child: SizedBox(height: 16)),
        ],
      ),
    );
  }

  Widget _buildCompanyNameNotice(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.12),
        border: Border.all(color: Colors.amber.withValues(alpha: 0.5)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.info_outline, color: Colors.amber, size: 22),
          const SizedBox(width: 10),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add your company name',
                  style: TextStyle(fontWeight: FontWeight.w700),
                ),
                SizedBox(height: 4),
                Text(
                  'Set a company name in Settings to personalize your dashboard and invoices.',
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          OutlinedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const SettingsPage()),
              ).then((_) => _loadCompanyName());
            },
            child: const Text('Set now'),
          )
        ],
      ),
    );
  }

  Widget _buildMetricPill({
    required Color color,
    required IconData icon,
    required String label,
    required String value,
    bool loading = false,
    double? width,
  }) {
    return Container(
      width: width ?? 220,
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 2),
                loading
                    ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2))
                    : Text(
                        value,
                        style: TextStyle(
                          fontWeight: FontWeight.w800,
                          fontSize: 18,
                          color: color,
                        ),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    String? badgeText,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    size: 40,
                    color: color,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            if (badgeText != null)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.redAccent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    badgeText,
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
