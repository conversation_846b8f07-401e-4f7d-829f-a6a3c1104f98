import 'package:flutter/material.dart';
import '../../services/expense_service.dart';
import '../../services/store_service.dart';
import '../../services/currency_service.dart' as currency_service;
import '../../models/expense_model.dart';
import '../../models/store_model.dart';

class ExpensesReportPage extends StatefulWidget {
  const ExpensesReportPage({super.key});

  @override
  State<ExpensesReportPage> createState() => _ExpensesReportPageState();
}

class _ExpensesReportPageState extends State<ExpensesReportPage> {
  // Supabase client not needed directly here (services handle access)
  final ExpenseService _expenseService = ExpenseService();
  final StoreService _storeService = StoreService();
  final currency_service.AppCurrencyService _currency = currency_service.AppCurrencyService();

  bool _isLoading = true;
  String _currencySymbol = '';

  // Filters
  String _rangePreset = 'This Month';
  DateTime _start = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime _end = DateTime.now();
  String? _selectedStoreId; // null = all stores
  String? _compareStoreId; // optional compare
  final Set<ExpenseCategory> _selectedCategories = {};

  // Data
  List<Store> _stores = [];
  double _total = 0.0;
  double _totalForStore = 0.0;
  double _totalForCompare = 0.0;
  Map<String, double> _byCategory = {}; // category displayName -> amount
  Map<String, double> _byStore = {}; // store id -> amount

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    setState(() => _isLoading = true);
    _currencySymbol = await _currency.getCurrencySymbol();
    _stores = await _storeService.getActiveStores();
    await _loadData();
  }

  void _applyPreset(String preset) {
    final now = DateTime.now();
    if (preset == 'This Month') {
      _start = DateTime(now.year, now.month, 1);
      _end = now;
    } else if (preset == 'Last Month') {
      final last = DateTime(now.year, now.month - 1, 1);
      _start = last;
      _end = DateTime(last.year, last.month + 1, 0, 23, 59, 59, 999);
    } else if (preset == 'This Year') {
      _start = DateTime(now.year, 1, 1);
      _end = now;
    }
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // fetch expenses including allocations for per-store breakdown
      final expenses = await _expenseService.listExpenses(
        startDate: _start,
        endDate: _end,
        categories: _selectedCategories.isEmpty ? null : _selectedCategories.toList(),
        includeAllocations: true,
      );

      double total = 0.0;
      final Map<String, double> byCat = {};
      final Map<String, double> byStore = {};

      for (final e in expenses) {
        total += e.amount;
        final catName = e.category.displayName;
        byCat.update(catName, (v) => v + e.amount, ifAbsent: () => e.amount);

        if (e.storeId != null) {
          byStore.update(e.storeId!, (v) => v + e.amount, ifAbsent: () => e.amount);
        }
        if (e.hasAllocations) {
          for (final a in e.allocations!) {
            byStore.update(a.storeId, (v) => v + a.amount, ifAbsent: () => a.amount);
          }
        }
      }

      double totalForStore = 0.0;
      if (_selectedStoreId != null) {
        totalForStore = byStore[_selectedStoreId!] ?? 0.0;
      }
      double totalForCompare = 0.0;
      if (_compareStoreId != null) {
        totalForCompare = byStore[_compareStoreId!] ?? 0.0;
      }

      if (!mounted) return;
      setState(() {
        _total = total;
        _byCategory = byCat;
        _byStore = byStore;
        _totalForStore = totalForStore;
        _totalForCompare = totalForCompare;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load expenses: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expenses Report'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [IconButton(onPressed: _loadData, icon: const Icon(Icons.refresh))],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _rangePreset,
                      decoration: const InputDecoration(labelText: 'Range', border: OutlineInputBorder()),
                      items: const [
                        DropdownMenuItem(value: 'This Month', child: Text('This Month')),
                        DropdownMenuItem(value: 'Last Month', child: Text('Last Month')),
                        DropdownMenuItem(value: 'This Year', child: Text('This Year')),
                        DropdownMenuItem(value: 'Custom', child: Text('Custom')),
                      ],
                      onChanged: (v) async {
                        if (v == null) return;
                        setState(() => _rangePreset = v);
                        if (v != 'Custom') {
                          _applyPreset(v);
                          await _loadData();
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  if (_rangePreset == 'Custom')
                    Expanded(child: _DateRangePicker(start: _start, end: _end, onChanged: (s, e) async { setState((){_start=s;_end=e;}); await _loadData(); }))
                ]),
                const SizedBox(height: 12),
                Row(children: [
                  Expanded(
                    child: DropdownButtonFormField<String?>(
                      value: _selectedStoreId,
                      decoration: const InputDecoration(labelText: 'View Store', border: OutlineInputBorder()),
                      items: [
                        const DropdownMenuItem<String?>(value: null, child: Text('All Stores')),
                        ..._stores.map((s) => DropdownMenuItem<String?>(value: s.id, child: Text(s.name)))
                      ],
                      onChanged: (v) async { setState(()=>_selectedStoreId = v); await _loadData(); },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: DropdownButtonFormField<String?>(
                      value: _compareStoreId,
                      decoration: const InputDecoration(labelText: 'Compare Store', border: OutlineInputBorder()),
                      items: [
                        const DropdownMenuItem<String?>(value: null, child: Text('None')),
                        ..._stores.map((s) => DropdownMenuItem<String?>(value: s.id, child: Text(s.name)))
                      ],
                      onChanged: (v) async { setState(()=>_compareStoreId = v); await _loadData(); },
                    ),
                  ),
                ]),
                const SizedBox(height: 12),
                Wrap(spacing: 8, runSpacing: 8, children: [
                  const Text('Categories:'),
                  ...ExpenseCategory.values.map((c) {
                    final on = _selectedCategories.contains(c);
                    return FilterChip(
                      label: Text(c.displayName),
                      selected: on,
                      onSelected: (sel) async {
                        setState(() { if (sel) { _selectedCategories.add(c); } else { _selectedCategories.remove(c); } });
                        await _loadData();
                      },
                    );
                  })
                ]),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    children: [
                      _buildTotalsCard(),
                      const SizedBox(height: 12),
                      _buildByCategoryCard(),
                      const SizedBox(height: 12),
                      _buildByStoreCard(),
                      const SizedBox(height: 12),
                      if (_selectedStoreId != null || _compareStoreId != null) _buildCompareStoresCard(),
                    ],
                  ),
          )
        ],
      ),
    );
  }

  Widget _buildTotalsCard() {
    final totalText = '$_currencySymbol${_total.toStringAsFixed(2)}';
    final viewText = _selectedStoreId == null
        ? null
        : 'This store: $_currencySymbol${_totalForStore.toStringAsFixed(2)}';
    return Card(
      elevation: 2,
      child: ListTile(
        leading: const Icon(Icons.summarize, color: Colors.blue),
        title: const Text('Total Expenses (Selected Range)', style: TextStyle(fontWeight: FontWeight.w700)),
        subtitle: viewText != null ? Text(viewText) : null,
        trailing: Text(totalText, style: const TextStyle(fontWeight: FontWeight.w800)),
      ),
    );
  }

  Widget _buildByCategoryCard() {
    final entries = _byCategory.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('By Category', style: TextStyle(fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          for (final e in entries)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(children: [
                Expanded(child: Text(e.key)),
                Text('$_currencySymbol${e.value.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.w700)),
              ]),
            ),
        ]),
      ),
    );
  }

  Widget _buildByStoreCard() {
    final entries = _byStore.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    String storeName(String id) {
      return _stores
          .firstWhere(
            (s) => s.id == id,
            orElse: () => Store(
              id: id,
              name: 'Store ${id.substring(0, 6)}…',
              address: '',
              phone: null,
              email: null,
              description: null,
              storeNumber: null,
              country: null,
              isActive: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          )
          .name;
    }
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('By Store', style: TextStyle(fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          for (final e in entries)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(children: [
                Expanded(child: Text(storeName(e.key))),
                Text('$_currencySymbol${e.value.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.w700)),
              ]),
            ),
        ]),
      ),
    );
  }

  Widget _buildCompareStoresCard() {
    final aId = _selectedStoreId;
    final bId = _compareStoreId;
    if (aId == null && bId == null) return const SizedBox.shrink();
    final a = aId == null ? null : _stores.firstWhere((s) => s.id == aId, orElse: () => _stores.first);
    final b = bId == null ? null : _stores.firstWhere((s) => s.id == bId, orElse: () => _stores.first);
    final aTotal = aId == null ? _total : _totalForStore;
    final bTotal = bId == null ? 0.0 : _totalForCompare;
    final diff = bTotal - aTotal;
    final up = diff >= 0;
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('Compare Stores', style: TextStyle(fontWeight: FontWeight.w800)),
          const SizedBox(height: 8),
          Row(children: [
            Expanded(child: _miniTile(title: a?.name ?? 'All Stores', amount: aTotal)),
            const SizedBox(width: 8),
            Expanded(child: _miniTile(title: b?.name ?? 'None', amount: bTotal)),
          ]),
          const SizedBox(height: 8),
          Row(children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(color: (up ? Colors.green : Colors.redAccent).withValues(alpha: 0.12), borderRadius: BorderRadius.circular(8)),
              child: Row(mainAxisSize: MainAxisSize.min, children: [
                Icon(up ? Icons.arrow_upward : Icons.arrow_downward, size: 14, color: up ? Colors.green : Colors.redAccent),
                const SizedBox(width: 4),
                Text('${up ? '+' : '-'}${diff.abs().toStringAsFixed(2)}', style: TextStyle(color: up ? Colors.green : Colors.redAccent, fontWeight: FontWeight.w700)),
              ]),
            ),
          ])
        ]),
      ),
    );
  }

  Widget _miniTile({required String title, required double amount}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(title, maxLines: 1, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 6),
        Text('$_currencySymbol${amount.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.w800, color: Colors.blue)),
      ]),
    );
  }
}

class _DateRangePicker extends StatelessWidget {
  final DateTime start;
  final DateTime end;
  final void Function(DateTime, DateTime) onChanged;
  const _DateRangePicker({required this.start, required this.end, required this.onChanged});

  Future<void> _pickStart(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: start,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) onChanged(DateTime(picked.year, picked.month, picked.day), end);
  }

  Future<void> _pickEnd(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: end,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) onChanged(start, DateTime(picked.year, picked.month, picked.day, 23, 59, 59, 999));
  }

  @override
  Widget build(BuildContext context) {
    String two(int v) => v.toString().padLeft(2, '0');
    String fmt(DateTime d) => '${two(d.day)}/${two(d.month)}/${d.year}';
    return Row(children: [
      Expanded(
        child: OutlinedButton.icon(
          onPressed: () => _pickStart(context),
          icon: const Icon(Icons.date_range),
          label: Text('Start: ${fmt(start)}'),
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        child: OutlinedButton.icon(
          onPressed: () => _pickEnd(context),
          icon: const Icon(Icons.event_available),
          label: Text('End: ${fmt(end)}'),
        ),
      ),
    ]);
  }
}


