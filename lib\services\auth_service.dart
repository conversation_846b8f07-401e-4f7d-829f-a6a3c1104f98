import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Custom exception for email verification errors
class EmailNotVerifiedException implements Exception {
  final String message;
  EmailNotVerifiedException(this.message);
  
  @override
  String toString() => message;
}

class AuthService {
  final SupabaseClient supabase;
  AuthService({required this.supabase});

  // Register
  Future<AuthResponse> signUp(String email, String password, {String? fullName, String? country}) async {
    try {
      Map<String, dynamic>? userData;
      if (fullName != null || country != null) {
        userData = {};
        if (fullName != null) userData['full_name'] = fullName;
        if (country != null) userData['country'] = country;
      }
      
      final response = await supabase.auth.signUp(
        email: email,
        password: password,
        data: userData,
      );
      return response;
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Sign In
  Future<AuthResponse> signIn(String email, String password) async {
    try {
      final response = await supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } on AuthException catch (e) {
      // Check if it's an email verification error
      if (e.message.toLowerCase().contains('email not confirmed') ||
          e.message.toLowerCase().contains('email not verified') ||
          e.message.toLowerCase().contains('confirm your email')) {
        throw EmailNotVerifiedException(e.message);
      }
      throw Exception(e.message);
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      await supabase.auth.signOut();
      // Clear saved credentials when signing out
      await clearSavedCredentials();
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Forgot password with OTP
  Future<void> sendPasswordResetOTP(String email) async {
    try {
      await supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: null, // We'll handle the reset flow in-app
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Verify OTP for password reset
  Future<void> verifyPasswordResetOTP(String email, String token) async {
    try {
      await supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.recovery,
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Verify OTP for email verification (after registration)
  Future<AuthResponse> verifyEmailOTP(String email, String token) async {
    try {
      final response = await supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.email,
      );
      return response;
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Resend email verification OTP
  Future<void> resendEmailVerificationOTP(String email) async {
    try {
      await supabase.auth.resend(
        type: OtpType.email,
        email: email,
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Send OTP for login (magic link alternative)
  Future<void> sendLoginOTP(String email) async {
    try {
      await supabase.auth.signInWithOtp(
        email: email,
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Verify OTP for login
  Future<AuthResponse> verifyLoginOTP(String email, String token) async {
    try {
      final response = await supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.magiclink,
      );
      return response;
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Update password after OTP verification
  Future<void> updatePassword(String newPassword) async {
    try {
      await supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Get current user
  User? get currentUser => supabase.auth.currentUser;

  // Check if user is signed in
  bool get isSignedIn => currentUser != null;

  // Get user display name
  String get userDisplayName {
    final user = currentUser;
    if (user?.userMetadata?['full_name'] != null) {
      return user!.userMetadata!['full_name'];
    }
    return user?.email?.split('@').first ?? 'User';
  }

  // Get user country
  String? get userCountry {
    final user = currentUser;
    return user?.userMetadata?['country'];
  }

  // Remember me using Shared Preferences
  Future<void> saveCredentials(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('email', email);
    await prefs.setString('password', password);
    await prefs.setBool('remember_me', true);
  }

  Future<Map<String, String>?> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final rememberMe = prefs.getBool('remember_me') ?? false;

    if (!rememberMe) return null;

    final email = prefs.getString('email');
    final password = prefs.getString('password');
    if (email != null && password != null) {
      return {'email': email, 'password': password};
    }
    return null;
  }

  Future<void> clearSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('email');
    await prefs.remove('password');
    await prefs.remove('remember_me');
  }

  // Session management
  Stream<AuthState> get authStateChanges => supabase.auth.onAuthStateChange;

  // Check if session is valid
  bool get hasValidSession {
    final session = supabase.auth.currentSession;
    if (session == null) return false;

    final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    return DateTime.now().isBefore(expiresAt);
  }

  // Refresh session
  Future<void> refreshSession() async {
    try {
      await supabase.auth.refreshSession();
    } on AuthException catch (e) {
      throw Exception(e.message);
    }
  }

  // Biometric authentication methods
  Future<void> enableBiometricAuth(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('biometric_email', email);
    await prefs.setString('biometric_password', password);
    await prefs.setBool('biometric_enabled', true);
  }

  // Safety helper: invalidate biometric creds if sign-in fails with them
  Future<void> validateOrClearBiometricCreds() async {
    final creds = await getBiometricCredentials();
    if (creds == null) return;
    try {
      await signIn(creds['email']!, creds['password']!);
    } catch (_) {
      await disableBiometricAuth();
    }
  }

  Future<void> disableBiometricAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('biometric_email');
    await prefs.remove('biometric_password');
    await prefs.setBool('biometric_enabled', false);
  }

  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('biometric_enabled') ?? false;
  }

  Future<Map<String, String>?> getBiometricCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString('biometric_email');
    final password = prefs.getString('biometric_password');
    
    if (email != null && password != null) {
      return {'email': email, 'password': password};
    }
    return null;
  }
}

