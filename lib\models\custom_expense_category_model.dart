import 'package:flutter/material.dart';

@immutable
class CustomExpenseCategory {
  final String id;
  final String name;
  final String? description;
  final String? iconName;
  final String? colorHex;
  final bool isActive;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CustomExpenseCategory({
    required this.id,
    required this.name,
    this.description,
    this.iconName,
    this.colorHex,
    required this.isActive,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomExpenseCategory.fromJson(Map<String, dynamic> json) {
    return CustomExpenseCategory(
      id: (json['id'] as String?) ?? '',
      name: (json['name'] as String?) ?? '',
      description: json['description'] as String?,
      iconName: json['icon_name'] as String?,
      colorHex: json['color_hex'] as String?,
      isActive: (json['is_active'] as bool?) ?? true,
      createdBy: (json['created_by'] as String?) ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon_name': iconName,
      'color_hex': colorHex,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CustomExpenseCategory copyWith({
    String? id,
    String? name,
    String? description,
    String? iconName,
    String? colorHex,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomExpenseCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      colorHex: colorHex ?? this.colorHex,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  IconData get icon {
    if (iconName == null) return Icons.category;
    return _iconMap[iconName] ?? Icons.category;
  }

  Color get color {
    if (colorHex == null) return Colors.grey;
    try {
      return Color(int.parse(colorHex!.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.grey;
    }
  }

  String get displayName => name;

  // Available icons for custom categories
  static const Map<String, IconData> _iconMap = {
    'home': Icons.home,
    'flash_on': Icons.flash_on,
    'people': Icons.people,
    'inventory': Icons.inventory,
    'directions_car': Icons.directions_car,
    'build': Icons.build,
    'campaign': Icons.campaign,
    'money_off': Icons.money_off,
    'category': Icons.category,
    'business': Icons.business,
    'local_gas_station': Icons.local_gas_station,
    'restaurant': Icons.restaurant,
    'school': Icons.school,
    'medical_services': Icons.medical_services,
    'security': Icons.security,
    'wifi': Icons.wifi,
    'phone': Icons.phone,
    'computer': Icons.computer,
    'print': Icons.print,
    'cleaning_services': Icons.cleaning_services,
    'handyman': Icons.handyman,
    'plumbing': Icons.plumbing,
    'electrical_services': Icons.electrical_services,
    'local_shipping': Icons.local_shipping,
    'flight': Icons.flight,
    'hotel': Icons.hotel,
    'shopping_cart': Icons.shopping_cart,
    'credit_card': Icons.credit_card,
    'account_balance': Icons.account_balance,
    'savings': Icons.savings,
    'trending_up': Icons.trending_up,
    'analytics': Icons.analytics,
    'receipt': Icons.receipt,
    'description': Icons.description,
    'folder': Icons.folder,
    'work': Icons.work,
    'event': Icons.event,
    'celebration': Icons.celebration,
    'local_cafe': Icons.local_cafe,
    'fitness_center': Icons.fitness_center,
    'pets': Icons.pets,
    'child_care': Icons.child_care,
    'elderly': Icons.elderly,
    'support': Icons.support,
    'psychology': Icons.psychology,
    'volunteer_activism': Icons.volunteer_activism,
  };

  static List<String> get availableIcons => _iconMap.keys.toList();

  // Helper method to get icon data from icon name
  static IconData getIconFromName(String iconName) {
    return _iconMap[iconName] ?? Icons.category;
  }
}
