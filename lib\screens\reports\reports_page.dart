import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/invoice_service.dart';
import '../../services/order_service.dart';
import '../../services/customer_service.dart';
import 'sales_report_page.dart';
import 'orders_report_page.dart';
import 'customers_report_page.dart';
import 'payments_report_page.dart';
import 'compare_report_page.dart';
import 'store_report_page.dart';
import 'staff_report_page.dart';
import 'expenses_report_page.dart';

class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage> {
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final CustomerService _customerService = CustomerService(supabase: Supabase.instance.client);
  
  bool _isLoading = true;
  Map<String, dynamic> _quickStats = {};
  String _currencySymbol = '';

  // Formats large currency/number values into compact form (e.g., 125000 -> 125K)
  // Returns the compact string; use with threshold 100k
  String _compactDisplay(String value, {double threshold = 100000}) {
    // Extract non-digit prefix as potential currency symbol
    final symbol = value.replaceAll(RegExp(r'[0-9.,+-]'), '').trim();
    final numString = value.replaceAll(RegExp(r'[^0-9.-]'), '');
    final parsed = double.tryParse(numString.replaceAll(',', ''));
    if (parsed == null || parsed.abs() < threshold) {
      return value;
    }
    double n = parsed.abs();
    String suffix;
    double divisor;
    if (n >= 1e9) {
      suffix = 'B';
      divisor = 1e9;
    } else if (n >= 1e6) {
      suffix = 'M';
      divisor = 1e6;
    } else {
      suffix = 'K';
      divisor = 1e3;
    }
    final compact = (n / divisor).toStringAsFixed(n / divisor >= 100 ? 0 : 1);
    final sign = parsed < 0 ? '-' : '';
    final prefix = symbol.isNotEmpty ? symbol : '';
    return "$sign$prefix$compact$suffix";
  }

  void _showFullAmountDialog(String title, String fullValue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SelectableText(fullValue, style: const TextStyle(fontWeight: FontWeight.w700)),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Close')),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadQuickStats();
  }

  Future<void> _loadQuickStats() async {
    setState(() => _isLoading = true);
    
    try {
      // Load currency symbol
      _currencySymbol = await _orderService.getCurrencySymbol();
      
      // Get current month date range
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);
      
      // Load basic stats
      final orders = await _orderService.getUserOrders(Supabase.instance.client.auth.currentUser!.id);
      final thisMonthOrders = orders.where((o) => 
        o.createdAt.isAfter(startOfMonth) && o.createdAt.isBefore(endOfMonth.add(const Duration(days: 1)))
      ).toList();
      
      final customers = await _customerService.getCustomers();
      
      // Calculate revenue from paid invoices
      double totalRevenue = 0;
      double thisMonthRevenue = 0;
      int paidOrders = 0;
      int thisMonthPaidOrders = 0;
      
      for (final order in orders) {
        try {
          final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
          if (invoice.isPaid) {
            totalRevenue += invoice.totalAmount;
            paidOrders++;
            
            if (thisMonthOrders.contains(order)) {
              thisMonthRevenue += invoice.totalAmount;
              thisMonthPaidOrders++;
            }
          }
        } catch (e) {
          // Invoice might not exist for some orders
        }
      }
      
      setState(() {
        _quickStats = {
          'totalOrders': orders.length,
          'thisMonthOrders': thisMonthOrders.length,
          'totalRevenue': totalRevenue,
          'thisMonthRevenue': thisMonthRevenue,
          'totalCustomers': customers.length,
          'paidOrders': paidOrders,
          'thisMonthPaidOrders': thisMonthPaidOrders,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading stats: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Reports & Analytics',
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue[600]!,
                Colors.blue[700]!,
              ],
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadQuickStats,
            icon: const Icon(Icons.refresh_rounded),
            tooltip: 'Refresh Data',
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue[50]!.withValues(alpha: 0.3),
              Colors.grey[50]!,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Quick Stats Overview
                    _buildQuickStatsSection(),
                    const SizedBox(height: 32),
                    
                    // Report Categories Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.blue[100]!,
                                Colors.blue[50]!,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.analytics_rounded,
                            color: Colors.blue[700],
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Report Categories',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w800,
                            color: Colors.grey[900],
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    
                    _buildReportGrid(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildQuickStatsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.blue.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue[200]!,
                      Colors.blue[100]!,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.speed_rounded,
                  color: Colors.blue[800],
                  size: 22,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Quick Overview',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  color: Colors.grey[900],
                  letterSpacing: 0.3,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Stats Grid
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            // Much lower aspect ratio -> much taller tiles to avoid vertical overflow
            childAspectRatio: 1.2,
            children: [
              _buildStatCard(
                'Total Orders',
                '${_quickStats['totalOrders'] ?? 0}',
                Icons.receipt_long,
                Colors.blue,
              ),
              _buildStatCard(
                'This Month',
                '${_quickStats['thisMonthOrders'] ?? 0}',
                Icons.calendar_month,
                Colors.green,
              ),
              _buildStatCard(
                'Total Revenue',
                '$_currencySymbol${(_quickStats['totalRevenue'] ?? 0).toStringAsFixed(2)}',
                Icons.monetization_on,
                Colors.orange,
              ),
              _buildStatCard(
                'Month Revenue',
                '$_currencySymbol${(_quickStats['thisMonthRevenue'] ?? 0).toStringAsFixed(2)}',
                Icons.trending_up,
                Colors.purple,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    final display = _compactDisplay(value);
    final isTruncated = display != value;
    final card = Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color.withValues(alpha: 0.04),
          ],
        ),
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.10),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(fontSize: 12, color: Colors.grey[800], fontWeight: FontWeight.w700),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            display,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w800, color: color),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (isTruncated)
            Padding(
              padding: const EdgeInsets.only(top: 6),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.info_outline, size: 14, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text('Tap to view full', style: TextStyle(fontSize: 10, color: Colors.grey[600])),
                ],
              ),
            ),
        ],
      ),
    );

    return isTruncated
        ? InkWell(
            onTap: () => _showFullAmountDialog(title, value),
            borderRadius: BorderRadius.circular(14),
            child: card,
          )
        : card;
  }

  Widget _buildReportGrid() {
    final reports = [
      {
        'title': 'Sales & Revenue',
        'subtitle': 'Revenue trends, payment analysis',
        'icon': Icons.trending_up,
        'color': Colors.green,
        'page': const SalesReportPage(),
      },
      {
        'title': 'Orders Analytics',
        'subtitle': 'Order status, trends, performance',
        'icon': Icons.receipt_long,
        'color': Colors.blue,
        'page': const OrdersReportPage(),
      },
      {
        'title': 'Customer Insights',
        'subtitle': 'Customer behavior, top customers',
        'icon': Icons.people,
        'color': Colors.purple,
        'page': const CustomersReportPage(),
      },
      {
        'title': 'Payment Methods',
        'subtitle': 'Payment method breakdown',
        'icon': Icons.payment,
        'color': Colors.orange,
        'page': const PaymentsReportPage(),
      },
      {
        'title': 'Expense Reports',
        'subtitle': 'Expenses by category and store',
        'icon': Icons.receipt_long_outlined,
        'color': Colors.redAccent,
        'page': const ExpensesReportPage(),
      },
      {
        'title': 'Staff Reports',
        'subtitle': 'Sales by staff, compare staff',
        'icon': Icons.badge,
        'color': Colors.brown,
        'page': const StaffReportPage(),
      },
      {
        'title': 'Store Reports',
        'subtitle': 'Sales by store, compare stores',
        'icon': Icons.store,
        'color': Colors.indigo,
        'page': const StoreReportPage(),
      },
      {
        'title': 'Compare Reports',
        'subtitle': 'Week-over-week, month-over-month',
        'icon': Icons.compare_arrows,
        'color': Colors.teal,
        'page': const CompareReportPage(),
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.4,
      ),
      itemCount: reports.length,
      itemBuilder: (context, index) {
        final report = reports[index];
        return _buildReportCard(
          title: report['title'] as String,
          subtitle: report['subtitle'] as String,
          icon: report['icon'] as IconData,
          color: report['color'] as Color,
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => report['page'] as Widget,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildReportCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                color.withValues(alpha: 0.03),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.12),
                blurRadius: 20,
                offset: const Offset(0, 6),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: color.withValues(alpha: 0.15),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with Icon and Arrow
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            color.withValues(alpha: 0.2),
                            color.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 0.25),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Icon(
                        icon,
                        color: color,
                        size: 20,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.12),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios,
                        color: color,
                        size: 14,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Title
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey[900],
                      letterSpacing: 0.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Subtitle
                Expanded(
                  child: Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                      letterSpacing: 0.1,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}