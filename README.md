# 🧺 Laundry Pro

A comprehensive, modern laundry management system built with Flutter and Supabase. Designed for laundry businesses of all sizes to streamline operations, manage customers, track orders, and generate insights.

<div align="center">
  <img src="Assets/laundry_logo.png" alt="Laundry Pro Logo" width="100" height="100">
  
  [![Flutter](https://img.shields.io/badge/Flutter-3.8.1+-02569B?style=flat&logo=flutter&logoColor=white)](https://flutter.dev)
  [![Dart](https://img.shields.io/badge/Dart-3.0+-0175C2?style=flat&logo=dart&logoColor=white)](https://dart.dev)
  [![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=flat&logo=supabase&logoColor=white)](https://supabase.com)
  [![License](https://img.shields.io/badge/License-Private-red?style=flat)](./LICENSE)
</div>

## ✨ Features

### 🔐 Authentication & Security
- **Multi-factor Authentication** - Email/Password with OTP verification
- **Biometric Authentication** - Fingerprint and Face ID support
- **Secure Session Management** - Automatic logout and session refresh
- **Password Recovery** - Email-based password reset with secure tokens

### 👥 Customer Management
- **Comprehensive Customer Profiles** - Contact details, preferences, and history
- **Customer Registration** - Streamlined onboarding process
- **Order History Tracking** - Complete transaction history per customer
- **Customer Analytics** - Insights into customer behavior and preferences

### 📦 Order Management
- **Smart Order Processing** - Intelligent item selection and automated pricing
- **Order Tracking** - Real-time status updates throughout the laundry process
- **Order History** - Complete order lifecycle management
- **Bulk Order Processing** - Handle multiple orders efficiently

### 💰 Financial Management
- **Payment Processing** - Multiple payment methods support
- **Invoice Generation** - Professional PDF invoices with company branding
- **Payment Tracking** - Monitor outstanding payments and payment history
- **Revenue Analytics** - Track daily, weekly, and monthly earnings

### 📊 Inventory & Services
- **Garment Management** - Track different clothing types and their processing
- **Service Categories** - Washing, dry cleaning, ironing, and custom services
- **Pricing Management** - Flexible pricing models and service packages
- **Inventory Control** - Track supplies and equipment

### 📈 Reports & Analytics
- **Sales Reports** - Comprehensive revenue and sales analytics
- **Customer Reports** - Customer behavior and retention metrics
- **Staff Performance** - Track employee productivity and performance
- **Expense Tracking** - Monitor operational costs and expenses
- **Store Comparison** - Multi-location performance analysis

### 🏪 Multi-Store Support
- **Store Management** - Handle multiple laundry locations
- **Store-specific Analytics** - Individual store performance tracking
- **Cross-store Operations** - Transfer orders and manage resources
- **Centralized Control** - Unified management dashboard

### 👨‍💼 Staff Management
- **Staff Profiles** - Employee information and role management
- **Activity Tracking** - Monitor staff activities and performance
- **Permission Management** - Role-based access control
- **Staff Analytics** - Performance metrics and reporting

### 🎨 User Experience
- **Modern UI/UX** - Clean, intuitive interface with Material Design
- **Dark/Light Themes** - User preference-based theme switching
- **Responsive Design** - Optimized for tablets and phones
- **Offline Capabilities** - Core features work without internet

### 🌍 Localization & Settings
- **Multi-currency Support** - Handle different currencies and exchange rates
- **Country Selection** - Automatic country detection with manual override
- **Company Branding** - Customize app with company logo and details
- **Backup & Sync** - Cloud-based data synchronization

## 🚀 Getting Started

### Prerequisites

Before running this application, ensure you have:

- **Flutter SDK** (3.8.1 or higher) - [Install Flutter](https://docs.flutter.dev/get-started/install)
- **Dart SDK** (3.0 or higher) - Included with Flutter
- **Android Studio/VS Code** - IDE with Flutter plugins
- **Android/iOS Development Setup** - For mobile deployment
- **Supabase Account** - [Create account](https://supabase.com)

### 📱 Supported Platforms

- ✅ **Android** (API 21+)
- ✅ **iOS** (iOS 12+)
- ✅ **Web** (Modern browsers)
- ✅ **Windows** (Windows 10+)
- ✅ **macOS** (macOS 10.14+)
- ✅ **Linux** (Ubuntu 18.04+)

### 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/laundry_pro.git
   cd laundry_pro
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Environment Setup**
   
   Create a `.env` file in the root directory:
   ```env
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Database Setup**
   
   Run the SQL migrations in the `migration/` folder in your Supabase SQL editor:
   ```sql
   -- Execute in order:
   -- 01_store_tables.sql
   -- 02_orders_system.sql
   -- 03_customers.sql
   -- 04_staff.sql
   -- 05_payments_system.sql
   -- 06_expenses.sql
   -- 07_user_settings.sql
   ```

5. **Configure App Icons** (Optional)
   ```bash
   flutter pub run flutter_launcher_icons:main
   ```

6. **Run the application**
   ```bash
   flutter run
   ```

### 🔧 Build for Production

#### Android APK
```bash
flutter build apk --release
```

#### Android App Bundle
```bash
flutter build appbundle --release
```

#### iOS
```bash
flutter build ipa
```

#### Web
```bash
flutter build web --release
```

#### Desktop
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

## 📱 Screenshots

*Coming soon - Add screenshots of your app here*

## 🔗 Key Dependencies

| Package | Version | Purpose |
|---------|---------|---------|
| **supabase_flutter** | ^2.7.1 | Backend & Authentication |
| **provider** | ^6.1.2 | State Management |
| **local_auth** | ^2.3.0 | Biometric Authentication |
| **shared_preferences** | ^2.3.2 | Local Storage |
| **email_validator** | ^3.0.0 | Email Validation |
| **currency_picker** | ^2.0.21 | Currency Selection |
| **pdf** | ^3.10.7 | PDF Generation |
| **geolocator** | ^10.1.1 | Location Services |
| **lottie** | ^3.1.2 | Animations |

## 🌐 API Documentation

### Supabase Integration

The app uses Supabase for:
- **Authentication** - User management and security
- **Database** - PostgreSQL with real-time subscriptions
- **Storage** - File uploads and management
- **Real-time** - Live updates across devices

### Database Schema

Key tables:
- `auth.users` - User authentication
- `customers` - Customer information
- `orders` - Order management
- `payments` - Payment tracking
- `stores` - Multi-store support
- `staff` - Employee management
- `expenses` - Cost tracking

## 🔒 Security Features

- **Row Level Security (RLS)** - Database-level access control
- **JWT Authentication** - Secure token-based auth
- **Biometric Authentication** - Device-level security
- **Data Encryption** - Sensitive data protection
- **Session Management** - Automatic security timeouts

## 🚀 Performance Optimizations

- **Lazy Loading** - Efficient data loading
- **Const Constructors** - Widget optimization
- **Image Caching** - Faster image loading
- **Database Indexing** - Optimized queries
- **State Management** - Efficient rebuilds

## 🧪 Testing

Run tests with:
```bash
flutter test
```

For integration tests:
```bash
flutter drive --target=test_driver/app.dart
```

## 📝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Code Style

- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `flutter analyze` to check code quality
- Add documentation for public APIs
- Write tests for new features

## 🐛 Troubleshooting

### Common Issues

**Issue: setState() called after dispose()**
```dart
// Solution: Always check if widget is mounted
if (mounted) {
  setState(() {
    // Your state changes
  });
}
```

**Issue: Biometric authentication not working**
- Ensure device has biometric hardware
- Check app permissions in device settings
- Verify local_auth plugin configuration

**Issue: Database connection failed**
- Verify Supabase URL and keys in `.env`
- Check internet connection
- Ensure Supabase project is active

## 📄 License

This project is private and proprietary. All rights reserved.

## 👥 Team

- **Development Team** - Flutter & Dart specialists
- **Design Team** - UI/UX designers
- **Backend Team** - Supabase & PostgreSQL experts

## 📞 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📱 Phone: +****************
- 🌐 Website: https://laundrypro.com

## 🗺️ Roadmap

### Version 2.0
- [ ] AI-powered demand forecasting
- [ ] Mobile payment integration
- [ ] Advanced analytics dashboard
- [ ] Multi-language support

### Version 2.1
- [ ] Customer mobile app
- [ ] IoT device integration
- [ ] Advanced reporting
- [ ] API for third-party integrations

---

<div align="center">
  <p>Made with ❤️ by the Laundry Pro Team</p>
  <p>© 2024 Laundry Pro. All rights reserved.</p>
</div>#   l a u n d r y _ p r o 
 
 