import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/invoice_service.dart';
import '../../services/order_service.dart';

class SalesReportPage extends StatefulWidget {
  const SalesReportPage({super.key});

  @override
  State<SalesReportPage> createState() => _SalesReportPageState();
}

class _SalesReportPageState extends State<SalesReportPage> {
  final InvoiceService _invoiceService = InvoiceService(supabase: Supabase.instance.client);
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  
  bool _isLoading = true;
  String _currencySymbol = '';
  DateTimeRange? _selectedDateRange;
  
  // Data
  List<Map<String, dynamic>> _dailySales = [];
  List<Map<String, dynamic>> _paymentMethodBreakdown = [];
  Map<String, dynamic> _totalStats = {};

  @override
  void initState() {
    super.initState();
    // Default to current month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      _currencySymbol = await _orderService.getCurrencySymbol();
      
      // Get payment summary for date range
      final paymentSummary = await _invoiceService.getPaymentSummary(
        startDate: _selectedDateRange?.start,
        endDate: _selectedDateRange?.end,
      );
      
      // Get orders for the date range
      final orders = await _orderService.getUserOrders(Supabase.instance.client.auth.currentUser!.id);
      final filteredOrders = orders.where((order) {
        if (_selectedDateRange == null) return true;
        return order.createdAt.isAfter(_selectedDateRange!.start) &&
               order.createdAt.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
      
      // Calculate daily sales
      final Map<String, double> dailySalesMap = {};
      final Map<String, int> dailyOrdersMap = {};
      double totalRevenue = 0;
      int totalPaidOrders = 0;
      
      for (final order in filteredOrders) {
        try {
          final invoice = await _invoiceService.getInvoiceByOrderId(order.id);
          if (invoice.isPaid) {
            final dateKey = invoice.paidAt?.toString().substring(0, 10) ?? 
                           order.createdAt.toString().substring(0, 10);
            
            dailySalesMap[dateKey] = (dailySalesMap[dateKey] ?? 0) + invoice.totalAmount;
            dailyOrdersMap[dateKey] = (dailyOrdersMap[dateKey] ?? 0) + 1;
            totalRevenue += invoice.totalAmount;
            totalPaidOrders++;
          }
        } catch (e) {
          // Invoice might not exist
        }
      }
      
      // Convert to list and sort
      final dailySalesList = dailySalesMap.entries.map((entry) => {
        'date': entry.key,
        'revenue': entry.value,
        'orders': dailyOrdersMap[entry.key] ?? 0,
      }).toList();
      
      dailySalesList.sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));
      
      setState(() {
        _dailySales = dailySalesList;
        _paymentMethodBreakdown = paymentSummary;
        _totalStats = {
          'totalRevenue': totalRevenue,
          'totalOrders': filteredOrders.length,
          'paidOrders': totalPaidOrders,
          'averageOrderValue': totalPaidOrders > 0 ? totalRevenue / totalPaidOrders : 0,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading sales data: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDateRange,
    );
    
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sales & Revenue Report'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 4,
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'Select Date Range',
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range Display
                  _buildDateRangeCard(),
                  const SizedBox(height: 16),
                  
                  // Summary Stats
                  _buildSummaryStats(),
                  const SizedBox(height: 24),
                  
                  // Daily Sales Chart
                  _buildDailySalesSection(),
                  const SizedBox(height: 24),
                  
                  // Payment Method Breakdown
                  _buildPaymentMethodSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildDateRangeCard() {
    final startDate = _selectedDateRange?.start.toString().substring(0, 10) ?? 'N/A';
    final endDate = _selectedDateRange?.end.toString().substring(0, 10) ?? 'N/A';
    
    return Card(
      child: ListTile(
        leading: const Icon(Icons.date_range, color: Colors.green),
        title: const Text('Date Range'),
        subtitle: Text('$startDate to $endDate'),
        trailing: const Icon(Icons.edit),
        onTap: _selectDateRange,
      ),
    );
  }

  Widget _buildSummaryStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Summary Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Revenue',
                    '$_currencySymbol${(_totalStats['totalRevenue'] ?? 0).toStringAsFixed(2)}',
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Paid Orders',
                    '${_totalStats['paidOrders'] ?? 0}',
                    Icons.check_circle,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Orders',
                    '${_totalStats['totalOrders'] ?? 0}',
                    Icons.receipt_long,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Avg Order Value',
                    '$_currencySymbol${(_totalStats['averageOrderValue'] ?? 0).toStringAsFixed(2)}',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDailySalesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Daily Sales Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_dailySales.isEmpty)
              const Center(
                child: Text(
                  'No sales data for selected period',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _dailySales.length,
                itemBuilder: (context, index) {
                  final sale = _dailySales[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.green.withValues(alpha: 0.1),
                      child: Text(
                        '${sale['orders']}',
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(sale['date'] as String),
                    subtitle: Text('${sale['orders']} orders'),
                    trailing: Text(
                      '$_currencySymbol${(sale['revenue'] as double).toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Method Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_paymentMethodBreakdown.isEmpty)
              const Center(
                child: Text(
                  'No payment data available',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _paymentMethodBreakdown.length,
                itemBuilder: (context, index) {
                  final payment = _paymentMethodBreakdown[index];
                  final method = payment['method'] as String;
                  final count = payment['count'] as int;
                  final total = payment['total'] as double;
                  
                  // Get method display name and color
                  String displayName;
                  Color color;
                  IconData icon;
                  
                  switch (method) {
                    case 'cash':
                      displayName = 'Cash';
                      color = Colors.green;
                      icon = Icons.money;
                      break;
                    case 'mpesa_till':
                      displayName = 'M-Pesa Till';
                      color = Colors.blue;
                      icon = Icons.phone_android;
                      break;
                    case 'mpesa_paybill':
                      displayName = 'M-Pesa Paybill';
                      color = Colors.indigo;
                      icon = Icons.payment;
                      break;
                    case 'mpesa_send_money':
                      displayName = 'M-Pesa Send Money';
                      color = Colors.purple;
                      icon = Icons.send;
                      break;
                    default:
                      displayName = method;
                      color = Colors.grey;
                      icon = Icons.payment;
                  }
                  
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: color.withValues(alpha: 0.1),
                      child: Icon(icon, color: color, size: 20),
                    ),
                    title: Text(displayName),
                    subtitle: Text('$count transactions'),
                    trailing: Text(
                      '$_currencySymbol${total.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}