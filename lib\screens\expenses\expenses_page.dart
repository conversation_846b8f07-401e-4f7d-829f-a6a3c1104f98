import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/expense_model.dart';
import '../../services/expense_service.dart';
import 'expense_form_page.dart';
import 'expense_detail_page.dart';
import 'custom_category_manager_page.dart';

class ExpensesPage extends StatefulWidget {
  const ExpensesPage({super.key});

  @override
  State<ExpensesPage> createState() => _ExpensesPageState();
}

class _ExpensesPageState extends State<ExpensesPage> {
  final ExpenseService _service = ExpenseService(supabase: Supabase.instance.client);
  bool _loading = true;
  List<Expense> _items = [];
  ExpenseCategory? _selectedCategory;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() => _loading = true);
    try {
      final rows = await _service.listExpenses(
        startDate: _startDate,
        endDate: _endDate,
        categories: _selectedCategory != null ? [_selectedCategory!] : null,
        limit: 100,
      );
      setState(() {
        _items = rows;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading expenses: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expenses', style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          IconButton(
            onPressed: _showCategoryManager,
            icon: const Icon(Icons.category),
            tooltip: 'Built-in Categories',
          ),
          IconButton(
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const CustomCategoryManagerPage()),
            ),
            icon: const Icon(Icons.add_box),
            tooltip: 'Custom Categories',
          ),
          IconButton(onPressed: _load, icon: const Icon(Icons.refresh)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final created = await Navigator.of(context).push<Expense>(
            MaterialPageRoute(builder: (_) => const ExpenseFormPage()),
          );
          if (created != null) _load();
        },
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          _buildFilters(),
          Expanded(
            child: _loading
                ? const Center(child: CircularProgressIndicator())
                : _items.isEmpty
                    ? const Center(child: Text('No expenses found'))
                    : RefreshIndicator(
                        onRefresh: _load,
                        child: ListView.separated(
                          padding: const EdgeInsets.all(12),
                          itemBuilder: (_, i) {
                            final e = _items[i];
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getCategoryColor(e.category).withValues(alpha: 0.1),
                                child: Icon(
                                  _getCategoryIcon(e.category),
                                  color: _getCategoryColor(e.category),
                                ),
                              ),
                              title: Text(
                                '${e.category.displayName} — ${e.paymentMethod.displayName}',
                                style: const TextStyle(fontWeight: FontWeight.w600),
                              ),
                              subtitle: Text('${e.vendor ?? 'N/A'} • ${e.expenseDate.toIso8601String().split('T').first}'),
                              trailing: Text(
                                e.amount.toStringAsFixed(2),
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              onTap: () async {
                                await Navigator.of(context).push(
                                  MaterialPageRoute(builder: (_) => ExpenseDetailPage(expenseId: e.id)),
                                );
                                _load(); // Refresh after returning from detail
                              },
                            );
                          },
                          separatorBuilder: (_, __) => const Divider(height: 1),
                          itemCount: _items.length,
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<ExpenseCategory?>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem<ExpenseCategory?>(
                      value: null,
                      child: Text('All Categories'),
                    ),
                    ...ExpenseCategory.values.map((cat) => DropdownMenuItem(
                          value: cat,
                          child: Row(
                            children: [
                              Icon(_getCategoryIcon(cat), size: 18, color: _getCategoryColor(cat)),
                              const SizedBox(width: 8),
                              Text(cat.displayName),
                            ],
                          ),
                        )),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedCategory = value);
                    _load();
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.date_range),
                tooltip: 'Date Range',
                style: IconButton.styleFrom(
                  backgroundColor: (_startDate != null || _endDate != null) 
                      ? Colors.blue.withValues(alpha: 0.1) 
                      : null,
                ),
              ),
            ],
          ),
          if (_startDate != null || _endDate != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Text(
                    'Date Range: ${_startDate?.toIso8601String().split('T').first ?? 'Any'} - ${_endDate?.toIso8601String().split('T').first ?? 'Any'}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _startDate = null;
                        _endDate = null;
                      });
                      _load();
                    },
                    child: const Text('Clear'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showCategoryManager() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Expense Categories'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: ExpenseCategory.values.map((cat) => ListTile(
              leading: CircleAvatar(
                backgroundColor: _getCategoryColor(cat).withValues(alpha: 0.1),
                child: Icon(_getCategoryIcon(cat), color: _getCategoryColor(cat), size: 20),
              ),
              title: Text(cat.displayName),
              subtitle: Text('Database value: ${cat.dbValue}'),
              trailing: IconButton(
                icon: Icon(Icons.filter_list, color: _getCategoryColor(cat)),
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() => _selectedCategory = cat);
                  _load();
                },
              ),
            )).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _load();
    }
  }

  IconData _getCategoryIcon(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.rent:
        return Icons.home;
      case ExpenseCategory.utilities:
        return Icons.flash_on;
      case ExpenseCategory.salaries:
        return Icons.people;
      case ExpenseCategory.supplies:
        return Icons.inventory;
      case ExpenseCategory.transport:
        return Icons.directions_car;
      case ExpenseCategory.maintenance:
        return Icons.build;
      case ExpenseCategory.marketing:
        return Icons.campaign;
      case ExpenseCategory.refund:
        return Icons.money_off;
      case ExpenseCategory.misc:
        return Icons.more_horiz;
    }
  }

  Color _getCategoryColor(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.rent:
        return Colors.brown;
      case ExpenseCategory.utilities:
        return Colors.yellow[700]!;
      case ExpenseCategory.salaries:
        return Colors.green;
      case ExpenseCategory.supplies:
        return Colors.blue;
      case ExpenseCategory.transport:
        return Colors.orange;
      case ExpenseCategory.maintenance:
        return Colors.grey;
      case ExpenseCategory.marketing:
        return Colors.purple;
      case ExpenseCategory.refund:
        return Colors.red;
      case ExpenseCategory.misc:
        return Colors.teal;
    }
  }
}


