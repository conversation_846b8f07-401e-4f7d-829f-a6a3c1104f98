import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/custom_expense_category_model.dart';
import '../../services/custom_expense_category_service.dart';

class CustomCategoryManagerPage extends StatefulWidget {
  const CustomCategoryManagerPage({super.key});

  @override
  State<CustomCategoryManagerPage> createState() => _CustomCategoryManagerPageState();
}

class _CustomCategoryManagerPageState extends State<CustomCategoryManagerPage> {
  final CustomExpenseCategoryService _service = CustomExpenseCategoryService(supabase: Supabase.instance.client);
  bool _loading = true;
  List<CustomExpenseCategory> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() => _loading = true);
    try {
      final categories = await _service.listCustomCategories();
      setState(() {
        _categories = categories;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading categories: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Categories', style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          IconButton(onPressed: _loadCategories, icon: const Icon(Icons.refresh)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryForm(),
        child: const Icon(Icons.add),
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _categories.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.category_outlined, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text('No custom categories yet', style: TextStyle(color: Colors.grey[600])),
                      const SizedBox(height: 8),
                      const Text('Tap + to create your first category'),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadCategories,
                  child: ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: _categories.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                    itemBuilder: (_, i) {
                      final category = _categories[i];
                      return Card(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: category.color.withValues(alpha: 0.1),
                            child: Icon(category.icon, color: category.color),
                          ),
                          title: Text(category.name, style: const TextStyle(fontWeight: FontWeight.w600)),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (category.description != null) Text(category.description!),
                              Row(
                                children: [
                                  Icon(Icons.circle, size: 8, color: category.isActive ? Colors.green : Colors.red),
                                  const SizedBox(width: 4),
                                  Text(category.isActive ? 'Active' : 'Inactive', 
                                       style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                                ],
                              ),
                            ],
                          ),
                          trailing: PopupMenuButton<String>(
                            onSelected: (action) => _handleCategoryAction(action, category),
                            itemBuilder: (_) => [
                              const PopupMenuItem(value: 'edit', child: Text('Edit')),
                              PopupMenuItem(
                                value: category.isActive ? 'deactivate' : 'activate',
                                child: Text(category.isActive ? 'Deactivate' : 'Activate'),
                              ),
                              const PopupMenuItem(value: 'delete', child: Text('Delete')),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
    );
  }

  void _handleCategoryAction(String action, CustomExpenseCategory category) async {
    switch (action) {
      case 'edit':
        _showCategoryForm(category: category);
        break;
      case 'activate':
        await _toggleCategoryStatus(category, true);
        break;
      case 'deactivate':
        await _toggleCategoryStatus(category, false);
        break;
      case 'delete':
        await _deleteCategory(category);
        break;
    }
  }

  Future<void> _toggleCategoryStatus(CustomExpenseCategory category, bool isActive) async {
    try {
      await _service.updateCustomCategory(id: category.id, isActive: isActive);
      _loadCategories();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Category ${isActive ? 'activated' : 'deactivated'}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _deleteCategory(CustomExpenseCategory category) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?\n\nThis action cannot be undone.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _service.deleteCustomCategory(category.id);
        _loadCategories();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Category deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  void _showCategoryForm({CustomExpenseCategory? category}) {
    showDialog(
      context: context,
      builder: (_) => _CategoryFormDialog(
        category: category,
        onSaved: () {
          Navigator.of(context).pop();
          _loadCategories();
        },
      ),
    );
  }
}

class _CategoryFormDialog extends StatefulWidget {
  final CustomExpenseCategory? category;
  final VoidCallback onSaved;

  const _CategoryFormDialog({required this.onSaved, this.category});

  @override
  State<_CategoryFormDialog> createState() => _CategoryFormDialogState();
}

class _CategoryFormDialogState extends State<_CategoryFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final CustomExpenseCategoryService _service = CustomExpenseCategoryService(supabase: Supabase.instance.client);
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedIcon = 'category';
  String _selectedColor = '#2196F3';
  bool _isActive = true;
  bool _saving = false;

  final List<String> _predefinedColors = [
    '#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0',
    '#607D8B', '#795548', '#FF5722', '#3F51B5', '#009688',
    '#FFEB3B', '#E91E63', '#00BCD4', '#8BC34A', '#FFC107',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      final cat = widget.category!;
      _nameController.text = cat.name;
      _descriptionController.text = cat.description ?? '';
      _selectedIcon = cat.iconName ?? 'category';
      _selectedColor = cat.colorHex ?? '#2196F3';
      _isActive = cat.isActive;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              Text(
                widget.category == null ? 'Create Category' : 'Edit Category',
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              
              // Name field
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Category Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) => value?.trim().isEmpty == true ? 'Name is required' : null,
              ),
              const SizedBox(height: 16),
              
              // Description field
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              
              // Icon selection
              const Text('Icon:', style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: CustomExpenseCategory.availableIcons.length,
                  itemBuilder: (_, i) {
                    final iconName = CustomExpenseCategory.availableIcons[i];
                    final icon = CustomExpenseCategory.getIconFromName(iconName);
                    final isSelected = _selectedIcon == iconName;
                    
                    return GestureDetector(
                      onTap: () => setState(() => _selectedIcon = iconName),
                      child: Container(
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isSelected ? Color(int.parse(_selectedColor.replaceFirst('#', '0xFF'))) : Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border: isSelected ? Border.all(color: Colors.blue, width: 2) : null,
                        ),
                        child: Icon(icon, color: isSelected ? Colors.white : Colors.grey[700]),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              
              // Color selection
              const Text('Color:', style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _predefinedColors.map((color) {
                  final isSelected = _selectedColor == color;
                  return GestureDetector(
                    onTap: () => setState(() => _selectedColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                        shape: BoxShape.circle,
                        border: isSelected ? Border.all(color: Colors.black, width: 3) : null,
                      ),
                      child: isSelected ? const Icon(Icons.check, color: Colors.white) : null,
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
              
              // Active toggle
              if (widget.category != null)
                SwitchListTile(
                  contentPadding: EdgeInsets.zero,
                  title: const Text('Active'),
                  value: _isActive,
                  onChanged: (value) => setState(() => _isActive = value),
                ),
              
              const SizedBox(height: 24),
              
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _saving ? null : _saveCategory,
                      child: Text(_saving ? 'Saving...' : 'Save'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _saving = true);
    try {
      if (widget.category == null) {
        // Create new category
        await _service.createCustomCategory(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
          iconName: _selectedIcon,
          colorHex: _selectedColor,
          isActive: _isActive,
        );
      } else {
        // Update existing category
        await _service.updateCustomCategory(
          id: widget.category!.id,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
          iconName: _selectedIcon,
          colorHex: _selectedColor,
          isActive: _isActive,
        );
      }
      
      widget.onSaved();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) setState(() => _saving = false);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
