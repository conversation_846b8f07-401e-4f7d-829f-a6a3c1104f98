import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/store_service.dart';
import '../../services/currency_service.dart' as currency_service;
import 'store_detail_report_page.dart';

class StoreReportPage extends StatefulWidget {
  const StoreReportPage({super.key});

  @override
  State<StoreReportPage> createState() => _StoreReportPageState();
}

class _StoreReportPageState extends State<StoreReportPage> {
  final _supabase = Supabase.instance.client;
  final StoreService _storeService = StoreService();
  final currency_service.AppCurrencyService _currency = currency_service.AppCurrencyService();

  bool _isLoading = true;
  String _currencySymbol = '';

  static const String _allStoresKey = '__ALL__';

  // Range controls
  String _rangePreset = 'This Month';
  DateTime _start = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime _end = DateTime.now();

  // Data
  List<_StoreTotal> _storeTotals = [];
  double _grandTotal = 0.0;
  String? _compareA; // storeId
  String? _compareB; // storeId

  // Stores cache
  Map<String, String> _storeIdToName = {};

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    setState(() => _isLoading = true);
    _currencySymbol = await _currency.getCurrencySymbol();
    await _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      // Load stores
      final stores = await _storeService.getStores();
      _storeIdToName = {for (final s in stores) s.id: s.name};
      // Add synthetic "All Stores" option for comparisons and filters
      _storeIdToName = {_allStoresKey: 'All Stores', ..._storeIdToName};

      // Try join payments -> orders to get store_id
      List<dynamic> payRows = [];
      try {
        var query = _supabase
            .from('payments')
            .select('amount, created_at, orders!inner(id, store_id)');
        query = query.gte('created_at', _start.toIso8601String());
        query = query.lte('created_at', _end.toIso8601String());
        payRows = await query;
      } catch (_) {
        // Fallback: fetch payments then resolve orders
        var q = _supabase.from('payments').select('amount, created_at, order_id');
        q = q.gte('created_at', _start.toIso8601String());
        q = q.lte('created_at', _end.toIso8601String());
        final payments = await q as List<dynamic>;
        final orderIds = payments.map((e) => e['order_id'] as String).toSet().toList();
        final orders = orderIds.isEmpty
            ? <dynamic>[]
            : await _supabase.from('orders').select('id, store_id').inFilter('id', orderIds);
        final idToStore = {for (final o in orders) o['id']: o['store_id']};
        payRows = payments.map((p) => {
              'amount': p['amount'],
              'created_at': p['created_at'],
              'orders': {'id': p['order_id'], 'store_id': idToStore[p['order_id']]}
            }).toList();
      }

      final Map<String, double> totals = {};
      double grand = 0.0;
      for (final row in payRows) {
        final amount = (row['amount'] as num).toDouble();
        final storeId = (row['orders']?['store_id']) as String?;
        if (storeId == null) continue;
        grand += amount;
        totals.update(storeId, (v) => v + amount, ifAbsent: () => amount);
      }

      final List<_StoreTotal> computed = totals.entries
          .map((e) => _StoreTotal(
                storeId: e.key,
                storeName: _storeIdToName[e.key] ?? 'Store',
                total: e.value,
              ))
          .toList()
        ..sort((a, b) => b.total.compareTo(a.total));

      if (!mounted) return;
      setState(() {
        _storeTotals = computed;
        _grandTotal = grand;
        _isLoading = false;
        _compareA ??= _allStoresKey;
        _compareB ??= _storeTotals.isNotEmpty ? _storeTotals.first.storeId : _allStoresKey;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load store report: $e'), backgroundColor: Colors.red),
      );
    }
  }

  void _applyPreset(String preset) {
    final now = DateTime.now();
    if (preset == 'This Month') {
      _start = DateTime(now.year, now.month, 1);
      _end = now;
    } else if (preset == 'Last Month') {
      final lastMonth = DateTime(now.year, now.month - 1, 1);
      _start = lastMonth;
      _end = DateTime(lastMonth.year, lastMonth.month + 1, 0, 23, 59, 59, 999);
    } else if (preset == 'This Year') {
      _start = DateTime(now.year, 1, 1);
      _end = now;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Store Reports'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _loadData, icon: const Icon(Icons.refresh)),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Range controls
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _rangePreset,
                        decoration: const InputDecoration(labelText: 'Range', border: OutlineInputBorder()),
                        items: const [
                          DropdownMenuItem(value: 'This Month', child: Text('This Month')),
                          DropdownMenuItem(value: 'Last Month', child: Text('Last Month')),
                          DropdownMenuItem(value: 'This Year', child: Text('This Year')),
                          DropdownMenuItem(value: 'Custom', child: Text('Custom')),
                        ],
                        onChanged: (v) async {
                          if (v == null) return;
                          setState(() => _rangePreset = v);
                          if (v != 'Custom') {
                            _applyPreset(v);
                            await _loadData();
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (_rangePreset == 'Custom')
                      Expanded(
                        child: _DateRangePicker(
                          start: _start,
                          end: _end,
                          onChanged: (s, e) async {
                            setState(() {
                              _start = s;
                              _end = e;
                            });
                            await _loadData();
                          },
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                // Compare controls
                Row(
                  children: [
                    Expanded(
                      child: _StoreDropdown(
                        label: 'Compare A',
                        value: _compareA,
                        options: _storeIdToName,
                        onChanged: (v) => setState(() => _compareA = v),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _StoreDropdown(
                        label: 'Compare B',
                        value: _compareB,
                        options: _storeIdToName,
                        onChanged: (v) => setState(() => _compareB = v),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    children: [
                      _buildTotalsCard(),
                      const SizedBox(height: 12),
                      _buildCompareCard(),
                      const SizedBox(height: 12),
                      _buildStoreList(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsCard() {
    final formattedGrand = _currencySymbol.isEmpty
        ? _grandTotal.toStringAsFixed(2)
        : '$_currencySymbol${_grandTotal.toStringAsFixed(2)}';
    return Card(
      elevation: 2,
      child: ListTile(
        leading: const Icon(Icons.summarize, color: Colors.blue),
        title: const Text('Total Sales (Selected Range)', style: TextStyle(fontWeight: FontWeight.w700)),
        subtitle: Text('${_storeTotals.length} stores'),
        trailing: Text(formattedGrand, style: const TextStyle(fontWeight: FontWeight.w800)),
      ),
    );
  }

  Widget _buildCompareCard() {
    final a = _resolveSelection(_compareA);
    final b = _resolveSelection(_compareB);
    final diff = b.total - a.total;
    final up = diff >= 0;
    final diffText = _currencySymbol.isEmpty
        ? diff.abs().toStringAsFixed(2)
        : '$_currencySymbol${diff.abs().toStringAsFixed(2)}';
    final percent = a.total == 0 ? (b.total > 0 ? 100.0 : 0.0) : (diff / a.total) * 100.0;
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Compare Stores', style: TextStyle(fontWeight: FontWeight.w800)),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _compareTile(
                    a,
                    onView: a.storeId == _allStoresKey
                        ? null
                        : () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => StoreDetailReportPage(
                                  storeId: a.storeId,
                                  storeName: a.storeName,
                                  initialStart: _start,
                                  initialEnd: _end,
                                ),
                              ),
                            );
                          },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _compareTile(
                    b,
                    onView: b.storeId == _allStoresKey
                        ? null
                        : () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => StoreDetailReportPage(
                                  storeId: b.storeId,
                                  storeName: b.storeName,
                                  initialStart: _start,
                                  initialEnd: _end,
                                ),
                              ),
                            );
                          },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (up ? Colors.green : Colors.redAccent).withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(up ? Icons.arrow_upward : Icons.arrow_downward, size: 14, color: up ? Colors.green : Colors.redAccent),
                      const SizedBox(width: 4),
                      Text('${up ? '+' : '-'}${percent.abs().toStringAsFixed(1)}%', style: TextStyle(color: up ? Colors.green : Colors.redAccent, fontWeight: FontWeight.w700)),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Text('${up ? '+' : '-'}$diffText', style: const TextStyle(fontWeight: FontWeight.w700)),
              ],
            )
          ],
        ),
      ),
    );
  }

  _StoreTotal _resolveSelection(String? id) {
    if (id == null) return _StoreTotal.empty();
    if (id == _allStoresKey) {
      return _StoreTotal(storeId: _allStoresKey, storeName: 'All Stores', total: _grandTotal);
    }
    return _storeTotals.firstWhere((e) => e.storeId == id, orElse: () => _StoreTotal(storeId: id, storeName: _storeIdToName[id] ?? '-', total: 0.0));
  }

  Widget _compareTile(_StoreTotal t, {VoidCallback? onView}) {
    final totalText = _currencySymbol.isEmpty ? t.total.toStringAsFixed(2) : '$_currencySymbol${t.total.toStringAsFixed(2)}';
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(t.storeName, maxLines: 1, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 6),
          Text(totalText, style: const TextStyle(fontWeight: FontWeight.w800, color: Colors.blue)),
          if (onView != null) ...[
            const SizedBox(height: 6),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton.icon(
                onPressed: onView,
                icon: const Icon(Icons.open_in_new, size: 16),
                label: const Text('View store detail'),
                style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4)),
              ),
            ),
          ]
        ],
      ),
    );
  }

  Widget _buildStoreList() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Sales by Store', style: TextStyle(fontWeight: FontWeight.w800)),
            const SizedBox(height: 8),
            for (final t in _storeTotals)
              Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => StoreDetailReportPage(
                              storeId: t.storeId,
                              storeName: t.storeName,
                              initialStart: _start,
                              initialEnd: _end,
                            ),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        child: Row(
                          children: [
                            Expanded(child: Text(t.storeName)),
                            Text(
                              _currencySymbol.isEmpty
                                  ? t.total.toStringAsFixed(2)
                                  : '$_currencySymbol${t.total.toStringAsFixed(2)}',
                              style: const TextStyle(fontWeight: FontWeight.w700),
                            ),
                            const SizedBox(width: 8),
                            _PercentBar(value: _grandTotal == 0 ? 0 : t.total / _grandTotal),
                            const SizedBox(width: 8),
                            const Icon(Icons.chevron_right, size: 18, color: Colors.grey),
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_) => StoreDetailReportPage(
                                storeId: t.storeId,
                                storeName: t.storeName,
                                initialStart: _start,
                                initialEnd: _end,
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.open_in_new, size: 16),
                        label: const Text('View store details'),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          minimumSize: Size.zero,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _StoreTotal {
  final String storeId;
  final String storeName;
  final double total;
  _StoreTotal({required this.storeId, required this.storeName, required this.total});
  factory _StoreTotal.empty() => _StoreTotal(storeId: '', storeName: '-', total: 0.0);
}

class _PercentBar extends StatelessWidget {
  final double value; // 0..1
  const _PercentBar({required this.value});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 90,
      height: 8,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: LinearProgressIndicator(
          value: value.clamp(0.0, 1.0),
          backgroundColor: Colors.grey.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade400),
        ),
      ),
    );
  }
}

class _StoreDropdown extends StatelessWidget {
  final String label;
  final String? value;
  final Map<String, String> options;
  final ValueChanged<String?> onChanged;
  const _StoreDropdown({
    required this.label,
    required this.value,
    required this.options,
    required this.onChanged,
  });
  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(labelText: label, border: const OutlineInputBorder()),
      items: options.entries
          .map((e) => DropdownMenuItem<String>(value: e.key, child: Text(e.value)))
          .toList(),
      onChanged: onChanged,
    );
  }
}

class _DateRangePicker extends StatelessWidget {
  final DateTime start;
  final DateTime end;
  final void Function(DateTime, DateTime) onChanged;
  const _DateRangePicker({required this.start, required this.end, required this.onChanged});

  Future<void> _pickStart(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: start,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      onChanged(DateTime(picked.year, picked.month, picked.day), end);
    }
  }

  Future<void> _pickEnd(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: end,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      onChanged(start, DateTime(picked.year, picked.month, picked.day, 23, 59, 59, 999));
    }
  }

  @override
  Widget build(BuildContext context) {
    String two(int v) => v.toString().padLeft(2, '0');
    String fmt(DateTime d) => '${two(d.day)}/${two(d.month)}/${d.year}';
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _pickStart(context),
            icon: const Icon(Icons.date_range),
            label: Text('Start: ${fmt(start)}'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _pickEnd(context),
            icon: const Icon(Icons.event_available),
            label: Text('End: ${fmt(end)}'),
          ),
        ),
      ],
    );
  }
}


