import 'package:supabase_flutter/supabase_flutter.dart';

class UserSettings {
  final String? companyName;
  final String? currencyCode;

  const UserSettings({this.companyName, this.currencyCode});

  bool get isEmpty => (companyName == null || companyName!.trim().isEmpty) &&
      (currencyCode == null || currencyCode!.trim().isEmpty);
}

class SettingsService {
  final SupabaseClient supabase;
  SettingsService({required this.supabase});

  String? get _userId => supabase.auth.currentUser?.id;

  Future<UserSettings?> getSettings() async {
    final uid = _userId;
    if (uid == null) return null;
    final List<dynamic> rows = await supabase
        .from('user_settings')
        .select()
        .eq('user_id', uid)
        .limit(1);
    if (rows.isNotEmpty) {
      final Map<String, dynamic> row = rows.first;
      return UserSettings(
        companyName: row['company_name'] as String?,
        currencyCode: row['currency_code'] as String?,
      );
    }
    return const UserSettings();
  }

  Future<void> upsertSettings({String? companyName, String? currencyCode}) async {
    final uid = _userId;
    if (uid == null) return;
    final payload = <String, dynamic>{
      'user_id': uid,
    };
    if (companyName != null) payload['company_name'] = companyName.trim();
    if (currencyCode != null) payload['currency_code'] = currencyCode.trim();
    await supabase.from('user_settings').upsert(payload, onConflict: 'user_id');
  }
}


