import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../services/staff_service.dart';
import '../../models/staff_profile_model.dart';
import 'staff_detail_page.dart';

class StaffListPage extends StatefulWidget {
  const StaffListPage({super.key});

  @override
  State<StaffListPage> createState() => _StaffListPageState();
}

class _StaffListPageState extends State<StaffListPage> {
  final StaffService _service = StaffService(supabase: Supabase.instance.client);

  String _search = '';
  String _status = 'all'; // all | active | inactive
  bool? _emailEnabled;    // null | true | false
  String _sortBy = 'staff_id';
  bool _ascending = true;
  late Future<List<StaffProfile>> _future;

  @override
  void initState() {
    super.initState();
    _future = _service.listStaff(status: _status, emailEnabled: _emailEnabled, sortBy: _sortBy, ascending: _ascending);
  }

  void _reload() {
    setState(() {
      _future = _service.listStaff(
        status: _status,
        emailEnabled: _emailEnabled,
        search: _search,
        sortBy: _sortBy,
        ascending: _ascending,
      );
    });
  }

  void _showAddStaffDialog() {
    showDialog(
      context: context,
      builder: (context) => AddStaffDialog(
        service: _service,
        onStaffAdded: () {
          _reload();
        },
      ),
    );
  }

  Future<void> _showDeleteConfirmation(StaffProfile staff) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Staff Member'),
        content: Text('Are you sure you want to delete ${staff.fullName}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _service.deleteStaff(staff.id);
        _reload();
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('${staff.fullName} has been deleted')),
          );
        }
      } catch (e) {
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('Error deleting staff: $e')),
          );
        }
      }
    }
  }

  Future<void> _toggleStaffActive(StaffProfile staff) async {
    final action = staff.isActive ? 'deactivate' : 'activate';
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${action[0].toUpperCase()}${action.substring(1)} Staff Member'),
        content: Text('Are you sure you want to $action ${staff.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: staff.isActive ? Colors.orange : Colors.green
            ),
            child: Text(action[0].toUpperCase() + action.substring(1)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _service.setActive(staff.id, !staff.isActive);
        _reload();
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('${staff.fullName} has been ${action}d')),
          );
        }
      } catch (e) {
        if (mounted) {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text('Error ${action}ing staff: $e')),
          );
        }
      }
    }
  }

  void _showStaffActions(StaffProfile staff) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              staff.fullName,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('View Details'),
              onTap: () async {
                Navigator.pop(context);
                await Navigator.push(
                  context, 
                  MaterialPageRoute(builder: (_) => StaffDetailPage(staffId: staff.id))
                );
                _reload();
              },
            ),
            // Staff management actions available to all authenticated users
            ListTile(
              leading: Icon(staff.isActive ? Icons.pause_circle : Icons.play_circle),
              title: Text(staff.isActive ? 'Deactivate' : 'Activate'),
              onTap: () {
                Navigator.pop(context);
                _toggleStaffActive(staff);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(staff);
              },
            ),
          ],
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Staff Management'),
        actions: [
          IconButton(onPressed: _reload, icon: const Icon(Icons.refresh)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddStaffDialog,
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // Search bar
                TextField(
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.search), 
                    hintText: 'Search staff'
                  ),
                  onChanged: (v) { _search = v; _reload(); },
                ),
                const SizedBox(height: 8),
                // Filters/Sort row
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // Status filter
                      DropdownButton<String>(
                        value: _status,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('All Staff')),
                          DropdownMenuItem(value: 'active', child: Text('Active')),
                          DropdownMenuItem(value: 'inactive', child: Text('Inactive')),
                        ],
                        onChanged: (v) { if (v != null) { setState(() { _status = v; }); _reload(); }},
                      ),
                      const SizedBox(width: 8),
                      // Email enabled filter
                      DropdownButton<bool?>(
                        value: _emailEnabled,
                        hint: const Text('Email: All'),
                        items: const [
                          DropdownMenuItem<bool?>(value: null, child: Text('Email: All')),
                          DropdownMenuItem<bool?>(value: true, child: Text('Email: Enabled')),
                          DropdownMenuItem<bool?>(value: false, child: Text('Email: Disabled')),
                        ],
                        onChanged: (v) { setState(() { _emailEnabled = v; }); _reload(); },
                      ),
                      const SizedBox(width: 8),
                      // Sort by
                      DropdownButton<String>(
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'staff_id', child: Text('Sort: ID')),
                          DropdownMenuItem(value: 'full_name', child: Text('Sort: Name')),
                          DropdownMenuItem(value: 'created_at', child: Text('Sort: Created')),
                        ],
                        onChanged: (v) { if (v != null) { setState(() { _sortBy = v; }); _reload(); }},
                      ),
                      IconButton(
                        tooltip: _ascending ? 'Ascending' : 'Descending',
                        onPressed: () { setState(() { _ascending = !_ascending; }); _reload(); },
                        icon: Icon(_ascending ? Icons.arrow_upward : Icons.arrow_downward),
                      ),
                      IconButton(
                        tooltip: 'Refresh',
                        onPressed: _reload,
                        icon: const Icon(Icons.refresh),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<StaffProfile>>(
              future: _future,
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snap.hasError) {
                  return Center(child: Text('Error: ${snap.error}'));
                }
                final list = snap.data ?? [];
                if (list.isEmpty) return const Center(child: Text('No staff found'));
                
                return ListView.separated(
                  itemCount: list.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, i) {
                    final staff = list[i];
                    final subtitle = [
                      'ID: ${staff.staffId}',
                      staff.email, 
                      staff.phone,
                      staff.emailEnabled ? 'Email Enabled' : 'Email Disabled'
                    ].where((e) => (e ?? '').isNotEmpty).join(' • ');
                    
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: staff.isActive ? Colors.green : Colors.grey,
                        child: Text(
                          staff.fullName.isNotEmpty ? staff.fullName[0].toUpperCase() : '?',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(staff.fullName),
                      subtitle: Text(subtitle),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (staff.isActive) 
                            const Icon(Icons.check_circle, color: Colors.green, size: 20)
                          else 
                            const Icon(Icons.pause_circle, color: Colors.grey, size: 20),
                          const SizedBox(width: 8),
                          IconButton(
                            icon: const Icon(Icons.more_vert),
                            onPressed: () => _showStaffActions(staff),
                          ),
                        ],
                      ),
                      onTap: () async {
                        await Navigator.push(
                          context, 
                          MaterialPageRoute(builder: (_) => StaffDetailPage(staffId: staff.id))
                        );
                        _reload();
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class AddStaffDialog extends StatefulWidget {
  final StaffService service;
  final VoidCallback onStaffAdded;

  const AddStaffDialog({
    super.key,
    required this.service,
    required this.onStaffAdded,
  });

  @override
  State<AddStaffDialog> createState() => _AddStaffDialogState();
}

class _AddStaffDialogState extends State<AddStaffDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _emailEnabled = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() { _isLoading = true; });

    try {
      // Create new staff account
      final result = await widget.service.createStaffWithEmail(
        email: _emailEnabled ? _emailController.text.trim() : null,
        fullName: _fullNameController.text.trim(),
        phone: _phoneController.text.trim().isNotEmpty 
          ? _phoneController.text.trim() 
          : null,
        password: _passwordController.text.trim().isNotEmpty 
          ? _passwordController.text.trim() 
          : null,
        emailEnabled: _emailEnabled,
      );

      widget.onStaffAdded();
      if (mounted) {
        Navigator.of(context).pop();
        if (_emailEnabled) {
          _showPasswordDialog(result['temp_password'], _emailController.text.trim());
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Staff member created successfully')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('Error adding staff: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  void _showPasswordDialog(String tempPassword, String email) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Staff Member Created'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Staff member has been created successfully!'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Login Credentials:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  SelectableText('Email: $email'),
                  SelectableText('Temporary Password: $tempPassword'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.security, color: Colors.orange, size: 16),
                      SizedBox(width: 8),
                      Text('Security Notice:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange)),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    '• Account created and ready for use\n'
                    '• Staff will need to verify email before first login\n'
                    '• Share credentials through secure channels only\n'
                    '• Staff should change password after first login',
                    style: TextStyle(fontSize: 12, color: Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton.icon(
            onPressed: () async {
              final subject = Uri.encodeComponent('Your LaundryPro Staff Account');
              final body = Uri.encodeComponent(
                'Hello,\n\n'
                'Your staff account has been created.\n\n'
                'Login Credentials:\n'
                'Email: $email\n'
                'Temporary Password: $tempPassword\n\n'
                'Security Notice:\n'
                '• Account created and ready for use\n'
                '• You may need to verify your email before first login\n'
                '• Please change your password after first login\n\n'
                'Thanks,\nLaundryPro Team'
              );
              final mailto = Uri.parse('mailto:$email?subject=$subject&body=$body');
              try {
                // ignore: deprecated_member_use
                await launchUrl(mailto, mode: LaunchMode.externalApplication);
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Could not open email app: $e')),
                  );
                }
              }
            },
            icon: const Icon(Icons.email),
            label: const Text('Send via Email'),
          ),
          TextButton(
            onPressed: () {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('Staff member created successfully')),
              );
            },
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Staff Member'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Email toggle first
              SwitchListTile(
                title: const Text('Email Enabled'),
                subtitle: const Text('Allow this staff member to receive emails'),
                value: _emailEnabled,
                onChanged: (value) {
                  setState(() {
                    _emailEnabled = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              
              // Email field (only show if email is enabled)
              if (_emailEnabled) ...[
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email Address *',
                    hintText: 'Enter staff email address',
                    prefixIcon: Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (_emailEnabled) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Email is required when email is enabled';
                      }
                      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                        return 'Please enter a valid email address';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Phone Number (Optional)',
                    hintText: 'Enter phone number',
                    prefixIcon: Icon(Icons.phone),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(
                    labelText: 'Password (Optional)',
                    hintText: 'Leave empty for auto-generated password',
                    prefixIcon: Icon(Icons.lock),
                  ),
                  obscureText: true,
                ),
              ],
              
              const SizedBox(height: 16),
              TextFormField(
                controller: _fullNameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name *',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Full name is required';
                  }
                  return null;
                },
              ),

              
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue, size: 20),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Staff Creation Info',
                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• New staff account will be created with immediate activation\n'
                      '• Password will be generated if not provided\n'
                      '• Staff will receive login credentials after creation\n'
                      '• Email verification may be required for first login',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _isLoading ? null : _submitForm,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create Staff'),
        ),
      ],
    );
  }
}