import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_simple_country_picker/flutter_simple_country_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'services/auth_service.dart';
import 'services/theme_service.dart';
import 'providers/theme_provider.dart';
import 'screens/auth/login_page.dart';
import 'screens/dashboard/dashboard_page.dart';
import 'screens/onboarding/onboarding_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");
    
    // Get environment variables
    final supabaseUrl = dotenv.env['SUPABASE_URL'];
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'];
    
    // Debug print to check if variables are loaded
    debugPrint('Supabase URL: $supabaseUrl');
    debugPrint('Supabase Anon Key: ${supabaseAnonKey?.substring(0, 20)}...');
    
    // Validate environment variables
    if (supabaseUrl == null || supabaseUrl.isEmpty) {
      throw Exception('SUPABASE_URL not found in .env file');
    }
    if (supabaseAnonKey == null || supabaseAnonKey.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY not found in .env file');
    }
    
    // Initialize Supabase
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );
    
    debugPrint('Supabase initialized successfully');
  } catch (e) {
    debugPrint('Error during initialization: $e');
    // You might want to show an error screen here
  }
  
  runApp(
    ChangeNotifierProvider(
      create: (context) => ThemeProvider(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Initialize theme provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ThemeProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Laundry Pro',
          theme: ThemeService.lightTheme,
          darkTheme: ThemeService.darkTheme,
          themeMode: themeProvider.themeMode,
          home: const AuthWrapper(),
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
            CountriesLocalization.delegate,
          ],
          supportedLocales: const [
            Locale('en', 'US'),
          ],
        );
      },
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late AuthService authService;
  bool _isLoading = true;
  bool _showOnboarding = false;

  @override
  void initState() {
    super.initState();
    authService = AuthService(supabase: Supabase.instance.client);
    _checkOnboardingThenInit();
  }

  Future<void> _checkFirstUserAdminPromotion() async {
    // Admin system removed - any authenticated user can manage staff
  }

  Future<void> _initializeAuth() async {
    // Add a small delay for splash effect
    await Future.delayed(const Duration(seconds: 1));

    try {
      // Check if user has a valid session
      if (authService.isSignedIn && authService.hasValidSession) {
        // Check for first user admin promotion
        await _checkFirstUserAdminPromotion();
        // User is logged in with valid session
        setState(() => _isLoading = false);
        return;
      }

      // Check for saved credentials (Remember me)
      final savedCredentials = await authService.getSavedCredentials();

      if (savedCredentials != null) {
        // Try to auto-login with saved credentials
        try {
          await authService.signIn(
            savedCredentials['email']!,
            savedCredentials['password']!,
          );
          // Check for first user admin promotion after successful login
          await _checkFirstUserAdminPromotion();
        } catch (e) {
          // Auto-login failed, clear saved credentials
          await authService.clearSavedCredentials();
        }
      }
    } catch (e) {
      // Handle any errors during initialization
      debugPrint('Auth initialization error: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _checkOnboardingThenInit() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final seen = prefs.getBool('onboarding_completed') ?? false;
      if (!seen) {
        if (!mounted) return;
        setState(() {
          _showOnboarding = true;
          _isLoading = false;
        });
        return;
      }
    } catch (_) {}
    _initializeAuth();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SplashScreen();
    }

    if (_showOnboarding) {
      return OnboardingFlow(
        onFinished: () async {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('onboarding_completed', true);
          if (!mounted) return;
          setState(() {
            _showOnboarding = false;
            _isLoading = true;
          });
          await _initializeAuth();
        },
      );
    }

    // Listen to auth state changes
    return StreamBuilder<AuthState>(
      stream: authService.authStateChanges,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SplashScreen();
        }

        final session = snapshot.data?.session;

        if (session != null) {
          return FutureBuilder<void>(
            future: _checkFirstUserAdminPromotion(),
            builder: (context, snapshot) {
              // Always show dashboard, admin promotion happens in background
              return const DashboardPage();
            },
          );
        } else {
          return const LoginPage();
        }
      },
    );
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Colors.blue,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_laundry_service,
              size: 100,
              color: Colors.white,
            ),
            SizedBox(height: 20),
            Text(
              'Laundry Pro',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 20),
            CircularProgressIndicator(
              color: Colors.white,
            ),
          ],
        ),
      ),
    );
  }
}
