import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/expense_model.dart';
import '../models/payment_model.dart';

class ExpenseService {
  final SupabaseClient _supabase;

  ExpenseService({SupabaseClient? supabase})
      : _supabase = supabase ?? Supabase.instance.client;

  // List expenses with optional filters
  Future<List<Expense>> listExpenses({
    DateTime? startDate,
    DateTime? endDate,
    List<ExpenseCategory>? categories,
    List<PaymentMethod>? methods,
    List<String>? storeIds,
    String? createdBy,
    int? limit,
    int? offset,
    bool includeAllocations = false,
  }) async {
    try {
      final query = _supabase
          .from('expenses')
          .select()
          .gte('expense_date', startDate?.toIso8601String().split('T').first ?? '2000-01-01')
          .lte('expense_date', endDate?.toIso8601String().split('T').first ?? '2100-12-31');

      // Apply optional filters only when provided to avoid invalid inputs (e.g., empty uuid string)
      final categoryValues = categories?.map((c) => c.dbValue).toList();
      if (categoryValues != null && categoryValues.isNotEmpty) {
        query.inFilter('category', categoryValues);
      }

      final methodValues = methods?.map((m) => m.dbValue).toList();
      if (methodValues != null && methodValues.isNotEmpty) {
        query.inFilter('payment_method', methodValues);
      }

      if (storeIds != null && storeIds.isNotEmpty) {
        query.inFilter('store_id', storeIds);
      }

      if (createdBy != null && createdBy.isNotEmpty) {
        query.eq('created_by', createdBy);
      }

      final response = await query
          .order('expense_date', ascending: false)
          .limit(limit ?? 50)
          .range(offset ?? 0, (offset ?? 0) + (limit ?? 50) - 1);

      final List<Expense> base = (response as List)
          .map((r) => Expense.fromJson(Map<String, dynamic>.from(r)))
          .toList();

      if (!includeAllocations || base.isEmpty) {
        return base;
      }

      final expenseIds = base.map((e) => e.id).toSet().toList();
      final allocRows = await _supabase
          .from('expense_store_allocations')
          .select()
          .inFilter('expense_id', expenseIds);

      final Map<String, List<ExpenseAllocation>> idToAllocs = {};
      for (final row in (allocRows as List)) {
        final map = Map<String, dynamic>.from(row as Map);
        final alloc = ExpenseAllocation.fromJson(map);
        idToAllocs.putIfAbsent(alloc.expenseId, () => []).add(alloc);
      }

      return base.map((e) => e.copyWith(allocations: idToAllocs[e.id])).toList();
    } catch (e) {
      throw 'Error fetching expenses: $e';
    }
  }

  Future<Expense> getExpense(String id) async {
    final exp = await _supabase.from('expenses').select().eq('id', id).single();
    final allocs = await _supabase
        .from('expense_store_allocations')
        .select()
        .eq('expense_id', id)
        .order('created_at', ascending: true);

    final map = Map<String, dynamic>.from(exp);
    map['allocations'] = (allocs as List).map((a) => Map<String, dynamic>.from(a)).toList();
    return Expense.fromJson(map);
  }

  Future<Expense> createExpenseSingleStore({
    ExpenseCategory? category,
    String? customCategoryId,
    required double amount,
    required DateTime date,
    required PaymentMethod method,
    required String storeId,
    String? note,
    String? vendor,
    String? reference,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw 'User not authenticated';

    if (category == null && (customCategoryId == null || customCategoryId.isEmpty)) {
      throw 'Please select a category (built-in or custom)';
    }

    final data = {
      'category': customCategoryId == null ? category!.dbValue : null,
      'custom_category_id': customCategoryId,
      'amount': amount,
      'expense_date': date.toIso8601String().split('T')[0],
      'payment_method': method.dbValue,
      'store_id': storeId,
      'note': note,
      'vendor': vendor,
      'reference': reference,
      'created_by': user.id,
    };

    final row = await _supabase.from('expenses').insert(data).select().single();
    return Expense.fromJson(row);
  }

  Future<Expense> createExpenseMultiStore({
    ExpenseCategory? category,
    String? customCategoryId,
    required double amount,
    required DateTime date,
    required PaymentMethod method,
    required List<Map<String, dynamic>> allocations, // {store_id, amount}
    String? note,
    String? vendor,
    String? reference,
  }) async {
    final user = _supabase.auth.currentUser;
    if (user == null) throw 'User not authenticated';

    // Basic client-side validation
    final total = allocations.fold<double>(0.0, (s, a) => s + ((a['amount'] as num).toDouble()));
    if ((total - amount).abs() > 0.001) {
      throw 'Allocation total must equal the expense amount';
    }

    if (category == null && (customCategoryId == null || customCategoryId.isEmpty)) {
      throw 'Please select a category (built-in or custom)';
    }

    // Use a PostgREST transaction via RPC or do optimistic inserts
    // We'll do simple: create expense without store_id, then allocations
    final data = {
      'category': customCategoryId == null ? category!.dbValue : null,
      'custom_category_id': customCategoryId,
      'amount': amount,
      'expense_date': date.toIso8601String().split('T')[0],
      'payment_method': method.dbValue,
      'store_id': null,
      'note': note,
      'vendor': vendor,
      'reference': reference,
      'created_by': user.id,
    };

    final exp = await _supabase.from('expenses').insert(data).select().single();
    final expenseId = exp['id'] as String;

    final allocRows = allocations
        .map((a) => {
              'expense_id': expenseId,
              'store_id': a['store_id'],
              'amount': (a['amount'] as num).toDouble(),
            })
        .toList();
    await _supabase.from('expense_store_allocations').insert(allocRows);

    return await getExpense(expenseId);
  }

  Future<Expense> updateExpense({
    required String expenseId,
    ExpenseCategory? category,
    String? customCategoryId,
    double? amount,
    DateTime? date,
    PaymentMethod? method,
    String? storeId,
    String? note,
    String? vendor,
    String? reference,
  }) async {
    final data = <String, dynamic>{'updated_at': DateTime.now().toIso8601String()};
    if (customCategoryId != null) {
      data['custom_category_id'] = customCategoryId;
      data['category'] = null; // enforce exclusivity when setting custom
    } else if (category != null) {
      data['category'] = category.dbValue;
      data['custom_category_id'] = null; // switching back to built-in
    }
    if (amount != null) data['amount'] = amount;
    if (date != null) data['expense_date'] = date.toIso8601String().split('T')[0];
    if (method != null) data['payment_method'] = method.dbValue;
    if (storeId != null) data['store_id'] = storeId; // may be null to switch to multi-store
    if (note != null) data['note'] = note;
    if (vendor != null) data['vendor'] = vendor;
    if (reference != null) data['reference'] = reference;

    final row = await _supabase.from('expenses').update(data).eq('id', expenseId).select().single();
    return Expense.fromJson(row);
  }

  Future<void> deleteExpense(String expenseId) async {
    await _supabase.from('expenses').delete().eq('id', expenseId);
  }

  // Replace allocations for an expense (used for multi-store updates)
  Future<void> setExpenseAllocations(String expenseId, List<Map<String, dynamic>> allocations) async {
    // delete all then insert new (enforced by trigger to match total)
    await _supabase.from('expense_store_allocations').delete().eq('expense_id', expenseId);
    if (allocations.isNotEmpty) {
      final rows = allocations
          .map((a) => {
                'expense_id': expenseId,
                'store_id': a['store_id'],
                'amount': (a['amount'] as num).toDouble(),
              })
          .toList();
      await _supabase.from('expense_store_allocations').insert(rows);
    }
  }
}


