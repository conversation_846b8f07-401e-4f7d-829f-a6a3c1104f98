import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/store_model.dart';
import '../../models/service_model.dart';
import '../../models/garment_model.dart';
import '../../models/user_address_model.dart';
import '../../models/customer_model.dart';
import '../../services/order_service.dart';
import '../../services/store_service.dart';
import '../../services/customer_service.dart';
import '../customers/customer_registration_page.dart';
import '../stores/store_detail_page.dart';
import '../../services/country_service.dart';
import '../stores/country_selection_page.dart';
import '../../widgets/country_picker_dialog.dart';
import 'order_detail_page.dart';
import '../../models/order_model.dart';

class PlaceOrderPage extends StatefulWidget {
  final Store? preselectedStore;

  const PlaceOrderPage({super.key, this.preselectedStore});

  @override
  State<PlaceOrderPage> createState() => _PlaceOrderPageState();
}

class _PlaceOrderPageState extends State<PlaceOrderPage> {
  final PageController _pageController = PageController();
  final OrderService _orderService = OrderService(supabase: Supabase.instance.client);
  final StoreService _storeService = StoreService(supabase: Supabase.instance.client);
  final CountryService _countryService = CountryService(supabase: Supabase.instance.client);
  final CustomerService _customerService = CustomerService(supabase: Supabase.instance.client);

  int _currentStep = 0;
  
  // Order data
  Store? _selectedStore;
  Service? _selectedService;
  final List<Service> _selectedServices = [];
  // ignore: prefer_final_fields
  List<OrderItemData> _orderItems = []; // Cannot be final - modified in _updateItemQuantity
  UserAddress? _pickupAddress;
  UserAddress? _deliveryAddress;
  Customer? _selectedCustomer;
  FulfillmentMode _fulfillmentMode = FulfillmentMode.pickupDelivery;
  InStoreOption _inStoreOption = InStoreOption.customerPickup;
  DateTime? _pickupDate;
  String? _pickupTimeSlot;
  DateTime? _deliveryDate;
  String? _deliveryTimeSlot;
  String? _specialInstructions;

  // UI state
  bool _isLoading = false;

  // Cached services future to avoid reloading on local UI updates
  Future<List<Service>>? _servicesFuture;
  // Cached garments-by-category future to avoid refetching on quantity changes
  Future<Map<String, List<Garment>>>? _garmentsFuture;

  // Store filters/search state
  final TextEditingController _storeSearchController = TextEditingController();
  String _storeSearchQuery = '';
  bool _storeOnlyActive = false;
  String? _storeCountryFilter;

  @override
  void initState() {
    super.initState();
    _selectedStore = widget.preselectedStore;
    if (_selectedStore != null) {
      _currentStep = 1; // Skip store selection
    }
    _servicesFuture = _orderService.getAllServices();
    _garmentsFuture = _orderService.getGarmentsByCategory();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _storeSearchController.dispose();
    super.dispose();
  }

  Future<List<Store>> _fetchStores() async {
    try {
      List<Store> stores;

      if (_storeSearchQuery.isNotEmpty) {
        stores = await _storeService.searchStores(_storeSearchQuery);
      } else if (_storeCountryFilter != null && _storeCountryFilter!.isNotEmpty) {
        stores = await _storeService.getStoresByCountry(_storeCountryFilter!);
      } else if (_storeOnlyActive) {
        stores = await _storeService.getActiveStores();
      } else {
        stores = await _storeService.getAllStores();
      }

      // Apply additional client-side filters when combined
      if (_storeOnlyActive) {
        stores = stores.where((s) => s.isActive).toList();
      }
      if (_storeCountryFilter != null && _storeCountryFilter!.isNotEmpty) {
        stores = stores.where((s) => (s.country ?? '').toLowerCase() == _storeCountryFilter!.toLowerCase()).toList();
      }
      if (_storeSearchQuery.isNotEmpty) {
        final q = _storeSearchQuery.toLowerCase();
        stores = stores.where((s) =>
          s.name.toLowerCase().contains(q) ||
          (s.address.toLowerCase().contains(q)) ||
          ((s.storeNumber ?? '').toLowerCase().contains(q))
        ).toList();
      }

      return stores;
    } catch (_) {
      return [];
    }
  }

  Future<void> _pickCountryFilter() async {
    try {
      final countries = await _countryService.getAllCountries();
      final names = countries.map((c) => c.name).toList();
      if (!mounted) return;
      final selected = await Navigator.push<String?>(
        context,
        MaterialPageRoute(
          builder: (ctx) => CountrySelectionPage(
            countries: names,
            selectedCountry: _storeCountryFilter,
            onChanged: (_) {},
          ),
        ),
      );
      if (selected != null) {
        setState(() {
          _storeCountryFilter = selected;
        });
      }
    } catch (_) {
      // ignore errors silently for UX
    }
  }

  void _clearStoreFilters() {
    setState(() {
      _storeSearchQuery = '';
      _storeSearchController.clear();
      _storeOnlyActive = false;
      _storeCountryFilter = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Place Order',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                if (widget.preselectedStore == null) _buildStoreSelection(),
                _buildServiceSelection(),
                _buildGarmentSelection(),
                _buildAddressSelection(),
                _buildTimeSlotSelection(),
                _buildOrderSummary(),
              ],
            ),
          ),
          _buildNavigationBar(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final steps = [
      if (widget.preselectedStore == null) 'Store',
      'Service',
      'Items',
      'Address',
      'Schedule',
      'Summary',
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: List.generate(steps.length, (index) {
          final isActive = index == _currentStep;
          final isCompleted = index < _currentStep;
          
          return Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 4,
                    decoration: BoxDecoration(
                      color: isCompleted || isActive ? Colors.blue : Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCompleted 
                        ? Colors.green 
                        : isActive 
                            ? Colors.blue 
                            : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: isCompleted
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: isActive ? Colors.white : Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStoreSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Store',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the store for your laundry order',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),

          // Search bar
          TextField(
            controller: _storeSearchController,
            decoration: InputDecoration(
              hintText: 'Search stores by name, number, or address...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _storeSearchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearStoreFilters,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              setState(() {
                _storeSearchQuery = value.trim();
              });
            },
          ),

          const SizedBox(height: 12),

          // Filter chips
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              FilterChip(
                label: const Text('Active only'),
                selected: _storeOnlyActive,
                onSelected: (val) {
                  setState(() {
                    _storeOnlyActive = val;
                  });
                },
              ),
              FilterChip(
                label: Text(_storeCountryFilter == null ? 'Country' : _storeCountryFilter!),
                selected: _storeCountryFilter != null,
                onSelected: (_) => _pickCountryFilter(),
              ),
              if (_storeSearchQuery.isNotEmpty || _storeOnlyActive || _storeCountryFilter != null)
                TextButton.icon(
                  onPressed: _clearStoreFilters,
                  icon: const Icon(Icons.filter_alt_off),
                  label: const Text('Clear filters'),
                ),
            ],
          ),

          const SizedBox(height: 12),
          Expanded(
            child: FutureBuilder<List<Store>>(
              future: _fetchStores(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text('Error: ${snapshot.error}'),
                  );
                }

                final stores = snapshot.data ?? [];
                
                if (stores.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.store,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No stores found',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _storeSearchQuery.isNotEmpty || _storeCountryFilter != null || _storeOnlyActive
                              ? 'Try adjusting your search or filters'
                              : 'Create a new store to continue with your order',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        if (_storeSearchQuery.isNotEmpty || _storeCountryFilter != null || _storeOnlyActive)
                          TextButton.icon(
                            onPressed: _clearStoreFilters,
                            icon: const Icon(Icons.filter_alt_off),
                            label: const Text('Clear filters'),
                          ),
                        const SizedBox(height: 12),
                        ElevatedButton.icon(
                          onPressed: _createNewStore,
                          icon: const Icon(Icons.add),
                          label: const Text('Create New Store'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: stores.length,
                  itemBuilder: (context, index) {
                    final store = stores[index];
                    return _buildStoreCard(store);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoreCard(Store store) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _selectedStore?.id == store.id ? Colors.blue : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () => setState(() => _selectedStore = store),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.store,
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      store.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (store.storeNumber != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Store #${store.storeNumber}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                    const SizedBox(height: 4),
                    Text(
                      store.address,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (store.phone != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            store.phone!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              if (_selectedStore?.id == store.id)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Service',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
             TextButton.icon(
               onPressed: _openCreateServiceDialog,
               icon: const Icon(Icons.add),
               label: const Text('Add Service'),
             ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Choose the type of laundry service you need',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
         if (_selectedServices.isNotEmpty)
           SingleChildScrollView(
             scrollDirection: Axis.horizontal,
             child: Row(
               children: _selectedServices
                   .map(
                     (s) => Padding(
                       padding: const EdgeInsets.only(right: 8),
                       child: Chip(
                         label: Text(s.name),
                         onDeleted: () {
                           setState(() {
                             _selectedServices.removeWhere((e) => e.id == s.id);
                             if (_selectedService?.id == s.id) {
                               _selectedService = _selectedServices.isNotEmpty ? _selectedServices.first : null;
                             }
                           });
                         },
                       ),
                     ),
                   )
                   .toList(),
             ),
           ),
          const SizedBox(height: 12),
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: FutureBuilder<List<Service>>(
                    future: _servicesFuture,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      if (snapshot.hasError) {
                        return Center(
                          child: Text('Error: ${snapshot.error}'),
                        );
                      }

                      final services = snapshot.data ?? [];
                      
                      return ListView.builder(
                        itemCount: services.length,
                        itemBuilder: (context, index) {
                          final service = services[index];
                          return _buildServiceCard(service);
                        },
                      );
                    },
                  ),
                ),
                // Summary Card
                if (_selectedServices.isNotEmpty)
                  _buildServiceSummaryCard(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceSummaryCard() {
    double totalAmount = 0.0;
    
    // Calculate total for selected services
    for (final service in _selectedServices) {
      final customAmount = _customAmountsPerService[service.id];
      final customMode = _customPricingModePerService[service.id];
      
      if (customAmount != null && customAmount > 0) {
        if (customMode == 'per_kg') {
          // For per_kg custom pricing, assume 1kg for estimation
          totalAmount += customAmount * 1.0;
        } else {
          // For fixed custom pricing
          totalAmount += customAmount;
        }
      } else {
        // Standard pricing
        switch (service.pricingType) {
          case 'per_kg':
            totalAmount += (service.pricePerKg ?? 0.0) * 1.0; // Assume 1kg for estimation
            break;
          case 'per_item':
            totalAmount += service.pricePerItem ?? 0.0;
            break;
          case 'fixed':
            totalAmount += service.basePrice;
            break;
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [Colors.blue[50]!, Colors.blue[100]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue[600],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.receipt_long,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Service Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green[600],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${_selectedServices.length} service${_selectedServices.length != 1 ? 's' : ''}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Services List
                ...(_selectedServices.map((service) {
                  final customAmount = _customAmountsPerService[service.id];
                  final customMode = _customPricingModePerService[service.id];
                  
                  String priceText;
                  if (customAmount != null && customAmount > 0) {
                    if (customMode == 'per_kg') {
                      priceText = '\$${customAmount.toStringAsFixed(2)}/kg (custom)';
                    } else {
                      priceText = '\$${customAmount.toStringAsFixed(2)} (custom)';
                    }
                  } else {
                    priceText = _getPricingText(service);
                  }
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _getServiceIcon(service.icon),
                          size: 16,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            service.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Text(
                          priceText,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                })),
                
                const SizedBox(height: 12),
                
                // Total Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.calculate,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'Estimated Total',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      Text(
                        '\$${totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Note
                Text(
                  'Estimated total based on 1kg per service. Final amount may vary based on actual weight and items.',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCard(Service service) {
    final isSelected = _selectedServices.any((s) => s.id == service.id);
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? Colors.blue : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedServices.removeWhere((s) => s.id == service.id);
              if (_selectedService?.id == service.id) {
                _selectedService = _selectedServices.isNotEmpty ? _selectedServices.first : null;
              }
            } else {
              _selectedServices.add(service);
              _selectedService ??= service;
            }
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getServiceIcon(service.icon),
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      service.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (service.description != null)
                      Text(
                        service.description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getPricingText(service),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => _openCustomAmountDialog(service),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.edit,
                                  size: 12,
                                  color: Colors.blue[700],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Custom',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue[700],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange[50],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            service.estimatedTimeText,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (service.pricingType == 'per_kg') ...[
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          const Text('Weight (kg):'),
                          SizedBox(
                            width: 90,
                            child: TextField(
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              decoration: const InputDecoration(
                                hintText: 'e.g. 3.5',
                                isDense: true,
                              ),
                              onChanged: (val) {
                                final weight = double.tryParse(val) ?? 0.0;
                                _customWeightPerKg[service.id] = weight;
                                setState(() {});
                              },
                            ),
                          ),
                          Text(
                            'Subtotal: \$${(service.basePrice + (service.pricePerKg ?? 0) * (_customWeightPerKg[service.id] ?? 0)).toStringAsFixed(2)}',
                            style: TextStyle(color: Colors.grey[700]),
                          ),
                        ],
                      ),
                    ],

                  ],
                ),
              ),
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Store per-kg custom weight per service
  final Map<String, double> _customWeightPerKg = {};
  
  // Store custom amounts per service
  final Map<String, double> _customAmountsPerService = {};
  
  // Store custom pricing mode per service ('fixed' or 'per_kg')
  final Map<String, String> _customPricingModePerService = {};
  
  // Store custom garments created by user
  final List<Garment> _customGarments = [];

  Future<void> _openCustomAmountDialog(Service service) async {
    final currentAmount = _customAmountsPerService[service.id];
    final currentMode = _customPricingModePerService[service.id] ?? 'fixed';
    final controller = TextEditingController(
      text: currentAmount != null ? currentAmount.toString() : '',
    );
    
    String selectedMode = currentMode;

    String getStandardPricingText() {
      switch (service.pricingType) {
        case 'per_kg':
          return '\$${service.pricePerKg?.toStringAsFixed(2) ?? '0.00'}/kg';
        case 'per_item':
          return '\$${service.pricePerItem?.toStringAsFixed(2) ?? '0.00'}/item';
        case 'fixed':
          return '\$${service.basePrice.toStringAsFixed(2)} fixed price';
        case 'custom':
          return 'Custom pricing';
        default:
          return 'Standard pricing';
      }
    }

    final result = await showDialog<dynamic>(
      context: context,
      barrierDismissible: true,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.edit,
                      color: Colors.blue[700],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Custom Amount',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(ctx).pop(null),
                    icon: const Icon(Icons.close),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[100],
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Service Info Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getServiceIcon(service.icon),
                          size: 18,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          service.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          'Standard pricing: ',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          getStandardPricingText(),
                          style: TextStyle(
                            color: Colors.grey[800],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Custom Amount Input
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Custom Amount',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Pricing Mode Selection
                  StatefulBuilder(
                    builder: (context, setDialogState) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Pricing Type',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    selectedMode = 'fixed';
                                    setDialogState(() {});
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                    decoration: BoxDecoration(
                                      color: selectedMode == 'fixed' ? Colors.blue[50] : Colors.grey[50],
                                      border: Border.all(
                                        color: selectedMode == 'fixed' ? Colors.blue[300]! : Colors.grey[300]!,
                                        width: selectedMode == 'fixed' ? 2 : 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.attach_money,
                                          size: 16,
                                          color: selectedMode == 'fixed' ? Colors.blue[700] : Colors.grey[600],
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Fixed Amount',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: selectedMode == 'fixed' ? Colors.blue[700] : Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    selectedMode = 'per_kg';
                                    setDialogState(() {});
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                    decoration: BoxDecoration(
                                      color: selectedMode == 'per_kg' ? Colors.blue[50] : Colors.grey[50],
                                      border: Border.all(
                                        color: selectedMode == 'per_kg' ? Colors.blue[300]! : Colors.grey[300]!,
                                        width: selectedMode == 'per_kg' ? 2 : 1,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.scale,
                                          size: 16,
                                          color: selectedMode == 'per_kg' ? Colors.blue[700] : Colors.grey[600],
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Price per KG',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: selectedMode == 'per_kg' ? Colors.blue[700] : Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          
                          // Amount Input Field
                          TextField(
                            controller: controller,
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            decoration: InputDecoration(
                              prefixText: '\$ ',
                              hintText: selectedMode == 'fixed' 
                                  ? 'Enter total amount' 
                                  : 'Enter price per kg',
                              labelText: selectedMode == 'fixed' 
                                  ? 'Total Amount' 
                                  : 'Price per KG',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.grey[50],
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              suffixIcon: controller.text.isNotEmpty 
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () {
                                        controller.clear();
                                        setDialogState(() {});
                                      },
                                    )
                                  : null,
                            ),
                            onChanged: (value) => setDialogState(() {}),
                            autofocus: true,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            selectedMode == 'fixed' 
                                ? 'Total price regardless of weight'
                                : 'Price will be multiplied by weight in kg',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(ctx).pop(-1.0); // Clear custom amount
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        side: BorderSide(color: Colors.grey[400]!),
                      ),
                      child: const Text('Clear Custom'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child:                     ElevatedButton(
                      onPressed: () {
                        final amount = double.tryParse(controller.text);
                        Navigator.of(ctx).pop({
                          'amount': amount,
                          'mode': selectedMode,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'Set Amount',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        if (result == -1.0) {
          // Clear custom amount
          _customAmountsPerService.remove(service.id);
          _customPricingModePerService.remove(service.id);
        } else if (result is Map) {
          // Set custom amount and mode
          final amount = result['amount'] as double?;
          final mode = result['mode'] as String;
          if (amount != null && amount > 0) {
            _customAmountsPerService[service.id] = amount;
            _customPricingModePerService[service.id] = mode;
          }
        }
      });
    }
  }

  Future<void> _openCreateCustomGarmentDialog() async {
    final formKey = GlobalKey<FormState>();
    String name = '';
    String? description;
    String category = 'Custom';
    String? icon;

    final result = await showDialog<bool>(
      context: context,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.add_circle,
                        color: Colors.orange[700],
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Add Custom Garment',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(ctx).pop(false),
                      icon: const Icon(Icons.close),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.grey[100],
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Form Fields
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Garment Name',
                    hintText: 'e.g., Special Jacket, Custom Dress',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.checkroom),
                  ),
                  validator: (v) => (v == null || v.trim().isEmpty) ? 'Name is required' : null,
                  onChanged: (v) => name = v.trim(),
                ),
                
                const SizedBox(height: 16),
                
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Description (optional)',
                    hintText: 'Add any special details',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.description),
                  ),
                  maxLines: 2,
                  onChanged: (v) => description = v.trim().isEmpty ? null : v.trim(),
                ),
                
                const SizedBox(height: 16),
                
                DropdownButtonFormField<String>(
                  value: 'custom',
                  decoration: InputDecoration(
                    labelText: 'Icon',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.palette),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'custom', child: Text('🔹 Custom')),
                    DropdownMenuItem(value: 'checkroom', child: Text('👔 Clothing')),
                    DropdownMenuItem(value: 'business_center', child: Text('💼 Business')),
                    DropdownMenuItem(value: 'bed', child: Text('🛏️ Bedding')),
                    DropdownMenuItem(value: 'shower', child: Text('🚿 Towels')),
                    DropdownMenuItem(value: 'work', child: Text('👷 Work wear')),
                    DropdownMenuItem(value: 'auto_awesome', child: Text('✨ Special')),
                  ],
                  onChanged: (v) => icon = v,
                ),
                
                const SizedBox(height: 24),
                
                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(ctx).pop(false),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () {
                          if (formKey.currentState?.validate() == true) {
                            Navigator.of(ctx).pop(true);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Add Garment',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );

    if (result == true) {
      // Create custom garment
      final customGarment = Garment(
        id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        category: category,
        icon: icon ?? 'custom',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      setState(() {
        _customGarments.add(customGarment);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Custom garment "$name" added!'),
            backgroundColor: Colors.orange[600],
          ),
        );
      }
    }
  }

  void _deleteCustomGarment(Garment garment) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Delete Custom Garment'),
        content: Text('Are you sure you want to delete "${garment.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _customGarments.removeWhere((g) => g.id == garment.id);
                // Also remove from order items if present
                _orderItems.removeWhere((item) => item.garmentId == garment.id);
              });
              Navigator.of(ctx).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${garment.name} deleted'),
                  backgroundColor: Colors.red[600],
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _openCreateServiceDialog() async {
    final formKey = GlobalKey<FormState>();
    String name = '';
    String? description;
    String pricingType = 'per_kg';
    String? icon;
    double basePrice = 0.0;
    double? pricePerKg;
    double? pricePerItem;
    int estimatedHours = 24;

    final res = await showDialog<bool>(
      context: context,
      builder: (ctx) {
        return AlertDialog(
          title: const Text('Add Service'),
          content: SingleChildScrollView(
            child: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Name'),
                    validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                    onChanged: (v) => name = v.trim(),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Description'),
                    onChanged: (v) => description = v.trim().isEmpty ? null : v.trim(),
                  ),
                  DropdownButtonFormField<String>(
                    value: pricingType,
                    items: const [
                      DropdownMenuItem(value: 'per_kg', child: Text('Per KG')),
                      DropdownMenuItem(value: 'per_item', child: Text('Per Item')),
                      DropdownMenuItem(value: 'fixed', child: Text('Fixed')),
                      DropdownMenuItem(value: 'custom', child: Text('Custom Amount')),
                    ],
                    onChanged: (v) {
                      pricingType = v ?? 'per_kg';
                    },
                    decoration: const InputDecoration(labelText: 'Pricing Type'),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Base Price'),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    onChanged: (v) => basePrice = double.tryParse(v) ?? 0.0,
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Price per KG'),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    onChanged: (v) => pricePerKg = double.tryParse(v),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Price per Item'),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    onChanged: (v) => pricePerItem = double.tryParse(v),
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Estimated Hours'),
                    keyboardType: TextInputType.number,
                    onChanged: (v) => estimatedHours = int.tryParse(v) ?? 24,
                  ),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Icon (Material icon name)'),
                    onChanged: (v) => icon = v.trim().isEmpty ? null : v.trim(),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState?.validate() == true) {
                  Navigator.of(ctx).pop(true);
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );

    if (res == true) {
      try {
        final newService = await _orderService.createService(
          name: name,
          description: description,
          basePrice: basePrice,
          pricePerKg: pricePerKg,
          pricePerItem: pricePerItem,
          pricingType: pricingType,
          estimatedHours: estimatedHours,
          icon: icon,
        );
        setState(() {
          _selectedServices.add(newService);
          _selectedService ??= newService;
          // refresh services list once, not on each input change
          _servicesFuture = _orderService.getAllServices();
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Service created')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to create service: $e')),
          );
        }
      }
    }
  }

  Widget _buildGarmentSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Items',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _openCreateCustomGarmentDialog,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('Add Custom'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Add garments and specify quantities',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: FutureBuilder<Map<String, List<Garment>>>(
              future: _garmentsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text('Error: ${snapshot.error}'),
                  );
                }

                final garmentsByCategory = snapshot.data ?? {};
                
                // Add custom garments to a special category
                final Map<String, List<Garment>> allGarments = Map.from(garmentsByCategory);
                if (_customGarments.isNotEmpty) {
                  allGarments['Custom Items'] = _customGarments;
                }
                
                return ListView.builder(
                  itemCount: allGarments.keys.length,
                  itemBuilder: (context, index) {
                    final category = allGarments.keys.elementAt(index);
                    final garments = allGarments[category]!;
                    return _buildGarmentCategory(category, garments, isCustomCategory: category == 'Custom Items');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGarmentCategory(String category, List<Garment> garments, {bool isCustomCategory = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              if (isCustomCategory)
                Icon(
                  Icons.star,
                  size: 20,
                  color: Colors.orange[600],
                ),
              if (isCustomCategory) const SizedBox(width: 8),
              Text(
                isCustomCategory ? 'Custom Items' : garments.first.categoryDisplayName,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: isCustomCategory ? Colors.orange[600] : Colors.blue,
                ),
              ),
            ],
          ),
        ),
        ...garments.map((garment) => _buildGarmentItem(garment, isCustom: isCustomCategory)),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildGarmentItem(Garment garment, {bool isCustom = false}) {
    final existingItem = _orderItems.firstWhere(
      (item) => item.garmentId == garment.id,
      orElse: () => OrderItemData(
        garmentId: garment.id,
        garment: garment,
        quantity: 0,
        unitPrice: _calculateUnitPrice(garment),
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isCustom ? Colors.orange[50] : Colors.grey[100],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                _getGarmentIcon(garment.icon),
                size: 20,
                color: isCustom ? Colors.orange[600] : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          garment.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      if (isCustom)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Custom',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (garment.description != null)
                    Text(
                      garment.description!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
            Row(
              children: [
                if (isCustom)
                  IconButton(
                    onPressed: () => _deleteCustomGarment(garment),
                    icon: const Icon(Icons.delete_outline),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.red[50],
                      foregroundColor: Colors.red[600],
                    ),
                    tooltip: 'Delete custom item',
                  ),
                IconButton(
                  onPressed: existingItem.quantity > 0
                      ? () => _updateItemQuantity(garment, existingItem.quantity - 1)
                      : null,
                  icon: const Icon(Icons.remove),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[700],
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    '${existingItem.quantity}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _updateItemQuantity(garment, existingItem.quantity + 1),
                  icon: const Icon(Icons.add),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.blue[100],
                    foregroundColor: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Normalizes address title to match dropdown options
  String _normalizeAddressTitle(String title) {
    final lowercaseTitle = title.toLowerCase();
    if (lowercaseTitle.contains('home')) return 'Home';
    if (lowercaseTitle.contains('office') || lowercaseTitle.contains('work')) return 'Office';
    return 'Other';
  }

  /// Checks if an address already exists for the user to avoid duplicates
  Future<UserAddress?> _findExistingAddress(String userId, String addressLine1, String city, String country) async {
    try {
      final addresses = await _orderService.getUserAddresses(userId);
      for (final addr in addresses) {
        if (addr.addressLine1.toLowerCase() == addressLine1.toLowerCase() &&
            addr.city.toLowerCase() == city.toLowerCase() &&
            addr.country.toLowerCase() == country.toLowerCase()) {
          return addr;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Widget _buildAddressSelection() {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Text('Please sign in to manage addresses', style: TextStyle(color: Colors.grey[700])),
        ),
      );
    }

    return Column(
      children: [
        // Top section with gradient background
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade600,
                Colors.blue.shade400,
                Colors.lightBlue.shade300,
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(24),
              bottomRight: Radius.circular(24),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 24, 20, 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Select Addresses',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Choose pickup and delivery preferences',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 20),
                // Customer selection button (primary action)
                if (_selectedCustomer == null)
                  ElevatedButton.icon(
                    onPressed: _openCustomerPicker,
                    icon: const Icon(Icons.person_search, size: 20),
                    label: const Text('Select Customer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.blue.shade700,
                      elevation: 4,
                      shadowColor: Colors.black.withValues(alpha: 0.3),
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  )
                else
                  // Customer selected - show address management buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _openCustomerPicker,
                          icon: const Icon(Icons.person_search, size: 18),
                          label: const Text('Change Customer'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.blue.shade700,
                            side: BorderSide(color: Colors.blue.shade300),
                            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _openAddAddressDialog(user.id),
                          icon: const Icon(Icons.add_location, size: 18),
                          label: const Text('Add Address'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.shade600,
                            foregroundColor: Colors.white,
                            elevation: 2,
                            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
        // Content section
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                // Customer selection prompt
                if (_selectedCustomer == null)
                  Center(
                    child: Column(
                      children: [
                        const SizedBox(height: 32),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.blue[200]!, width: 2),
                          ),
                          child: Column(
                            children: [
                              Icon(Icons.person_search, size: 48, color: Colors.blue[600]),
                              const SizedBox(height: 12),
                              Text(
                                'Choose Your Customer',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue[800],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Please select a customer first to configure their pickup and delivery preferences.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.blue[700],
                                ),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: _openCustomerPicker,
                                icon: const Icon(Icons.person_search),
                                label: const Text('Select Customer'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                // Show fulfillment options and addresses only after customer is selected
                if (_selectedCustomer != null) ...[
          const SizedBox(height: 8),
          // Fulfillment mode selector
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12), side: BorderSide(color: Colors.grey[200]!)),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Fulfillment', style: TextStyle(fontWeight: FontWeight.w600)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      ChoiceChip(
                        label: const Text('Pickup & Delivery'),
                        selected: _fulfillmentMode == FulfillmentMode.pickupDelivery,
                        onSelected: (v) => setState(() { _fulfillmentMode = FulfillmentMode.pickupDelivery; }),
                      ),
                      ChoiceChip(
                        label: const Text('In-Store'),
                        selected: _fulfillmentMode == FulfillmentMode.inStore,
                        onSelected: (v) => setState(() { _fulfillmentMode = FulfillmentMode.inStore; }),
                      ),
                    ],
                  ),
                  if (_fulfillmentMode == FulfillmentMode.inStore) ...[
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ChoiceChip(
                          label: const Text('Customer Pickup'),
                          selected: _inStoreOption == InStoreOption.customerPickup,
                          onSelected: (v) => setState(() { _inStoreOption = InStoreOption.customerPickup; }),
                        ),
                        ChoiceChip(
                          label: const Text('Deliver to Customer'),
                          selected: _inStoreOption == InStoreOption.deliveryToCustomer,
                          onSelected: (v) => setState(() { _inStoreOption = InStoreOption.deliveryToCustomer; }),
                        ),
                      ],
                    ),
                    // Store selection for in-store pickup
                    if (_inStoreOption == InStoreOption.customerPickup) ...[
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.store, color: Colors.blue[700], size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Pickup Store',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            if (_selectedStore != null) ...[
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.blue[300]!),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _selectedStore!.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                      ),
                                    ),
                                    if (_selectedStore!.address.isNotEmpty) ...[
                                      const SizedBox(height: 4),
                                      Text(
                                        _selectedStore!.address,
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                    if (_selectedStore!.phone != null && _selectedStore!.phone!.isNotEmpty) ...[
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                                          const SizedBox(width: 4),
                                          Text(
                                            _selectedStore!.phone!,
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              OutlinedButton.icon(
                                onPressed: _openStorePicker,
                                icon: const Icon(Icons.edit_location, size: 18),
                                label: const Text('Change Store'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.blue[700],
                                  side: BorderSide(color: Colors.blue[300]!),
                                ),
                              ),
                            ] else ...[
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.orange[300]!),
                                ),
                                child: Column(
                                  children: [
                                    Icon(Icons.store_outlined, size: 32, color: Colors.orange[600]),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No store selected',
                                      style: TextStyle(
                                        color: Colors.orange[700],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Please select a pickup store',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              ElevatedButton.icon(
                                onPressed: _openStorePicker,
                                icon: const Icon(Icons.store),
                                label: const Text('Select Store'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ],
                ],
              ),
            ),
          ),
          if (_selectedCustomer != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.person, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _selectedCustomer!.fullName,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  TextButton(
                    onPressed: _openCustomerPicker,
                    child: const Text('Change'),
                  ),
                  TextButton(
                    onPressed: () => setState(() { _selectedCustomer = null; }),
                    child: const Text('Clear'),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 16),
          FutureBuilder<List<UserAddress>>(
              future: _orderService.getUserAddresses(user.id),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }
                final addresses = snapshot.data ?? [];
                if (addresses.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('No addresses yet'),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          alignment: WrapAlignment.center,
                          children: [
                            ElevatedButton.icon(
                              onPressed: () => _openAddAddressDialog(user.id),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Address'),
                            ),
                            if (_selectedCustomer != null)
                              OutlinedButton.icon(
                                onPressed: _openCustomerPicker,
                                icon: const Icon(Icons.person_search),
                                label: const Text('Change Customer'),
                              ),
                          ],
                        ),
                      ],
                    ),
                  );
                }

                return ListView(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    if (_fulfillmentMode == FulfillmentMode.pickupDelivery) ...[
                      const Text('Pickup Address', style: TextStyle(fontWeight: FontWeight.w600)),
                      const SizedBox(height: 8),
                      ...addresses.map((a) => RadioListTile<String>(
                            value: a.id,
                            groupValue: _pickupAddress?.id,
                            onChanged: (val) {
                              setState(() { _pickupAddress = a; });
                            },
                            title: Text(a.displayTitle),
                            subtitle: Text(a.fullAddress),
                            secondary: IconButton(
                              icon: const Icon(Icons.edit),
                              tooltip: 'Edit',
                              onPressed: () => _openEditAddressDialog(a),
                            ),
                          )),
                      const SizedBox(height: 16),
                    ],
                    if (_fulfillmentMode == FulfillmentMode.pickupDelivery || (_fulfillmentMode == FulfillmentMode.inStore && _inStoreOption == InStoreOption.deliveryToCustomer)) ...[
                      const Text('Delivery Address', style: TextStyle(fontWeight: FontWeight.w600)),
                      const SizedBox(height: 8),
                      ...addresses.map((a) => RadioListTile<String>(
                            value: a.id,
                            groupValue: _deliveryAddress?.id,
                            onChanged: (val) {
                              setState(() { _deliveryAddress = a; });
                            },
                            title: Text(a.displayTitle),
                            subtitle: Text(a.fullAddress),
                            secondary: IconButton(
                              icon: const Icon(Icons.edit),
                              tooltip: 'Edit',
                              onPressed: () => _openEditAddressDialog(a),
                            ),
                          )),
                      if (_fulfillmentMode == FulfillmentMode.pickupDelivery) ...[
                        const SizedBox(height: 12),
                        TextButton.icon(
                          onPressed: () {
                            if (_pickupAddress != null) {
                              setState(() => _deliveryAddress = _pickupAddress);
                            }
                          },
                          icon: const Icon(Icons.copy_all),
                          label: const Text('Use pickup address for delivery'),
                        ),
                      ],
                    ],
                  ],
                );
              },
            ),
        ],
                ],
      ),
            ),
    ),
      ],
    );
  }

  Widget _buildTimeSlotSelection() {
    final timeSlots = _orderService.getAvailableTimeSlots();
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Schedule', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text('Pick your preferred pickup and delivery dates and time slots',
              style: TextStyle(fontSize: 16, color: Colors.grey[600])),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _DatePickerTile(
                  label: 'Pickup date',
                  date: _pickupDate,
                  onTap: () async {
                    final now = DateTime.now();
                    final date = await showDatePicker(
                      context: context,
                      firstDate: now,
                      lastDate: now.add(const Duration(days: 30)),
                      initialDate: _pickupDate ?? now.add(const Duration(days: 1)),
                    );
                    if (date != null) setState(() => _pickupDate = date);
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _pickupTimeSlot,
                  items: timeSlots.map((s) => DropdownMenuItem(value: s, child: Text(s))).toList(),
                  onChanged: (v) => setState(() => _pickupTimeSlot = v),
                  decoration: const InputDecoration(labelText: 'Pickup slot'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _DatePickerTile(
                  label: 'Delivery date',
                  date: _deliveryDate,
                  onTap: () async {
                    final base = _pickupDate ?? DateTime.now();
                    final date = await showDatePicker(
                      context: context,
                      firstDate: base,
                      lastDate: base.add(const Duration(days: 30)),
                      initialDate: _deliveryDate ?? base.add(const Duration(days: 1)),
                    );
                    if (date != null) setState(() => _deliveryDate = date);
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _deliveryTimeSlot,
                  items: timeSlots.map((s) => DropdownMenuItem(value: s, child: Text(s))).toList(),
                  onChanged: (v) => setState(() => _deliveryTimeSlot = v),
                  decoration: const InputDecoration(labelText: 'Delivery slot'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            initialValue: _specialInstructions,
            decoration: const InputDecoration(labelText: 'Special instructions (optional)'),
            maxLines: 2,
            onChanged: (v) => _specialInstructions = v.trim().isEmpty ? null : v.trim(),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    final itemsTotal = _orderItems.fold<double>(0.0, (sum, i) => sum + i.totalPrice);
    final tax = itemsTotal * 0.10;
    final total = itemsTotal + tax;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ListView(
        children: [
          const Text('Order Summary', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
          const SizedBox(height: 12),
          _summaryRow('Store', _selectedStore?.name ?? '-'),
          _summaryRow('Service(s)', _selectedServices.map((s) => s.name).join(', ')),
          _summaryRow('Pickup address', _pickupAddress?.shortAddress ?? '-'),
          _summaryRow('Delivery address', _deliveryAddress?.shortAddress ?? '-'),
          _summaryRow('Pickup', _formatDateSlot(_pickupDate, _pickupTimeSlot)),
          _summaryRow('Delivery', _formatDateSlot(_deliveryDate, _deliveryTimeSlot)),
          const Divider(height: 32),
          const Text('Items', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          ..._orderItems.map((i) => ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                title: Text(i.garment.name),
                subtitle: Text('${i.quantity} x \$${i.unitPrice.toStringAsFixed(2)}'),
                trailing: Text('\$${i.totalPrice.toStringAsFixed(2)}'),
              )),
          const Divider(height: 32),
          _summaryMoney('Subtotal', itemsTotal),
          _summaryMoney('Tax (10%)', tax),
          const SizedBox(height: 8),
          _summaryMoney('Total', total, isBold: true),
        ],
      ),
    );
  }

  String _formatDateSlot(DateTime? date, String? slot) {
    if (date == null || slot == null) return '-';
    final d = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return '$d $slot';
  }

  Widget _summaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(child: Text(label, style: TextStyle(color: Colors.grey[700]))),
          Text(value),
        ],
      ),
    );
  }

  Widget _summaryMoney(String label, double amount, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(child: Text(label, style: TextStyle(color: Colors.grey[700]))),
          Text('\$${amount.toStringAsFixed(2)}', style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
        ],
      ),
    );
  }

  Future<void> _openAddAddressDialog(String userId) async {
    final formKey = GlobalKey<FormState>();
    String title = 'Home';
    String address1 = '';
    String? address2;
    String city = '';
    String country = '';
    final countryCtrl = TextEditingController(text: country);

    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Add Address'),
        content: SingleChildScrollView(
          child: StatefulBuilder(
            builder: (context, setDialogState) {
              return Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    DropdownButtonFormField<String>(
                      value: title,
                      items: const [
                        DropdownMenuItem(value: 'Home', child: Text('Home')),
                        DropdownMenuItem(value: 'Office', child: Text('Office')),
                        DropdownMenuItem(value: 'Other', child: Text('Other')),
                      ],
                      onChanged: (v) => title = v ?? 'Home',
                      decoration: const InputDecoration(labelText: 'Title'),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      decoration: const InputDecoration(labelText: 'Address line 1'),
                      validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                      onChanged: (v) => address1 = v.trim(),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      decoration: const InputDecoration(labelText: 'Address line 2 (optional)'),
                      onChanged: (v) => address2 = v.trim().isEmpty ? null : v.trim(),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      decoration: const InputDecoration(labelText: 'City'),
                      validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                      onChanged: (v) => city = v.trim(),
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () async {
                        await showCountryPicker(
                          context: context,
                          selectedCountry: null,
                          onSelect: (c) {
                            setDialogState(() {
                              country = c.name;
                              countryCtrl.text = c.name;
                            });
                          },
                        );
                      },
                      child: AbsorbPointer(
                        child: TextFormField(
                          controller: countryCtrl,
                          decoration: const InputDecoration(
                            labelText: 'Country',
                            suffixIcon: Icon(Icons.keyboard_arrow_down),
                          ),
                          validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState?.validate() == true) {
                Navigator.pop(ctx, true);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (ok == true) {
      try {
        // Check if this address already exists to avoid duplicates
        final existingAddr = await _findExistingAddress(userId, address1, city, country);
        
        UserAddress addr;
        if (existingAddr != null) {
          // Update existing address
          final updated = await Supabase.instance.client
              .from('user_addresses')
              .update({
                'title': title,
                'address_line_2': address2,
              })
              .eq('id', existingAddr.id)
              .select()
              .single();
          addr = UserAddress.fromJson(updated);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Address updated'), backgroundColor: Colors.green),
            );
          }
        } else {
          // Create new address
          final inserted = await Supabase.instance.client
              .from('user_addresses')
              .insert({
                'user_id': userId,
                'title': title,
                'address_line_1': address1,
                'address_line_2': address2,
                'city': city,
                'country': country,
                'is_default': false,
                'is_active': true,
              })
              .select()
              .single();
          addr = UserAddress.fromJson(inserted);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Address added'), backgroundColor: Colors.green),
            );
          }
        }
        
        setState(() {
          _pickupAddress ??= addr;
          _deliveryAddress ??= addr;
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save address: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Future<void> _openEditAddressDialog(UserAddress address) async {
    final formKey = GlobalKey<FormState>();
    // Normalize title to match dropdown options
    String title = _normalizeAddressTitle(address.title);
    String address1 = address.addressLine1;
    String? address2 = address.addressLine2;
    String city = address.city;
    String country = address.country;
    final countryCtrl = TextEditingController(text: country);

    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Edit Address'),
        content: SingleChildScrollView(
          child: StatefulBuilder(
            builder: (context, setDialogState) {
              return Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    DropdownButtonFormField<String>(
                      value: title,
                      items: const [
                        DropdownMenuItem(value: 'Home', child: Text('Home')),
                        DropdownMenuItem(value: 'Office', child: Text('Office')),
                        DropdownMenuItem(value: 'Other', child: Text('Other')),
                      ],
                      onChanged: (v) => title = v ?? 'Home',
                      decoration: const InputDecoration(labelText: 'Title'),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      initialValue: address1,
                      decoration: const InputDecoration(labelText: 'Address line 1'),
                      validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                      onChanged: (v) => address1 = v.trim(),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      initialValue: address2,
                      decoration: const InputDecoration(labelText: 'Address line 2 (optional)'),
                      onChanged: (v) => address2 = v.trim().isEmpty ? null : v.trim(),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      initialValue: city,
                      decoration: const InputDecoration(labelText: 'City'),
                      validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                      onChanged: (v) => city = v.trim(),
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () async {
                        await showCountryPicker(
                          context: context,
                          selectedCountry: null,
                          onSelect: (c) {
                            setDialogState(() {
                              country = c.name;
                              countryCtrl.text = c.name;
                            });
                          },
                        );
                      },
                      child: AbsorbPointer(
                        child: TextFormField(
                          controller: countryCtrl,
                          decoration: const InputDecoration(
                            labelText: 'Country',
                            suffixIcon: Icon(Icons.keyboard_arrow_down),
                          ),
                          validator: (v) => (v == null || v.trim().isEmpty) ? 'Required' : null,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState?.validate() == true) {
                Navigator.pop(ctx, true);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (ok == true) {
      try {
        final updated = await Supabase.instance.client
            .from('user_addresses')
            .update({
              'title': title,
              'address_line_1': address1,
              'address_line_2': address2,
              'city': city,
              'country': country,
            })
            .eq('id', address.id)
            .select()
            .single();
        final addr = UserAddress.fromJson(updated);
        setState(() {
          if (_pickupAddress?.id == addr.id) _pickupAddress = addr;
          if (_deliveryAddress?.id == addr.id) _deliveryAddress = addr;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address updated'), backgroundColor: Colors.green),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to update address: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Future<void> _openCustomerPicker() async {
    final selected = await showModalBottomSheet<Customer?>(
      context: context,
      isScrollControlled: true,
      builder: (ctx) {
        String search = '';
        Future<List<Customer>> fetch([String? term]) => _customerService.getCustomers(searchTerm: term, onlyActive: true);
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.85,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, controller) {
            return StatefulBuilder(builder: (context, setModalState) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.person_search),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text('Select Customer', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                        ),
                        ElevatedButton.icon(
                          onPressed: () async {
                            final created = await Navigator.of(context).push<Customer>(
                              MaterialPageRoute(
                                builder: (_) => const CustomerRegistrationPage(),
                              ),
                            );
                            if (!context.mounted) return;
                            if (created != null) {
                              Navigator.of(context).pop(created);
                            }
                          },
                          icon: const Icon(Icons.person_add_alt_1, size: 18),
                          label: const Text('Add Customer'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            elevation: 3,
                            shadowColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                          ),
                        ),
                        const SizedBox(width: 4),
                        IconButton(onPressed: () => Navigator.pop(context, null), icon: const Icon(Icons.close)),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      decoration: const InputDecoration(prefixIcon: Icon(Icons.search), hintText: 'Search by name, email or phone'),
                      onChanged: (v) => setModalState(() { search = v.trim(); }),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: FutureBuilder<List<Customer>>(
                        future: fetch(search.isEmpty ? null : search),
                        builder: (context, snap) {
                          if (snap.connectionState == ConnectionState.waiting) {
                            return const Center(child: CircularProgressIndicator());
                          }
                          final list = snap.data ?? [];
                          if (list.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Text('No customers found'),
                                  const SizedBox(height: 12),
                                  ElevatedButton.icon(
                                    onPressed: () async {
                                      final created = await Navigator.of(context).push<Customer>(
                                        MaterialPageRoute(
                                          builder: (_) => const CustomerRegistrationPage(),
                                        ),
                                      );
                                      if (!context.mounted) return;
                                      if (created != null) {
                                        Navigator.of(context).pop(created);
                                      }
                                    },
                                    icon: const Icon(Icons.person_add_alt_1, size: 18),
                                    label: const Text('Add Customer'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                      elevation: 3,
                                      shadowColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                          return ListView.separated(
                            controller: controller,
                            itemCount: list.length,
                            separatorBuilder: (_, __) => const Divider(height: 1),
                            itemBuilder: (context, i) {
                              final c = list[i];
                              final subtitle = [c.email, c.phone, c.country]
                                  .where((e) => (e ?? '').isNotEmpty)
                                  .join(' • ');
                              return ListTile(
                                leading: CircleAvatar(child: Text(c.fullName.isNotEmpty ? c.fullName[0].toUpperCase() : '?')),
                                title: Text(c.fullName),
                                subtitle: subtitle.isNotEmpty ? Text(subtitle) : null,
                                onTap: () => Navigator.pop(context, c),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            });
          },
        );
      },
    );

    if (selected != null) {
      setState(() { _selectedCustomer = selected; });
      await _importCustomerDefaultAddress(selected);
    }
  }

  Future<void> _importCustomerDefaultAddress(Customer customer) async {
    try {
      final addrs = await _customerService.getCustomerAddresses(customer.id!);
      if (addrs.isEmpty) {
        if (!mounted) return;
        // Inform and prompt to add or change customer
        showDialog(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('No Address Found'),
            content: Text('${customer.fullName} has no saved addresses. You can add one to continue or choose a different customer.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(ctx).pop();
                  _openCustomerPicker();
                },
                child: const Text('Change Customer'),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(ctx).pop();
                  final userId = Supabase.instance.client.auth.currentUser!.id;
                  _openAddAddressDialog(userId);
                },
                icon: const Icon(Icons.add),
                label: const Text('Add Address'),
              ),
            ],
          ),
        );
        return;
      }
      final preferred = addrs.firstWhere((a) => a.isDefault, orElse: () => addrs.first);

      final user = Supabase.instance.client.auth.currentUser!;
      
      // Check if this address already exists to avoid duplicates
      final existingAddr = await _findExistingAddress(
        user.id, 
        preferred.addressLine1, 
        preferred.city, 
        preferred.country
      );
      
      UserAddress userAddr;
      if (existingAddr != null) {
        // Update existing address with normalized title
        final updated = await Supabase.instance.client
            .from('user_addresses')
            .update({
              'title': _normalizeAddressTitle(preferred.title),
              'address_line_2': preferred.addressLine2,
              'state': preferred.state,
              'postal_code': preferred.postalCode,
            })
            .eq('id', existingAddr.id)
            .select()
            .single();
        userAddr = UserAddress.fromJson(updated);
      } else {
        // Create new address with normalized title
        final inserted = await Supabase.instance.client
            .from('user_addresses')
            .insert({
              'user_id': user.id,
              'title': _normalizeAddressTitle(preferred.title),
              'address_line_1': preferred.addressLine1,
              'address_line_2': preferred.addressLine2,
              'city': preferred.city,
              'state': preferred.state,
              'postal_code': preferred.postalCode,
              'country': preferred.country,
              'is_default': false,
              'is_active': true,
            })
            .select()
            .single();
        userAddr = UserAddress.fromJson(inserted);
      }
      if (!mounted) return;
      setState(() {
        _pickupAddress = userAddr;
        _deliveryAddress = userAddr;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Address imported from ${customer.fullName}')),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to import address: $e')),
      );
    }
  }

  Widget _buildNavigationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _goToPreviousStep,
                child: const Text('Back'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: _canProceed() ? _goToNextStep : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(_getNextButtonText()),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getServiceIcon(String? iconName) {
    switch (iconName) {
      case 'local_laundry_service':
        return Icons.local_laundry_service;
      case 'dry_cleaning':
        return Icons.dry_cleaning;
      case 'iron':
        return Icons.iron;
      case 'flash_on':
        return Icons.flash_on;
      case 'favorite':
        return Icons.favorite;
      default:
        return Icons.local_laundry_service;
    }
  }

  IconData _getGarmentIcon(String? iconName) {
    switch (iconName) {
      case 'checkroom':
        return Icons.checkroom;
      case 'business_center':
        return Icons.business_center;
      case 'bed':
        return Icons.bed;
      case 'shower':
        return Icons.shower;
      case 'window':
        return Icons.window;
      case 'work':
        return Icons.work;
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'custom':
        return Icons.star;
      default:
        return Icons.checkroom;
    }
  }

  String _getPricingText(Service service) {
    // Check if custom amount is set for any service type
    final customAmount = _customAmountsPerService[service.id];
    final customMode = _customPricingModePerService[service.id];
    if (customAmount != null && customAmount > 0) {
      final suffix = customMode == 'per_kg' ? '/kg custom' : ' custom';
      return '\$${customAmount.toStringAsFixed(2)}$suffix';
    }
    
    switch (service.pricingType) {
      case 'per_kg':
        return '\$${service.pricePerKg?.toStringAsFixed(2) ?? '0.00'}/kg';
      case 'per_item':
        return '\$${service.pricePerItem?.toStringAsFixed(2) ?? '0.00'}/item';
      case 'fixed':
        return '\$${service.basePrice.toStringAsFixed(2)} fixed price';
      case 'custom':
        return 'Custom pricing';
      default:
        return 'Standard pricing';
    }
  }

  double _calculateUnitPrice(Garment garment) {
    if (_selectedService == null) return 0.0;
    
    // Check if custom amount is set for this service first
    final customAmount = _customAmountsPerService[_selectedService!.id];
    final customMode = _customPricingModePerService[_selectedService!.id];
    if (customAmount != null && customAmount > 0) {
      if (customMode == 'per_kg') {
        // For per_kg custom pricing, return the per kg rate (will be multiplied by quantity/weight later)
        return customAmount;
      } else {
        // For fixed custom pricing, return the fixed amount
        return customAmount;
      }
    }
    
    switch (_selectedService!.pricingType) {
      case 'per_item':
        return _selectedService!.pricePerItem ?? 0.0;
      case 'per_kg':
        // For per_kg pricing, we need weight input
        // For now, assume 1kg per item as default
        return _selectedService!.pricePerKg ?? 0.0;
      case 'fixed':
        return _selectedService!.basePrice;
      case 'custom':
        return 0.0;
      default:
        return 0.0;
    }
  }

  void _updateItemQuantity(Garment garment, int quantity) {
    setState(() {
      final existingIndex = _orderItems.indexWhere(
        (item) => item.garmentId == garment.id,
      );

      if (quantity <= 0) {
        if (existingIndex >= 0) {
          _orderItems.removeAt(existingIndex);
        }
      } else {
        final unitPrice = _calculateUnitPrice(garment);
        final newItem = OrderItemData(
          garmentId: garment.id,
          garment: garment,
          quantity: quantity,
          unitPrice: unitPrice,
        );

        if (existingIndex >= 0) {
          _orderItems[existingIndex] = newItem;
        } else {
          _orderItems.add(newItem);
        }
      }
    });
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0: // Store selection
        return _selectedStore != null;
      case 1: // Service selection
        return _selectedServices.isNotEmpty;
      case 2: // Garment selection
        return _orderItems.isNotEmpty;
      case 3: // Address selection
        if (_selectedCustomer == null) return false;
        if (_fulfillmentMode == FulfillmentMode.pickupDelivery) {
          return _pickupAddress != null && _deliveryAddress != null;
        }
        // In-store
        if (_inStoreOption == InStoreOption.deliveryToCustomer) {
          return _deliveryAddress != null && _selectedStore != null;
        }
        // Customer pickup in-store requires customer and store selection
        return _selectedStore != null;
      case 4: // Time slot selection
        return _pickupDate != null && _pickupTimeSlot != null;
      default:
        return true;
    }
  }

  String _getNextButtonText() {
    final totalSteps = widget.preselectedStore == null ? 6 : 5;
    return _currentStep == totalSteps - 1 ? 'Place Order' : 'Next';
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    final totalSteps = widget.preselectedStore == null ? 6 : 5;
    
    if (_currentStep < totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _placeOrder();
    }
  }

  void _createNewStore() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StoreDetailPage(),
      ),
    ).then((_) async {
      // Refresh the store list when returning from store creation
      setState(() {});
      
      // Get the latest stores and auto-select the first one if none selected
      final stores = await _storeService.getAllStores();
      if (stores.isNotEmpty && _selectedStore == null) {
        setState(() {
          _selectedStore = stores.first;
        });
        
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Store "${stores.first.name}" created and selected!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        
        // Automatically proceed to next step
        _goToNextStep();
      }
    });
  }

  Future<void> _placeOrder() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        throw 'User not authenticated';
      }

      final orderItems = _orderItems.map((item) => OrderItem(
        orderId: '', // Will be set when creating
        garmentId: item.garmentId,
        garment: item.garment,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.unitPrice * item.quantity,
      )).toList();

      // Ensure we have a valid service selected
      if (_selectedService == null && _selectedServices.isNotEmpty) {
        _selectedService = _selectedServices.first;
      }
      
      if (_selectedService == null) {
        throw 'No service selected';
      }

      // NEW WORKFLOW: Create order first (no invoice yet)
      final order = await _orderService.createOrder(
        userId: user.id,
        storeId: _selectedStore!.id,
        serviceId: _selectedService!.id,
        items: orderItems,
        pickupAddressId: _fulfillmentMode == FulfillmentMode.pickupDelivery ? _pickupAddress?.id : null,
        deliveryAddressId: (_fulfillmentMode == FulfillmentMode.pickupDelivery || (_fulfillmentMode == FulfillmentMode.inStore && _inStoreOption == InStoreOption.deliveryToCustomer))
            ? _deliveryAddress?.id
            : null,
        pickupDate: _pickupDate,
        pickupTimeSlot: _pickupTimeSlot,
        deliveryDate: _deliveryDate,
        deliveryTimeSlot: _deliveryTimeSlot,
        specialInstructions: _specialInstructions,
      );

      // TODO: Later when payment is received, create invoice:
      // final invoiceData = await _orderService.createInvoiceForOrder(
      //   order.id!, 
      //   paymentMethod: 'cash' // or 'card', 'mpesa', etc.
      // );
      // print('Invoice created: ${invoiceData['invoice_number']}');

      if (mounted) {
        // Show success dialog with options
        await _showOrderSuccessDialog(order);
      }
    } catch (e) {
      if (mounted) {
        // Enhanced error handling for better debugging
        String errorMessage = 'Failed to place order';
        
        if (e.toString().contains('PostgrestException')) {
          // Parse PostgreSQL specific errors
          if (e.toString().contains('ambiguous')) {
            errorMessage = 'Database error: Column reference ambiguity. Please try again.';
          } else if (e.toString().contains('order_number')) {
            errorMessage = 'Error generating order number. Please try again.';
          } else if (e.toString().contains('foreign key')) {
            errorMessage = 'Data consistency error. Please refresh and try again.';
          } else {
            errorMessage = 'Database error occurred. Please try again.';
          }
        } else if (e.toString().contains('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = 'Error placing order: ${e.toString()}';
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Details',
              textColor: Colors.white,
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Error Details'),
                    content: SingleChildScrollView(
                      child: Text(e.toString()),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _openStorePicker() async {
    final selected = await showModalBottomSheet<Store?>(
      context: context,
      isScrollControlled: true,
      builder: (ctx) {
        String search = '';
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.85,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, controller) {
            return StatefulBuilder(builder: (context, setModalState) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.store),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text('Select Store', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                        ),
                        IconButton(onPressed: () => Navigator.pop(context, null), icon: const Icon(Icons.close)),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      decoration: const InputDecoration(prefixIcon: Icon(Icons.search), hintText: 'Search stores by name or location'),
                      onChanged: (v) => setModalState(() { search = v.trim(); }),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: FutureBuilder<List<Store>>(
                        future: _fetchStoresBySearch(search),
                        builder: (context, snap) {
                          if (snap.connectionState == ConnectionState.waiting) {
                            return const Center(child: CircularProgressIndicator());
                          }
                          
                          if (snap.hasError) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Error loading stores',
                                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.red[700]),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Please check your connection and try again.',
                                    style: TextStyle(color: Colors.grey[600]),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton.icon(
                                    onPressed: () => setModalState(() {}),
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('Retry'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red[600],
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                          
                          final stores = snap.data ?? [];
                          
                          if (stores.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.store_outlined, size: 64, color: Colors.orange[300]),
                                  const SizedBox(height: 16),
                                  Text(
                                    search.isEmpty ? 'No Stores Available' : 'No Stores Found',
                                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.orange[700]),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    search.isEmpty 
                                      ? 'There are currently no stores available for selection. Please contact support or check back later.'
                                      : 'No stores match your search criteria. Try adjusting your search terms.',
                                    style: TextStyle(color: Colors.grey[600]),
                                    textAlign: TextAlign.center,
                                  ),
                                  if (search.isNotEmpty) ...[
                                    const SizedBox(height: 16),
                                    OutlinedButton.icon(
                                      onPressed: () => setModalState(() { search = ''; }),
                                      icon: const Icon(Icons.clear),
                                      label: const Text('Clear Search'),
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: Colors.orange[700],
                                        side: BorderSide(color: Colors.orange[300]!),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }
                          
                          return ListView.builder(
                            controller: controller,
                            itemCount: stores.length,
                            itemBuilder: (context, i) {
                              final store = stores[i];
                              final isSelected = _selectedStore?.id == store.id;
                              
                              return Card(
                                margin: const EdgeInsets.symmetric(vertical: 4),
                                elevation: isSelected ? 2 : 0,
                                color: isSelected ? Colors.blue[50] : null,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  side: BorderSide(
                                    color: isSelected ? Colors.blue[300]! : Colors.grey[200]!,
                                    width: isSelected ? 2 : 1,
                                  ),
                                ),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.all(12),
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: isSelected ? Colors.blue[100] : Colors.grey[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.store,
                                      color: isSelected ? Colors.blue[700] : Colors.grey[600],
                                    ),
                                  ),
                                  title: Text(
                                    store.name,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: isSelected ? Colors.blue[900] : null,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      if (store.address.isNotEmpty) ...[
                                        const SizedBox(height: 4),
                                        Text(store.address, style: TextStyle(color: Colors.grey[600])),
                                      ],
                                      if (store.phone != null && store.phone!.isNotEmpty) ...[
                                        const SizedBox(height: 2),
                                        Row(
                                          children: [
                                            Icon(Icons.phone, size: 14, color: Colors.grey[500]),
                                            const SizedBox(width: 4),
                                            Text(store.phone!, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
                                          ],
                                        ),
                                      ],
                                    ],
                                  ),
                                  trailing: isSelected
                                    ? Icon(Icons.check_circle, color: Colors.blue[700])
                                    : Icon(Icons.radio_button_unchecked, color: Colors.grey[400]),
                                  onTap: () => Navigator.pop(context, store),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            });
          },
        );
      },
    );

    if (selected != null) {
      setState(() { _selectedStore = selected; });
    }
  }

  Future<List<Store>> _fetchStoresBySearch(String search) async {
    try {
      if (search.isNotEmpty) {
        return await _storeService.searchStores(search);
      } else {
        return await _storeService.getActiveStores();
      }
    } catch (e) {
      throw Exception('Failed to load stores: $e');
    }
  }

  Future<void> _showOrderSuccessDialog(Order order) async {
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Color(0xFFF0F8FF)],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green.shade600,
                  size: 48,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Success message
              Text(
                'Order Created Successfully!',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 12),
              
              // Order details
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Order Number:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        Text(
                          order.orderNumber,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total Amount:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        Text(
                          '\$${order.totalAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Action buttons
              Column(
                children: [
                  // View Order Detail button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => Navigator.of(ctx).pop('view_order'),
                      icon: const Icon(Icons.receipt_long, size: 20),
                      label: const Text('View Order Details'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Create New Order button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.of(ctx).pop('new_order'),
                      icon: const Icon(Icons.add_shopping_cart, size: 20),
                      label: const Text('Create New Order'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.green.shade700,
                        side: BorderSide(color: Colors.green.shade300, width: 2),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Close button
                  TextButton(
                    onPressed: () => Navigator.of(ctx).pop('close'),
                    child: Text(
                      'Close',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    // Handle user choice
    if (!mounted) return;
    
    if (result == 'view_order') {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => OrderDetailPage(orderId: order.id),
        ),
      );
    } else if (result == 'new_order') {
      // Reset the form for a new order
      _resetForm();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Order ${order.orderNumber} created! Ready for new order.'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // Close - go back to previous page
      Navigator.of(context).pop(order);
    }
  }

  void _resetForm() {
    setState(() {
      _currentStep = widget.preselectedStore == null ? 0 : 1;
      _selectedServices.clear();
      _selectedService = null;
      _orderItems.clear();
      _pickupAddress = null;
      _deliveryAddress = null;
      _selectedCustomer = null;
      _fulfillmentMode = FulfillmentMode.pickupDelivery;
      _inStoreOption = InStoreOption.customerPickup;
      _pickupDate = null;
      _pickupTimeSlot = null;
      _deliveryDate = null;
      _deliveryTimeSlot = null;
      _specialInstructions = null;
      _customAmountsPerService.clear();
      _customPricingModePerService.clear();
      _customWeightPerKg.clear();
      _customGarments.clear();
    });
    
    // Reset page controller to first step
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}

enum FulfillmentMode { pickupDelivery, inStore }
enum InStoreOption { customerPickup, deliveryToCustomer }

class OrderItemData {
  final String garmentId;
  final Garment garment;
  final int quantity;
  final double unitPrice;

  OrderItemData({
    required this.garmentId,
    required this.garment,
    required this.quantity,
    required this.unitPrice,
  });

  double get totalPrice => unitPrice * quantity;
}

class _DatePickerTile extends StatelessWidget {
  final String label;
  final DateTime? date;
  final VoidCallback onTap;
  const _DatePickerTile({required this.label, required this.date, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final text = date == null
        ? 'Select date'
        : '${date!.year}-${date!.month.toString().padLeft(2, '0')}-${date!.day.toString().padLeft(2, '0')}';
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(labelText: label, border: const OutlineInputBorder()),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, size: 18),
            const SizedBox(width: 8),
            Text(text),
          ],
        ),
      ),
    );
  }
}
