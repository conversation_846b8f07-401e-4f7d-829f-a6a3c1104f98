-- Laundry Orders System Database Schema
-- This script creates all tables needed for the comprehensive ordering system

-- ============================================
-- DROP STATEMENTS (in dependency order)
-- ============================================

-- Drop sequences (no dependencies)
DROP SEQUENCE IF EXISTS order_number_seq;
DROP SEQUENCE IF EXISTS invoice_number_seq;

-- Drop functions (no table dependencies)
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS generate_order_number() CASCADE;
DROP FUNCTION IF EXISTS generate_invoice_number() CASCADE;

-- Drop complex functions (with table dependencies)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'calculate_order_total') THEN
        DROP FUNCTION calculate_order_total(UUID);
    END IF;
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_orders_by_status') THEN
        DROP FUNCTION get_orders_by_status(TEXT);
    END IF;
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_user_orders') THEN
        DROP FUNCTION get_user_orders(UUID);
    END IF;
END $$;

-- Drop tables and their triggers (in dependency order)
DO $$ 
BEGIN
    -- Drop triggers first if tables exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        DROP TRIGGER IF EXISTS update_order_items_updated_at ON order_items;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN
        DROP TRIGGER IF EXISTS update_invoices_updated_at ON invoices;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_addresses') THEN
        DROP TRIGGER IF EXISTS update_user_addresses_updated_at ON user_addresses;
    END IF;

    -- Now drop tables in proper order
    DROP TABLE IF EXISTS order_status_log CASCADE;
    DROP TABLE IF EXISTS invoices CASCADE;
    DROP TABLE IF EXISTS order_items CASCADE;
    DROP TABLE IF EXISTS orders CASCADE;
    DROP TABLE IF EXISTS user_addresses CASCADE;
    DROP TABLE IF EXISTS garments CASCADE;
    DROP TABLE IF EXISTS services CASCADE;
END $$;

-- ============================================
-- EXTENSIONS AND DEPENDENCIES
-- ============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Ensure auth schema exists and is accessible
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'auth') THEN
        RAISE NOTICE 'Warning: auth schema does not exist. This migration assumes Supabase auth is set up.';
    END IF;
END $$;

-- Create or ensure the update_updated_at_column function exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================
-- CREATE SEQUENCES
-- ============================================

CREATE SEQUENCE order_number_seq START 100001;
CREATE SEQUENCE invoice_number_seq START 1;

-- ============================================
-- SERVICES TABLE
-- ============================================

CREATE TABLE services (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    price_per_kg DECIMAL(10,2),
    price_per_item DECIMAL(10,2),
    pricing_type TEXT CHECK (pricing_type IN ('per_kg', 'per_item', 'fixed')) DEFAULT 'per_kg',
    estimated_hours INTEGER DEFAULT 24,
    is_active BOOLEAN DEFAULT true NOT NULL,
    icon TEXT, -- Icon name for UI
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    CONSTRAINT unique_service_name UNIQUE (name)
);

-- Create indexes for services
CREATE INDEX idx_services_is_active ON services(is_active);
CREATE INDEX idx_services_pricing_type ON services(pricing_type);

-- ============================================
-- GARMENTS TABLE
-- ============================================

CREATE TABLE garments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL, -- 'clothing', 'household', 'special'
    description TEXT,
    icon TEXT, -- Icon name for UI
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    CONSTRAINT unique_garment_name UNIQUE (name)
);

-- Create indexes for garments
CREATE INDEX idx_garments_category ON garments(category);
CREATE INDEX idx_garments_is_active ON garments(is_active);

-- ============================================
-- USER ADDRESSES TABLE
-- ============================================

CREATE TABLE user_addresses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL, -- 'Home', 'Office', 'Other'
    address_line_1 TEXT NOT NULL,
    address_line_2 TEXT,
    city TEXT NOT NULL,
    state TEXT,
    postal_code TEXT,
    country TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes for user_addresses
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_addresses_is_default ON user_addresses(is_default);
CREATE INDEX idx_user_addresses_is_active ON user_addresses(is_active);

-- ============================================
-- ORDERS TABLE
-- ============================================

CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_number TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    store_id UUID, -- References stores(id) - will add constraint later if stores table exists
    service_id UUID REFERENCES services(id) ON DELETE SET NULL NOT NULL,
    
    -- Status management
    status TEXT CHECK (status IN (
        'pending', 'accepted', 'picked_up', 'in_process', 
        'ready_for_pickup', 'out_for_delivery', 'completed', 'cancelled'
    )) DEFAULT 'pending' NOT NULL,
    
    -- Address information
    pickup_address_id UUID REFERENCES user_addresses(id) ON DELETE SET NULL,
    delivery_address_id UUID REFERENCES user_addresses(id) ON DELETE SET NULL,
    
    -- Time slots
    pickup_date DATE,
    pickup_time_slot TEXT, -- '09:00-12:00', '12:00-15:00', etc.
    delivery_date DATE,
    delivery_time_slot TEXT,
    
    -- Pricing
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Payment options
    pay_on_delivery BOOLEAN DEFAULT false NOT NULL,
    
    -- Special instructions
    special_instructions TEXT,
    
    -- Assignment and cancellation
    assigned_agent_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    cancelled_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    cancel_reason TEXT,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    CONSTRAINT unique_order_number UNIQUE (order_number)
);

-- Create indexes for orders
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_store_id ON orders(store_id);
CREATE INDEX idx_orders_service_id ON orders(service_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_pickup_date ON orders(pickup_date);
CREATE INDEX idx_orders_delivery_date ON orders(delivery_date);
CREATE INDEX idx_orders_assigned_agent_id ON orders(assigned_agent_id);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- ============================================
-- ORDER ITEMS TABLE
-- ============================================

CREATE TABLE order_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    garment_id UUID REFERENCES garments(id) ON DELETE SET NULL NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    CONSTRAINT positive_quantity CHECK (quantity > 0),
    CONSTRAINT positive_unit_price CHECK (unit_price >= 0),
    CONSTRAINT positive_total_price CHECK (total_price >= 0)
);

-- Create indexes for order_items
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_garment_id ON order_items(garment_id);

-- ============================================
-- ORDER STATUS LOG TABLE
-- ============================================

CREATE TABLE order_status_log (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes for order_status_log
CREATE INDEX idx_order_status_log_order_id ON order_status_log(order_id);
CREATE INDEX idx_order_status_log_created_at ON order_status_log(created_at);

-- ============================================
-- INVOICES TABLE
-- ============================================

CREATE TABLE invoices (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    invoice_number TEXT NOT NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Invoice details
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Payment info
    payment_status TEXT CHECK (payment_status IN (
        'pending', 'paid', 'partial', 'refunded', 'failed'
    )) DEFAULT 'pending' NOT NULL,
    payment_method TEXT,
    paid_at TIMESTAMP WITH TIME ZONE,
    
    -- Invoice dates
    invoice_date DATE DEFAULT CURRENT_DATE NOT NULL,
    due_date DATE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    CONSTRAINT unique_invoice_number UNIQUE (invoice_number),
    CONSTRAINT unique_order_invoice UNIQUE (order_id)
);

-- Create indexes for invoices
CREATE INDEX idx_invoices_order_id ON invoices(order_id);
CREATE INDEX idx_invoices_user_id ON invoices(user_id);
CREATE INDEX idx_invoices_payment_status ON invoices(payment_status);
CREATE INDEX idx_invoices_invoice_date ON invoices(invoice_date);

-- ============================================
-- TRIGGER FUNCTIONS (simple functions that don't reference other tables)
-- ============================================

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER
SET search_path = 'public'
SECURITY DEFINER
AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := 'Order: ' || nextval('public.order_number_seq')::text;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TRIGGER
SET search_path = 'public'
SECURITY DEFINER
AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := 'INV-' || TO_CHAR(NOW(), 'YYYY') || '-' || LPAD(nextval('public.invoice_number_seq')::text, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- TRIGGERS
-- ============================================

DO $$ 
BEGIN
    -- Create triggers only if their tables exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        -- Order number generation trigger
        CREATE TRIGGER generate_order_number_trigger
            BEFORE INSERT ON orders
            FOR EACH ROW
            EXECUTE FUNCTION generate_order_number();

        -- Orders updated_at trigger
        CREATE TRIGGER update_orders_updated_at
            BEFORE UPDATE ON orders
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN
        -- Invoice number generation trigger
        CREATE TRIGGER generate_invoice_number_trigger
            BEFORE INSERT ON invoices
            FOR EACH ROW
            EXECUTE FUNCTION generate_invoice_number();

        -- Invoices updated_at trigger
        CREATE TRIGGER update_invoices_updated_at
            BEFORE UPDATE ON invoices
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        -- Order items updated_at trigger
        CREATE TRIGGER update_order_items_updated_at
            BEFORE UPDATE ON order_items
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_addresses') THEN
        -- User addresses updated_at trigger
        CREATE TRIGGER update_user_addresses_updated_at
            BEFORE UPDATE ON user_addresses
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- ============================================
-- ROW LEVEL SECURITY
-- ============================================

-- Enable RLS on all tables
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE garments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Services policies (viewable by all authenticated users)
CREATE POLICY "Services are viewable by anonymous users" ON services
    FOR SELECT TO anon USING (is_active = true);

-- Allow authenticated users to read all services (management)
CREATE POLICY "Services readable by authenticated (all)" ON services
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Services can be inserted by authenticated" ON services
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Services can be updated by authenticated" ON services
    FOR UPDATE TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Services can be deleted by authenticated" ON services
    FOR DELETE TO authenticated USING (true);

-- Garments policies (viewable by all authenticated users)
CREATE POLICY "Garments are viewable by anonymous users" ON garments
    FOR SELECT TO anon USING (is_active = true);

CREATE POLICY "Garments readable by authenticated (all)" ON garments
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Garments can be inserted by authenticated" ON garments
    FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Garments can be updated by authenticated" ON garments
    FOR UPDATE TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Garments can be deleted by authenticated" ON garments
    FOR DELETE TO authenticated USING (true);

-- User addresses policies (users can only see their own addresses)
CREATE POLICY "Users can view their own addresses" ON user_addresses
    FOR SELECT TO authenticated USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own addresses" ON user_addresses
    FOR INSERT TO authenticated WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own addresses" ON user_addresses
    FOR UPDATE TO authenticated USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own addresses" ON user_addresses
    FOR DELETE TO authenticated USING ((SELECT auth.uid()) = user_id);

-- Orders policies
CREATE POLICY "Authenticated users can view all orders" ON orders
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can create their own orders" ON orders
    FOR INSERT TO authenticated WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own orders" ON orders
    FOR UPDATE TO authenticated USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

-- Order items policies (via order ownership)
CREATE POLICY "Users can view their order items" ON order_items
    FOR SELECT TO authenticated USING (
        EXISTS (
            SELECT 1 FROM public.orders o 
            WHERE o.id = order_id AND o.user_id = (SELECT auth.uid())
        )
    );

CREATE POLICY "Users can insert their order items" ON order_items
    FOR INSERT TO authenticated WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.orders o 
            WHERE o.id = order_id AND o.user_id = (SELECT auth.uid())
        )
    );

-- Order status log policies (viewable by order owner)
CREATE POLICY "Users can view their order status log" ON order_status_log
    FOR SELECT TO authenticated USING (
        EXISTS (
            SELECT 1 FROM orders o WHERE o.id = order_id AND o.user_id = (SELECT auth.uid())
        )
    );

-- Allow users to insert status logs for their own orders
CREATE POLICY "Users can insert their order status log" ON order_status_log
    FOR INSERT TO authenticated WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders o WHERE o.id = order_id AND o.user_id = (SELECT auth.uid())
        )
    );

-- Allow users to update status logs for their own orders (for order management)
CREATE POLICY "Users can update their order status log" ON order_status_log
    FOR UPDATE TO authenticated USING (
        EXISTS (
            SELECT 1 FROM orders o WHERE o.id = order_id AND o.user_id = (SELECT auth.uid())
        )
    );

-- Invoices policies (users can manage their own invoices)
CREATE POLICY "Users can view their own invoices" ON invoices
    FOR SELECT TO authenticated USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own invoices" ON invoices
    FOR INSERT TO authenticated WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own invoices" ON invoices
    FOR UPDATE TO authenticated 
    USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own invoices" ON invoices
    FOR DELETE TO authenticated USING ((SELECT auth.uid()) = user_id);

-- ============================================
-- CONDITIONAL FOREIGN KEY CONSTRAINTS
-- ============================================

-- Add stores foreign key constraint if stores table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stores') THEN
        ALTER TABLE orders ADD CONSTRAINT fk_orders_store_id 
        FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE SET NULL;
        RAISE NOTICE 'Added foreign key constraint for stores table';
    ELSE
        RAISE NOTICE 'Stores table not found - skipping foreign key constraint';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not add stores foreign key constraint: %', SQLERRM;
END $$;

-- ============================================
-- COMPLEX FUNCTIONS (that reference tables)
-- ============================================

-- Function to calculate order total
CREATE OR REPLACE FUNCTION calculate_order_total(order_uuid UUID)
RETURNS DECIMAL(10,2)
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
    item_total DECIMAL(10,2) := 0;
    order_rec RECORD;
    final_total DECIMAL(10,2);
BEGIN
    -- Get sum of all order items
    SELECT COALESCE(SUM(total_price), 0) INTO item_total
    FROM public.order_items
    WHERE order_id = order_uuid;
    
    -- Get order details for additional charges
    SELECT subtotal, tax_amount, delivery_fee, discount_amount
    INTO order_rec
    FROM public.orders
    WHERE id = order_uuid;
    
    -- Calculate final total
    final_total := item_total + COALESCE(order_rec.tax_amount, 0) + 
                   COALESCE(order_rec.delivery_fee, 0) - COALESCE(order_rec.discount_amount, 0);
    
    RETURN final_total;
END;
$$ LANGUAGE plpgsql;

-- Function to get orders by status
CREATE OR REPLACE FUNCTION get_orders_by_status(order_status TEXT)
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    user_id UUID,
    store_id UUID,
    service_id UUID,
    status TEXT,
    total_amount DECIMAL(10,2),
    pickup_date DATE,
    delivery_date DATE,
    created_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT o.id, o.order_number, o.user_id, o.store_id, o.service_id, 
           o.status, o.total_amount, o.pickup_date, o.delivery_date, o.created_at
    FROM public.orders o
    WHERE o.status = order_status
    ORDER BY o.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get user orders
CREATE OR REPLACE FUNCTION get_user_orders(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    service_name TEXT,
    store_name TEXT,
    status TEXT,
    total_amount DECIMAL(10,2),
    pickup_date DATE,
    delivery_date DATE,
    created_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT o.id, o.order_number, s.name as service_name, 
           CASE 
               WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stores') 
               THEN st.name 
               ELSE 'Store Not Available'::TEXT 
           END as store_name,
           o.status, o.total_amount, o.pickup_date, o.delivery_date, o.created_at
    FROM public.orders o
    LEFT JOIN public.services s ON o.service_id = s.id
    LEFT JOIN public.stores st ON o.store_id = st.id AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stores')
    WHERE o.user_id = user_uuid
    ORDER BY o.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get all orders with optional store filtering
CREATE OR REPLACE FUNCTION get_all_orders(store_filter_id UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    user_id UUID,
    service_name TEXT,
    store_name TEXT,
    status TEXT,
    total_amount DECIMAL(10,2),
    pickup_date DATE,
    delivery_date DATE,
    created_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT o.id, o.order_number, o.user_id, s.name as service_name, 
           CASE 
               WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stores') 
               THEN st.name 
               ELSE 'Store Not Available'::TEXT 
           END as store_name,
           o.status, o.total_amount, o.pickup_date, o.delivery_date, o.created_at
    FROM public.orders o
    LEFT JOIN public.services s ON o.service_id = s.id
    LEFT JOIN public.stores st ON o.store_id = st.id AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stores')
    WHERE (store_filter_id IS NULL OR o.store_id = store_filter_id)
    ORDER BY o.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- SEED DATA
-- ============================================

-- Insert default services
INSERT INTO services (name, description, base_price, price_per_kg, pricing_type, estimated_hours, icon) VALUES
('Wash & Fold', 'Regular washing and folding service', 0.00, 5.00, 'per_kg', 24, 'local_laundry_service'),
('Dry Cleaning', 'Professional dry cleaning service', 0.00, 15.00, 'per_item', 48, 'dry_cleaning'),
('Ironing', 'Professional ironing and pressing', 0.00, 3.00, 'per_item', 12, 'iron'),
('Express Service', 'Same day wash and fold', 0.00, 8.00, 'per_kg', 8, 'flash_on'),
('Delicate Care', 'Special care for delicate items', 0.00, 20.00, 'per_item', 72, 'favorite');

-- Insert default garment types
INSERT INTO garments (name, category, description, icon) VALUES
-- Clothing
('Shirts', 'clothing', 'All types of shirts', 'checkroom'),
('Pants', 'clothing', 'Trousers, jeans, etc.', 'checkroom'),
('Dresses', 'clothing', 'All types of dresses', 'checkroom'),
('Suits', 'clothing', 'Business suits', 'business_center'),
('Jackets', 'clothing', 'Coats and jackets', 'checkroom'),
('Underwear', 'clothing', 'Undergarments', 'checkroom'),
('Socks', 'clothing', 'All types of socks', 'checkroom'),
-- Household
('Bed Sheets', 'household', 'Bed linens and sheets', 'bed'),
('Pillowcases', 'household', 'Pillow covers', 'bed'),
('Towels', 'household', 'Bath and hand towels', 'shower'),
('Curtains', 'household', 'Window curtains', 'window'),
('Blankets', 'household', 'Comforters and blankets', 'bed'),
-- Special
('Wedding Dress', 'special', 'Bridal gowns', 'favorite'),
('Leather Items', 'special', 'Leather garments and accessories', 'work'),
('Silk Items', 'special', 'Silk clothing and fabrics', 'auto_awesome');

-- ============================================
-- GRANT PERMISSIONS FOR SEQUENCES
-- ============================================

-- Grant necessary permissions for sequences and functions
GRANT USAGE, SELECT ON SEQUENCE order_number_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE invoice_number_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE order_number_seq TO anon;
GRANT USAGE, SELECT ON SEQUENCE invoice_number_seq TO anon;

-- Grant permissions on sequences to public (broader access)
GRANT USAGE, SELECT ON SEQUENCE order_number_seq TO public;
GRANT USAGE, SELECT ON SEQUENCE invoice_number_seq TO public;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION generate_order_number() TO authenticated;
GRANT EXECUTE ON FUNCTION generate_invoice_number() TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_order_total(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_orders_by_status(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_orders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_orders(UUID) TO authenticated;

-- Grant function permissions to public for trigger functions
GRANT EXECUTE ON FUNCTION generate_order_number() TO public;
GRANT EXECUTE ON FUNCTION generate_invoice_number() TO public;

-- ============================================
-- MIGRATION COMPLETE
-- ============================================

-- Add comments for documentation
COMMENT ON TABLE services IS 'Laundry service types with pricing information';
COMMENT ON TABLE garments IS 'Types of garments that can be ordered';
COMMENT ON TABLE user_addresses IS 'User addresses for pickup and delivery';
COMMENT ON TABLE orders IS 'Main orders table with complete order information';
COMMENT ON TABLE order_items IS 'Individual items within an order';
COMMENT ON TABLE order_status_log IS 'Status change history for orders';
COMMENT ON TABLE invoices IS 'Invoice generation and payment tracking';

-- Add comments to document the updated status flow
COMMENT ON COLUMN orders.status IS 'Order status flow: pending -> accepted -> picked_up (order collected from customer) -> in_process -> ready_for_pickup (laundry done) -> [CHOICE: customer pickup -> completed OR delivery -> out_for_delivery -> completed]';
