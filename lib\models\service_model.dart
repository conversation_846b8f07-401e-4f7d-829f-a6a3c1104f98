import 'package:flutter/foundation.dart';

@immutable
class Service {
  final String id;
  final String name;
  final String? description;
  final double basePrice;
  final double? pricePerKg;
  final double? pricePerItem;
  final String pricingType; // 'per_kg', 'per_item', 'fixed', 'custom'
  final int estimatedHours;
  final bool isActive;
  final String? icon;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Service({
    required this.id,
    required this.name,
    this.description,
    required this.basePrice,
    this.pricePerKg,
    this.pricePerItem,
    required this.pricingType,
    required this.estimatedHours,
    required this.isActive,
    this.icon,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      basePrice: (json['base_price'] as num).toDouble(),
      pricePerKg: json['price_per_kg'] != null ? (json['price_per_kg'] as num).toDouble() : null,
      pricePerItem: json['price_per_item'] != null ? (json['price_per_item'] as num).toDouble() : null,
      pricingType: json['pricing_type'] as String,
      estimatedHours: json['estimated_hours'] as int,
      isActive: json['is_active'] as bool,
      icon: json['icon'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'base_price': basePrice,
      'price_per_kg': pricePerKg,
      'price_per_item': pricePerItem,
      'pricing_type': pricingType,
      'estimated_hours': estimatedHours,
      'is_active': isActive,
      'icon': icon,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Calculate price based on pricing type
  double calculatePrice({int quantity = 1, double weight = 0.0, double? customAmount}) {
    switch (pricingType) {
      case 'per_kg':
        return basePrice + (pricePerKg ?? 0.0) * weight;
      case 'per_item':
        return basePrice + (pricePerItem ?? 0.0) * quantity;
      case 'fixed':
        return basePrice;
      case 'custom':
        return customAmount ?? basePrice;
      default:
        return basePrice;
    }
  }

  String get estimatedTimeText {
    if (estimatedHours < 24) {
      return '$estimatedHours hours';
    } else {
      final days = (estimatedHours / 24).round();
      return '$days ${days == 1 ? 'day' : 'days'}';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Service && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => '$name ($pricingType)';
}
