import 'package:flutter/material.dart';
import '../services/theme_service.dart';

/// Theme provider for managing theme state across the app
class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  /// Current theme mode
  ThemeMode get themeMode => _themeMode;
  
  /// Current theme mode as string for display
  String get themeModeString => ThemeService.getThemeModeString(_themeMode);
  
  /// Initialize theme provider by loading saved preference
  Future<void> initialize() async {
    _themeMode = await ThemeService.getThemeMode();
    notifyListeners();
  }
  
  /// Set theme mode and save to preferences
  Future<void> setThemeMode(ThemeMode themeMode) async {
    if (_themeMode == themeMode) return;
    
    _themeMode = themeMode;
    await ThemeService.setThemeMode(themeMode);
    notifyListeners();
  }
  
  /// Set theme mode from string
  Future<void> setThemeModeFromString(String themeString) async {
    final themeMode = ThemeService.getThemeModeFromString(themeString);
    await setThemeMode(themeMode);
  }
  
  /// Toggle between light and dark theme (ignores system)
  Future<void> toggleTheme() async {
    final newThemeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    await setThemeMode(newThemeMode);
  }
  
  /// Check if current theme is dark mode
  bool get isDarkMode {
    return _themeMode == ThemeMode.dark;
  }
  
  /// Check if current theme is light mode
  bool get isLightMode {
    return _themeMode == ThemeMode.light;
  }
  
  /// Check if current theme is system mode
  bool get isSystemMode {
    return _themeMode == ThemeMode.system;
  }
}
