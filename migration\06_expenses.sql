-- ============================================
-- EXPENSES FEATURE WITH CUSTOM CATEGORIES (idempotent, with DROPs)
-- ============================================

-- SAFETY: Drop dependent triggers/functions/policies first
DO $$
BEGIN
  -- Drop custom category triggers if table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'custom_expense_categories') THEN
    DROP TRIGGER IF EXISTS trg_custom_expense_categories_updated_at ON public.custom_expense_categories;
  END IF;

  -- Drop allocation triggers if tables exist
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expense_store_allocations') THEN
    DROP TRIGGER IF EXISTS trg_expense_allocations_updated_at ON public.expense_store_allocations;
    DROP TRIGGER IF EXISTS trg_enforce_expense_allocations_total_ins ON public.expense_store_allocations;
    DROP TRIGGER IF EXISTS trg_enforce_expense_allocations_total_upd ON public.expense_store_allocations;
    DROP TRIGGER IF EXISTS trg_enforce_expense_allocations_total_del ON public.expense_store_allocations;
  END IF;

  -- Drop expense triggers if table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses') THEN
    DROP TRIGGER IF EXISTS trg_expenses_updated_at ON public.expenses;
  END IF;

  -- Drop RLS policies (idempotent and guarded by table existence)
  IF to_regclass('public.expenses') IS NOT NULL THEN
    DROP POLICY IF EXISTS expenses_select_authenticated ON public.expenses;
    DROP POLICY IF EXISTS expenses_insert_own ON public.expenses;
    DROP POLICY IF EXISTS expenses_update_own ON public.expenses;
    DROP POLICY IF EXISTS expenses_delete_own ON public.expenses;
  END IF;

  IF to_regclass('public.expense_store_allocations') IS NOT NULL THEN
    DROP POLICY IF EXISTS expense_allocations_select_authenticated ON public.expense_store_allocations;
    DROP POLICY IF EXISTS expense_allocations_ins_own ON public.expense_store_allocations;
    DROP POLICY IF EXISTS expense_allocations_upd_own ON public.expense_store_allocations;
    DROP POLICY IF EXISTS expense_allocations_del_own ON public.expense_store_allocations;
  END IF;

  -- Drop custom categories RLS policies
  IF to_regclass('public.custom_expense_categories') IS NOT NULL THEN
    DROP POLICY IF EXISTS custom_categories_select_authenticated ON public.custom_expense_categories;
    DROP POLICY IF EXISTS custom_categories_insert_own ON public.custom_expense_categories;
    DROP POLICY IF EXISTS custom_categories_update_own ON public.custom_expense_categories;
    DROP POLICY IF EXISTS custom_categories_delete_own ON public.custom_expense_categories;
  END IF;

  -- Drop helper functions
  DROP FUNCTION IF EXISTS public.enforce_expense_allocations_total();
END $$;

-- Drop tables (in dependency order)
DROP TABLE IF EXISTS public.expense_store_allocations CASCADE;
DROP TABLE IF EXISTS public.expenses CASCADE;
DROP TABLE IF EXISTS public.custom_expense_categories CASCADE;

-- ============================================
-- TABLE: custom_expense_categories
-- ============================================
CREATE TABLE public.custom_expense_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  icon_name TEXT, -- Material Design icon name (e.g., 'home', 'flash_on')
  color_hex TEXT, -- Hex color code (e.g., '#FF5722')
  is_active BOOLEAN NOT NULL DEFAULT true,
  
  -- Audit
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  
  -- Constraints
  CONSTRAINT unique_category_name_per_user UNIQUE (name, created_by),
  CONSTRAINT valid_color_hex CHECK (color_hex ~ '^#[0-9A-Fa-f]{6}$')
);

-- Indexes for custom categories
CREATE INDEX IF NOT EXISTS idx_custom_categories_name ON public.custom_expense_categories(name);
CREATE INDEX IF NOT EXISTS idx_custom_categories_created_by ON public.custom_expense_categories(created_by);
CREATE INDEX IF NOT EXISTS idx_custom_categories_active ON public.custom_expense_categories(is_active);

-- Updated-at trigger for custom categories
CREATE TRIGGER trg_custom_expense_categories_updated_at
  BEFORE UPDATE ON public.custom_expense_categories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- TABLE: expenses (single or multi-store via allocations)
-- ============================================
CREATE TABLE public.expenses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category TEXT CHECK (category IN (
    'rent','utilities','salaries','supplies','transport','maintenance','marketing','refund','misc'
  )),
  custom_category_id UUID REFERENCES public.custom_expense_categories(id) ON DELETE SET NULL,
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  expense_date DATE NOT NULL DEFAULT CURRENT_DATE,
  note TEXT,
  vendor TEXT,
  reference TEXT,
  payment_method TEXT NOT NULL CHECK (payment_method IN (
    'cash','mpesa_till','mpesa_paybill','mpesa_send_money'
  )),
  -- Single store mode: store_id not null
  -- Multi-store mode: store_id null and allocations used
  store_id UUID REFERENCES public.stores(id) ON DELETE SET NULL,

  -- Audit
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  
  -- Constraint: either category OR custom_category_id must be set, but not both
  CONSTRAINT check_category_exclusive CHECK (
    (category IS NOT NULL AND custom_category_id IS NULL) OR
    (category IS NULL AND custom_category_id IS NOT NULL)
  )
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_expenses_date ON public.expenses(expense_date);
CREATE INDEX IF NOT EXISTS idx_expenses_category ON public.expenses(category);
CREATE INDEX IF NOT EXISTS idx_expenses_custom_category_id ON public.expenses(custom_category_id);
CREATE INDEX IF NOT EXISTS idx_expenses_method ON public.expenses(payment_method);
CREATE INDEX IF NOT EXISTS idx_expenses_store_id ON public.expenses(store_id);
CREATE INDEX IF NOT EXISTS idx_expenses_created_by ON public.expenses(created_by);
CREATE INDEX IF NOT EXISTS idx_expenses_created_at ON public.expenses(created_at);

-- Updated-at trigger
CREATE TRIGGER trg_expenses_updated_at
  BEFORE UPDATE ON public.expenses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- TABLE: expense_store_allocations (multi-store split)
-- ============================================
CREATE TABLE public.expense_store_allocations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  expense_id UUID NOT NULL REFERENCES public.expenses(id) ON DELETE CASCADE,
  store_id UUID NOT NULL REFERENCES public.stores(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT TIMEZONE('utc'::text, NOW()),
  UNIQUE(expense_id, store_id)
);

CREATE INDEX IF NOT EXISTS idx_expense_allocations_expense_id ON public.expense_store_allocations(expense_id);
CREATE INDEX IF NOT EXISTS idx_expense_allocations_store_id ON public.expense_store_allocations(store_id);

-- Updated-at trigger for allocations (piggybacks on expense updated_at when we auto-null store_id)
CREATE TRIGGER trg_expense_allocations_updated_at
  BEFORE UPDATE ON public.expense_store_allocations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Helper to enforce allocation sum = expense.amount and null-out store_id when allocations exist
-- ============================================
CREATE OR REPLACE FUNCTION public.enforce_expense_allocations_total()
RETURNS TRIGGER
SET search_path = ''
SECURITY DEFINER
AS $$
DECLARE
  v_expense_id UUID;
  v_total DECIMAL(10,2);
  v_amount DECIMAL(10,2);
BEGIN
  v_expense_id := COALESCE(NEW.expense_id, OLD.expense_id);

  SELECT amount INTO v_amount FROM public.expenses WHERE id = v_expense_id;

  SELECT COALESCE(SUM(amount), 0) INTO v_total
  FROM public.expense_store_allocations
  WHERE expense_id = v_expense_id;

  -- If any allocations exist, their sum must exactly equal the expense amount
  IF v_total > 0 AND v_amount IS NOT NULL AND v_total <> v_amount THEN
    RAISE EXCEPTION 'Allocation total (%.2f) must equal expense amount (%.2f) for expense %', v_total, v_amount, v_expense_id
    USING ERRCODE = 'check_violation';
  END IF;

  -- If allocations exist, ensure expenses.store_id is NULL (multi-store mode)
  IF v_total > 0 THEN
    UPDATE public.expenses
    SET store_id = NULL, updated_at = TIMEZONE('utc'::text, NOW())
    WHERE id = v_expense_id AND store_id IS NOT NULL;
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers on allocations to enforce rules
CREATE TRIGGER trg_enforce_expense_allocations_total_ins
  AFTER INSERT ON public.expense_store_allocations
  FOR EACH ROW EXECUTE FUNCTION public.enforce_expense_allocations_total();

CREATE TRIGGER trg_enforce_expense_allocations_total_upd
  AFTER UPDATE ON public.expense_store_allocations
  FOR EACH ROW EXECUTE FUNCTION public.enforce_expense_allocations_total();

CREATE TRIGGER trg_enforce_expense_allocations_total_del
  AFTER DELETE ON public.expense_store_allocations
  FOR EACH ROW EXECUTE FUNCTION public.enforce_expense_allocations_total();

-- ============================================
-- RLS (match payments style: broad read, own writes)
-- ============================================
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_store_allocations ENABLE ROW LEVEL SECURITY;

-- Expenses policies
CREATE POLICY expenses_select_authenticated
ON public.expenses
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY expenses_insert_own
ON public.expenses
FOR INSERT
TO authenticated
WITH CHECK (created_by = (SELECT auth.uid()));

CREATE POLICY expenses_update_own
ON public.expenses
FOR UPDATE
TO authenticated
USING (created_by = (SELECT auth.uid()))
WITH CHECK (created_by = (SELECT auth.uid()));

CREATE POLICY expenses_delete_own
ON public.expenses
FOR DELETE
TO authenticated
USING (created_by = (SELECT auth.uid()));

-- Allocations policies (write access tied to owning expense creator)
CREATE POLICY expense_allocations_select_authenticated
ON public.expense_store_allocations
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY expense_allocations_ins_own
ON public.expense_store_allocations
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.expenses e
    WHERE e.id = expense_id AND e.created_by = (SELECT auth.uid())
  )
);

-- Grants matching app style
GRANT ALL ON TABLE public.expenses TO authenticated;
GRANT ALL ON TABLE public.expense_store_allocations TO authenticated;

CREATE POLICY expense_allocations_upd_own
ON public.expense_store_allocations
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.expenses e
    WHERE e.id = expense_id AND e.created_by = (SELECT auth.uid())
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.expenses e
    WHERE e.id = expense_id AND e.created_by = (SELECT auth.uid())
  )
);

CREATE POLICY expense_allocations_del_own
ON public.expense_store_allocations
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.expenses e
    WHERE e.id = expense_id AND e.created_by = (SELECT auth.uid())
  )
);

-- ============================================
-- RLS for custom expense categories
-- ============================================
ALTER TABLE public.custom_expense_categories ENABLE ROW LEVEL SECURITY;

-- Custom categories policies
CREATE POLICY custom_categories_select_authenticated
ON public.custom_expense_categories
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY custom_categories_insert_own
ON public.custom_expense_categories
FOR INSERT
TO authenticated
WITH CHECK (created_by = (SELECT auth.uid()));

CREATE POLICY custom_categories_update_own
ON public.custom_expense_categories
FOR UPDATE
TO authenticated
USING (created_by = (SELECT auth.uid()))
WITH CHECK (created_by = (SELECT auth.uid()));

CREATE POLICY custom_categories_delete_own
ON public.custom_expense_categories
FOR DELETE
TO authenticated
USING (created_by = (SELECT auth.uid()));

-- ============================================
-- GRANTS
-- ============================================
GRANT ALL ON TABLE public.custom_expense_categories TO authenticated;