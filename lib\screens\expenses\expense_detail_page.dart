import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/expense_model.dart';
import '../../services/expense_service.dart';
import '../../services/store_service.dart';
import 'expense_form_page.dart';

class ExpenseDetailPage extends StatefulWidget {
  final String expenseId;
  const ExpenseDetailPage({super.key, required this.expenseId});

  @override
  State<ExpenseDetailPage> createState() => _ExpenseDetailPageState();
}

class _ExpenseDetailPageState extends State<ExpenseDetailPage> {
  final ExpenseService _service = ExpenseService(supabase: Supabase.instance.client);
  final StoreService _storeService = StoreService(supabase: Supabase.instance.client);
  Expense? _expense;
  bool _loading = true;
  String? _error;
  String? _storeName;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() {
      _loading = true;
      _error = null;
    });
    try {
      final e = await _service.getExpense(widget.expenseId);
      String? storeName;
      if (e.storeId != null && e.storeId!.isNotEmpty) {
        final store = await _storeService.getStoreById(e.storeId!);
        storeName = store?.name ?? store?.id; // display friendly name
      }
      if (!mounted) return;
      setState(() {
        _expense = e;
        _storeName = storeName;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _loading = false;
      });
    }
  }

  Future<void> _deleteExpense() async {
    try {
      await _service.deleteExpense(_expense!.id);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting expense: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expense Detail', style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          IconButton(onPressed: _load, icon: const Icon(Icons.refresh)),
          if (_expense != null)
            IconButton(
              tooltip: 'Edit',
              onPressed: () async {
                final updated = await Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => ExpenseFormPage(initial: _expense!)),
                );
                if (updated != null) {
                  _load();
                }
              },
              icon: const Icon(Icons.edit),
            ),
          if (_expense != null)
            IconButton(
              tooltip: 'Delete',
              onPressed: () async {
                final ok = await showDialog<bool>(
                  context: context,
                  builder: (dialogContext) => AlertDialog(
                    title: const Text('Delete Expense?'),
                    content: const Text('This action cannot be undone.'),
                    actions: [
                      TextButton(onPressed: () => Navigator.pop(dialogContext, false), child: const Text('Cancel')),
                      ElevatedButton(onPressed: () => Navigator.pop(dialogContext, true), child: const Text('Delete')),
                    ],
                  ),
                );
                if (ok == true) {
                  _deleteExpense();
                }
              },
              icon: const Icon(Icons.delete_outline),
            ),
        ],
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _expense == null
                  ? const Center(child: Text('Expense not found'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(),
                          const SizedBox(height: 16),
                          _buildMeta(),
                          const SizedBox(height: 16),
                          if (_expense!.hasAllocations) _buildAllocations(),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildHeader() {
    final e = _expense!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.06), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(color: Colors.blue.withValues(alpha: 0.1), shape: BoxShape.circle),
            child: const Icon(Icons.money_off, color: Colors.blue),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(e.category.displayName, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700)),
                Text(e.paymentMethod.displayName, style: TextStyle(color: Colors.grey[600])),
              ],
            ),
          ),
          Text(e.amount.toStringAsFixed(2), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
        ],
      ),
    );
  }

  Widget _buildMeta() {
    final e = _expense!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [
        BoxShadow(color: Colors.black.withValues(alpha: 0.06), blurRadius: 8, offset: const Offset(0, 2)),
      ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _row('Date', e.expenseDate.toIso8601String().split('T').first),
          _row('Vendor', e.vendor ?? '—'),
          _row('Reference', e.reference ?? '—'),
          _row('Store', _storeName ?? (e.hasAllocations ? 'Multiple stores' : '—')),
          const Divider(height: 24),
          if (e.note != null && e.note!.isNotEmpty) Text(e.note!),
        ],
      ),
    );
  }

  Widget _buildAllocations() {
    final e = _expense!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(12), boxShadow: [
        BoxShadow(color: Colors.black.withValues(alpha: 0.06), blurRadius: 8, offset: const Offset(0, 2)),
      ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Allocations', style: TextStyle(fontWeight: FontWeight.w700)),
          const SizedBox(height: 8),
          ...e.allocations!.map((a) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: Row(
                  children: [
                    Expanded(child: Text('Store: ${a.storeId}')),
                    Text(a.amount.toStringAsFixed(2), style: const TextStyle(fontWeight: FontWeight.w600)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _row(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(width: 120, child: Text(label, style: TextStyle(color: Colors.grey[700]))),
          Expanded(child: Text(value, style: const TextStyle(fontWeight: FontWeight.w600))),
        ],
      ),
    );
  }
}


