import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/garment_model.dart';
import '../../services/garment_service.dart';
import '../../services/category_service.dart';
import 'garment_detail_page.dart';

class GarmentsPage extends StatefulWidget {
  const GarmentsPage({super.key});

  @override
  State<GarmentsPage> createState() => _GarmentsPageState();
}

class _GarmentsPageState extends State<GarmentsPage> {
  final _svc = GarmentService(supabase: Supabase.instance.client);
  final _categorySvc = CategoryService(supabase: Supabase.instance.client);
  final _search = TextEditingController();
  String _query = '';
  String? _selectedCategory;
  
  // Available categories - fetched from database
  List<String> _categories = ['all'];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _search.dispose();
    super.dispose();
  }

  Future<List<Garment>> _load() async {
    List<Garment> garments;
    if (_query.isEmpty) {
      garments = await _svc.getAll();
    } else {
      garments = await _svc.search(_query);
    }
    
    // Filter by category if selected
    if (_selectedCategory != null && _selectedCategory != 'all') {
      garments = garments.where((g) => g.category == _selectedCategory).toList();
    }
    
    return garments;
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _categorySvc.getAllCategories();
      setState(() {
        _categories = ['all', ...categories];
      });
    } catch (e) {
      debugPrint('Error loading categories: $e');
      setState(() {
        _categories = ['all', 'clothing', 'household', 'special'];
      });
    }
  }

  Future<void> _refresh() async {
    setState(() {});
    await _loadCategories();
    await Future<void>.delayed(const Duration(milliseconds: 150));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Items'),
        actions: [
          IconButton(
            tooltip: 'Manage Categories',
            icon: const Icon(Icons.category),
            onPressed: _manageCategoriesDialog,
          ),
          IconButton(
            tooltip: 'Refresh',
            icon: const Icon(Icons.refresh),
            onPressed: _refresh,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              final res = await Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const GarmentDetailPage()),
              );
              if (!context.mounted) return;
              if (res is Map && res['ok'] == true) {
                final action = (res['action'] ?? 'saved') as String;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Item $action successfully'),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
              setState(() {});
            },
          )
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                TextField(
                  controller: _search,
                  decoration: InputDecoration(
                    hintText: 'Search items...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _query.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _query = '';
                                _search.clear();
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  onChanged: (v) => setState(() => _query = v.trim()),
                ),
                const SizedBox(height: 12),
                // Category Filter
                Row(
                  children: [
                    const Icon(Icons.filter_list, color: Colors.grey),
                    const SizedBox(width: 8),
                    const Text(
                      'Category:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedCategory,
                          hint: const Text('All Categories'),
                          isExpanded: true,
                          items: _categories.map((category) {
                            return DropdownMenuItem<String>(
                              value: category == 'all' ? null : category,
                              child: Row(
                                children: [
                                  Icon(
                                    _iconForCategory(category),
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(_categoryDisplayName(category)),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          },
                        ),
                      ),
                    ),
                    if (_selectedCategory != null)
                      IconButton(
                        icon: const Icon(Icons.clear, size: 18),
                        onPressed: () {
                          setState(() {
                            _selectedCategory = null;
                          });
                        },
                        tooltip: 'Clear filter',
                      ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<Garment>>(
              future: _load(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                final items = snapshot.data!;
                if (items.isEmpty) {
                  return RefreshIndicator(
                    onRefresh: _refresh,
                    child: ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: const [
                        SizedBox(height: 120),
                        Center(child: Text('No items found')),
                      ],
                    ),
                  );
                }
                return RefreshIndicator(
                  onRefresh: _refresh,
                  child: ListView.separated(
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemCount: items.length,
                    separatorBuilder: (_, __) => const Divider(height: 0),
                    itemBuilder: (context, i) {
                      final g = items[i];
                      return ListTile(
                        leading: CircleAvatar(
                          child: Icon(_iconFor(g.icon)),
                        ),
                        title: Text(g.name),
                        subtitle: Text(_categoryText(g.category)),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () async {
                                final res = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => GarmentDetailPage(garment: g),
                                  ),
                                );
                                if (!context.mounted) return;
                                if (res is Map && res['ok'] == true) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Item updated successfully'),
                                      backgroundColor: Colors.green,
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                }
                                setState(() {});
                              },
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () async {
                                final ok = await showDialog<bool>(
                                  context: context,
                                  builder: (_) => AlertDialog(
                                    title: const Text('Delete item?'),
                                    content: Text('Are you sure you want to delete "${g.name}"?'),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: const Text('Cancel'),
                                      ),
                                      ElevatedButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        child: const Text('Delete'),
                                      ),
                                    ],
                                  ),
                                );
                                if (ok == true) {
                                  try {
                                    await _svc.delete(g.id);
                                    if (!context.mounted) return;
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Item deleted'),
                                        backgroundColor: Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                    setState(() {});
                                  } catch (e) {
                                    if (!context.mounted) return;
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('Delete failed: $e'),
                                        backgroundColor: Colors.red,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                }
                              },
                            ),
                          ],
                        ),
                        onTap: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => GarmentDetailPage(garment: g, readOnly: true),
                            ),
                          );
                        },
                      );
                    },
                  ),
                );
              },
            ),
          )
        ],
      ),
    );
  }

  String _categoryText(String category) {
    switch (category) {
      case 'clothing':
        return 'Clothing';
      case 'household':
        return 'Household Items';
      case 'special':
        return 'Special Care';
      default:
        return 'Other';
    }
  }

  IconData _iconFor(String? iconName) {
    switch (iconName) {
      case 'checkroom':
        return Icons.checkroom;
      case 'business_center':
        return Icons.business_center;
      case 'bed':
        return Icons.bed;
      case 'shower':
        return Icons.shower;
      case 'window':
        return Icons.window;
      case 'work':
        return Icons.work;
      case 'auto_awesome':
        return Icons.auto_awesome;
      default:
        return Icons.checkroom;
    }
  }

  IconData _iconForCategory(String category) {
    switch (category) {
      case 'all':
        return Icons.all_inclusive;
      case 'clothing':
        return Icons.checkroom;
      case 'household':
        return Icons.home;
      case 'special':
        return Icons.auto_awesome;
      case 'custom':
        return Icons.star;
      default:
        return Icons.category;
    }
  }

  String _categoryDisplayName(String category) {
    if (category == 'all') {
      return 'All Categories';
    }
    return _categorySvc.getCategoryDisplayName(category);
  }

  Future<void> _manageCategoriesDialog() async {
    final result = await showDialog<String>(
      context: context,
      builder: (ctx) => _CategoryManagementDialog(
        categories: _categories.where((c) => c != 'all').toList(),
        categoryService: _categorySvc,
        onCategoryAdded: (category) async {
          // Category is added when creating garments with new category
          await _loadCategories();
        },
        onCategoryRenamed: (oldName, newName) async {
          final success = await _categorySvc.renameCategory(oldName, newName);
          if (success) {
            await _loadCategories();
            if (_selectedCategory == oldName) {
              setState(() {
                _selectedCategory = newName;
              });
            }
          }
          return success;
        },
        onCategoryDeleted: (category) async {
          final success = await _categorySvc.deleteCategory(category);
          if (success) {
            await _loadCategories();
            if (_selectedCategory == category) {
              setState(() {
                _selectedCategory = null;
              });
            }
          }
          return success;
        },
      ),
    );

    if (result != null) {
      setState(() {});
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

class _CategoryManagementDialog extends StatefulWidget {
  final List<String> categories;
  final CategoryService categoryService;
  final Future<void> Function(String) onCategoryAdded;
  final Future<bool> Function(String, String) onCategoryRenamed;
  final Future<bool> Function(String) onCategoryDeleted;

  const _CategoryManagementDialog({
    required this.categories,
    required this.categoryService,
    required this.onCategoryAdded,
    required this.onCategoryRenamed,
    required this.onCategoryDeleted,
  });

  @override
  State<_CategoryManagementDialog> createState() => _CategoryManagementDialogState();
}

class _CategoryManagementDialogState extends State<_CategoryManagementDialog> {
  final _newCategoryController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  Map<String, int> _categoryStats = {};
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _loadCategoryStats();
  }

  @override
  void dispose() {
    _newCategoryController.dispose();
    super.dispose();
  }

  Future<void> _loadCategoryStats() async {
    try {
      final stats = await widget.categoryService.getCategoryStats();
      setState(() {
        _categoryStats = stats;
      });
    } catch (e) {
      debugPrint('Error loading category stats: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.category,
                    color: Colors.blue[700],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Manage Categories',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    padding: const EdgeInsets.all(8),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Add New Category
            Form(
              key: _formKey,
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _newCategoryController,
                      decoration: InputDecoration(
                        labelText: 'New Category',
                        hintText: 'Enter category name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.add),
                      ),
                      validator: (value) {
                        return widget.categoryService.validateCategoryName(value ?? '');
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _loading ? null : _addCategory,
                    icon: _loading 
                        ? const SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.add, size: 18),
                    label: Text(_loading ? 'Adding...' : 'Add'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Categories List
            const Text(
              'Existing Categories',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),

            Flexible(
              child: widget.categories.isEmpty
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: Text(
                          'No custom categories yet.\nAdd one above to get started.',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: widget.categories.length,
                      itemBuilder: (context, index) {
                        final category = widget.categories[index];
                        final isBuiltIn = widget.categoryService.isBuiltInCategory(category);
                        final itemCount = _categoryStats[category] ?? 0;
                        
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: isBuiltIn ? Colors.blue[50] : Colors.orange[50],
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Icon(
                                _iconForCategory(category),
                                size: 16,
                                color: isBuiltIn ? Colors.blue[700] : Colors.orange[700],
                              ),
                            ),
                            title: Text(
                              widget.categoryService.getCategoryDisplayName(category),
                              style: const TextStyle(fontWeight: FontWeight.w500),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isBuiltIn ? 'Built-in category' : 'Custom category',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                Text(
                                  '$itemCount item${itemCount != 1 ? 's' : ''}',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.grey[500],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            trailing: isBuiltIn
                                ? null
                                : Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.edit, size: 18),
                                        onPressed: _loading ? null : () => _editCategory(category),
                                        tooltip: 'Edit category',
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                                        onPressed: _loading ? null : () => _deleteCategory(category),
                                        tooltip: 'Delete category',
                                      ),
                                    ],
                                  ),
                          ),
                        );
                      },
                    ),
            ),

            const SizedBox(height: 16),

            // Close Button
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Done'),
            ),
          ],
        ),
      ),
    );
  }

  IconData _iconForCategory(String category) {
    switch (category) {
      case 'clothing':
        return Icons.checkroom;
      case 'household':
        return Icons.home;
      case 'special':
        return Icons.auto_awesome;
      case 'custom':
        return Icons.star;
      default:
        return Icons.category;
    }
  }

  void _addCategory() async {
    if (_formKey.currentState?.validate() == true) {
      setState(() {
        _loading = true;
      });

      try {
        final categoryName = _newCategoryController.text.trim();
        final categoryKey = widget.categoryService.displayNameToKey(categoryName);
        
        await widget.onCategoryAdded(categoryKey);
        _newCategoryController.clear();
        
        if (mounted) {
          Navigator.of(context).pop('Category "$categoryName" will appear when items are added to it');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error adding category: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _loading = false;
          });
        }
      }
    }
  }

  void _editCategory(String category) async {
    final controller = TextEditingController(text: widget.categoryService.getCategoryDisplayName(category));
    
    final result = await showDialog<String>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Edit Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Category Name',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 8),
            Text(
              'This will rename the category for all ${_categoryStats[category] ?? 0} items.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newName = controller.text.trim();
              final validation = widget.categoryService.validateCategoryName(newName, excludeCategory: category);
              if (validation == null) {
                final newKey = widget.categoryService.displayNameToKey(newName);
                Navigator.of(ctx).pop(newKey);
              } else {
                ScaffoldMessenger.of(ctx).showSnackBar(
                  SnackBar(content: Text(validation), backgroundColor: Colors.red),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null && result != category) {
      setState(() {
        _loading = true;
      });

      try {
        final success = await widget.onCategoryRenamed(category, result);
        if (success && mounted) {
          Navigator.of(context).pop('Category renamed successfully');
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to rename category'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _loading = false;
          });
        }
      }
    }
  }

  void _deleteCategory(String category) async {
    final itemCount = _categoryStats[category] ?? 0;
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Delete Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete the "${widget.categoryService.getCategoryDisplayName(category)}" category?'),
            const SizedBox(height: 8),
            if (itemCount > 0)
              Text(
                'This will move $itemCount item${itemCount != 1 ? 's' : ''} to the "Other" category.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _loading = true;
      });

      try {
        final success = await widget.onCategoryDeleted(category);
        if (success && mounted) {
          Navigator.of(context).pop('Category deleted successfully');
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete category'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _loading = false;
          });
        }
      }
    }
  }
}
