import 'package:shared_preferences/shared_preferences.dart';
import 'package:currency_picker/currency_picker.dart';

class AppCurrencyService {
  static const String _currencyCodeKey = 'selected_currency_code';
  static const String _currencySymbolKey = 'selected_currency_symbol';
  static const String _currencyNameKey = 'selected_currency_name';
  
  // Default currency
  static const String _defaultCurrencyCode = 'USD';
  static const String _defaultCurrencySymbol = '\$';
  static const String _defaultCurrencyName = 'US Dollar';

  /// Get the currently selected currency
  Future<Currency> getSelectedCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    
    final currencyCode = prefs.getString(_currencyCodeKey) ?? _defaultCurrencyCode;
    final currencySymbol = prefs.getString(_currencySymbolKey) ?? _defaultCurrencySymbol;
    final currencyName = prefs.getString(_currencyNameKey) ?? _defaultCurrencyName;
    
    return Currency(
      code: currencyCode,
      symbol: currencySymbol,
      name: currencyName,
      flag: currencyCode == 'USD' ? '🇺🇸' : '🏳️',
      number: 840, // ISO 4217 number for USD, can be made dynamic later
      namePlural: currencyName,
      symbolOnLeft: true,
      decimalDigits: 2,
      decimalSeparator: '.',
      thousandsSeparator: ',',
      spaceBetweenAmountAndSymbol: false,
    );
  }

  /// Save the selected currency
  Future<void> saveSelectedCurrency(Currency currency) async {
    final prefs = await SharedPreferences.getInstance();
    
    await Future.wait([
      prefs.setString(_currencyCodeKey, currency.code),
      prefs.setString(_currencySymbolKey, currency.symbol),
      prefs.setString(_currencyNameKey, currency.name),
    ]);
  }

  /// Get the currency symbol
  Future<String> getCurrencySymbol() async {
    final currency = await getSelectedCurrency();
    return currency.symbol;
  }

  /// Get the currency code
  Future<String> getCurrencyCode() async {
    final currency = await getSelectedCurrency();
    return currency.code;
  }

  /// Format amount with currency symbol
  Future<String> formatAmount(double amount) async {
    final currency = await getSelectedCurrency();
    return '${currency.symbol}${amount.toStringAsFixed(2)}';
  }

  /// Reset to default currency
  Future<void> resetToDefault() async {
    final defaultCurrency = Currency(
      code: _defaultCurrencyCode,
      symbol: _defaultCurrencySymbol,
      name: _defaultCurrencyName,
      flag: '🇺🇸',
      number: 840,
      namePlural: _defaultCurrencyName,
      symbolOnLeft: true,
      decimalDigits: 2,
      decimalSeparator: '.',
      thousandsSeparator: ',',
      spaceBetweenAmountAndSymbol: false,
    );
    
    await saveSelectedCurrency(defaultCurrency);
  }
  /// Return an ordered list of currency codes with the currently active
  /// currency code placed first (no duplicates). Useful for pinning the
  /// active currency at the top of pickers.
  Future<List<String>> activeFirstList(List<String> baseCodes) async {
    final active = (await getSelectedCurrency()).code;
    final seen = <String>{};
    final ordered = <String>[];
    void addIfNew(String code) {
      if (code.isEmpty) return;
      final up = code.toUpperCase();
      if (seen.add(up)) ordered.add(up);
    }
    addIfNew(active);
    for (final c in baseCodes) {
      addIfNew(c);
    }
    return ordered;
  }
}
